@@require(PLATFORM, VERSION, chip)
<!DOCTYPE HTML>
<html>

<head>
	<title>Welcome to your ExpressLRS System</title>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<link rel="stylesheet" href="elrs.css" />
</head>

<body>
	<header class="mui-appbar mui--z1 mui--text-center elrs-header">
		@@include("logo-template.svg")
		<h1><b>ExpressLRS</b></h1>
		<span id="product_name"></span><br/>
		<b>Firmware Rev. </b>@@{VERSION} <span id="reg_domain"></span>
	</header>
	<br>
	<div class="mui-container-fluid mui-col-sm-10 mui-col-sm-offset-1">
		<div class="mui-panel">
			This page puts the Semtech LoRa chip into a mode where it transmits a continuous wave with a center frequency of <span id="frequency"></span> MHz.
			<br>
			You can then measure the actual continuous wave center frequency using a spectrum analyzer and enter the measured value below
			and this will be used to calculate the accuracy of the crystal used in the device and how far it differs from the ideal frequency.
			<form>
				<div id="radioOption" class="mui-radio" style="display: none;">
					<label>
						<input type="radio"
							name="optionsRadios"
							id="optionsRadios1"
							value="1"
							checked>
						Radio 1
					</label>
					<label>
						<input type="radio"
							name="optionsRadios"
							id="optionsRadios2"
							value="2">
						Radio 2
					</label>
				</div>
				<br>
@@if chip == 'LR1121':
				Basic support is available for the LR1121 and setting 915 MHz.
				<br>
				<div class="mui-checkbox">
					<label>
						<input type="checkbox"
							name="setSubGHz"
							id="optionsSetSubGHz">
						Set 915 MHz
					</label>
				</div>
@@end
				<br>
				<button id="start-cw" class="mui-btn mui-btn--primary" disabled>Start Continuous Wave</button>
			</form>
			<br>
			<div class="mui-textfield">
				<input id='measured' type='text' required/>
				<label>Center Frequency</label>
			</div>
			<div id="result" style="display: none;">
				<table class="mui-table mui-table--bordered">
					<tr><td>Calculated XO Freq</td><td id="calculated"></td></tr>
					<tr><td>Calculated XO Offset (kHz)</td><td id="offset"></td></tr>
					<tr><td>Calculated XO Offset (PPM)</td><td id="ppm"></td></tr>
					<tr><td>Raw Offset (kHz)</td><td id="raw"></td></tr>
					<tr><td>TL;DR</td><td id="tldr"></td></tr>
				</table>
			</div>
		</div>
	</div>
</body>
<script src="cw.js"></script>
</html>
