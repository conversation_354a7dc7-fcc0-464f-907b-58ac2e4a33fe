@@require(PLATFORM, VERSION, isTX)
<!DOCTYPE HTML>
<html lang="en">

<head>
	<title>Welcome to your ExpressLRS System</title>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<link rel="stylesheet" href="elrs.css" />
	<style>

img.icon-input {
	content: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='700pt' height='700pt' version='1.1' viewBox='0 0 700 700' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m326.67 46.668h46.664v260.54l140-140 32.996 32.992-196.33 196.33-196.33-196.33 32.996-32.992 140 140zm210 396.66v-93.332h46.664v140h-466.66v-140h46.664v93.332z' fill='%2312B0FB' fill-rule='evenodd'/%3E%3C/svg%3E%0A");
	display: block;
	height: 1.5em;
	width: 1.5em;
	float: left;
}
td img.icon-input {
	float: right;
}

img.icon-output {
	content: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='700pt' height='700pt' version='1.1' viewBox='0 0 700 700' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m373.33 396.67h-46.664v-283.88l-140 140-32.996-32.992 196.33-196.33 196.33 196.33-32.996 32.992-140-140zm163.34 46.664v-93.332h46.664v140h-466.66v-140h46.664v93.332z' fill='%2371D358' fill-rule='evenodd'/%3E%3C/svg%3E%0A");
	display: block;
	height: 1.5em;
	width: 1.5em;
	float: left;
}
td img.icon-output {
	float: right;
}

img.icon-analog {
	content: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='700pt' height='700pt' version='1.1' viewBox='0 0 700 700' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg%3E%3Cpath fill='%23FFB258' d='m357.88 493.4c-1.5078 0.14062-2.1211 0.24219-2.7383 0.24609-4.1055 0.015625-8.2031 0.003907-12.906 0.003907-0.089844-1.7578-0.24609-3.4102-0.24609-5.0664-0.003906-59.535-0.12891-119.07 0.17188-178.59 0.039062-7.5078-2.9219-13.719-5.3359-20.281-0.375-1.0195-2.2852-1.9609-3.5547-2.0703-3.293-0.29297-6.625-0.09375-9.9414-0.09375h-223.6-21.555c-0.16797-6.7031 0.25391-13.125 2.4805-19.938 12.93-39.48 27.965-77.98 49.668-113.58 9.9688-16.359 21.422-31.543 35.812-44.398 32.379-28.918 69.746-29.664 103.05-1.8125 20.035 16.762 34.719 37.828 47.383 60.328 7.8672 13.988 14.727 28.547 22.035 42.852 0.82422 1.6133 1.625 3.2422 3.2422 4.7031v-149.36h15.848v5.6094c-0.035156 60.637-0.22266 121.27 0.14062 181.91 0.035156 5.6445 3.4648 11.297 5.4805 16.883 0.20703 0.57031 1.4961 0.91406 2.3359 1.0547 1.0742 0.17969 2.1992 0.050781 3.3086 0.050781 81.008-0.003906 162.01-0.003906 243.02-0.011718h9.6758c0.46875 6.6797 0.34766 12.562-2.0508 18.676-9.2461 23.582-17.238 47.672-27.066 70.996-11.355 26.945-25.742 52.336-45.031 74.602-11.906 13.742-25.418 25.559-42.824 31.82-22.547 8.1094-43.766 4.4141-63.57-8.4727-19.449-12.652-33.645-30.312-46.367-49.309-13.188-19.691-23.922-40.723-33.859-62.188-0.71875-1.5508-1.4375-3.0977-3.0078-4.5078 0.003906 49.875 0.003906 99.738 0.003906 149.95zm11.914-205.65c0.46094 1.668 0.64844 2.75 1.0469 3.7539 13.316 33.551 27.578 66.652 47.062 97.215 9.7148 15.238 20.59 29.508 34.832 40.887 17.785 14.207 34.82 15.301 52.586 3.1797 12.449-8.4883 21.973-19.867 30.379-32.176 22.062-32.277 36.883-68.012 49.422-104.8 0.875-2.5664 1.5273-5.2031 2.3594-8.0586-72.914-0.003906-145.03-0.003906-217.69-0.003906zm-257.73-16.09h217.44c0-0.66797 0.11719-1.1641-0.023438-1.5781-0.65625-1.9336-1.3438-3.8594-2.0898-5.7578-13.402-34.055-28.195-67.441-48.496-98.062-10.148-15.309-21.422-29.727-37.043-39.969-16.32-10.703-31.035-10.457-47.086 0.48047-12.465 8.4961-21.973 19.84-30.445 32.09-18.867 27.281-32.129 57.371-43.668 88.297-2.9492 7.9062-5.5977 15.941-8.5898 24.5z'/%3E%3C/g%3E%3C/svg%3E%0A");
	display: block;
	height: 1.5em;
	width: 1.5em;
	float: left;
}
td img.icon-analog {
	float: right;
}

img.icon-pwm {
	content: url("data:image/svg+xml,%3Csvg width='400' height='400' xmlns='http://www.w3.org/2000/svg'%3E%3Cg stroke='%23C462DD' stroke-width='10' stroke-linecap='undefined' stroke-linejoin='undefined' fill='none'%3E%3Cline y2='318' x2='2' y1='318' x1='106'/%3E%3Cline y2='74' x2='105' y1='323.00001' x1='105'/%3E%3Cline y2='79' x2='254.00001' y1='79' x1='106'/%3E%3Cline y2='323' x2='249' y1='79' x1='249'/%3E%3Cline y2='318' x2='248' y1='318' x1='320'/%3E%3Cline y2='317' x2='315' y1='77' x1='315'/%3E%3Cline y2='77' x2='310' y1='77' x1='377'/%3E%3Cline y2='78' x2='372' y1='322.00001' x1='372'/%3E%3Cline y2='317' x2='371' y1='317' x1='399'/%3E%3C/g%3E%3C/svg%3E");
	display: block;
	height: 1.5em;
	width: 1.5em;
	float: left;
}
td img.icon-pwm {
	float: right;
}
	</style>
</head>

<body>
	<header class="mui-appbar mui--z1 mui--text-center elrs-header">
		@@include("logo-template.svg")
		<h1><b>ExpressLRS</b></h1>
		<span id="product_name"></span><br/>
		<b>Firmware Rev. </b>@@{VERSION} <span id="reg_domain"></span>
	</header>
	<br>
	<div class="mui-container-fluid mui-col-sm-10 mui-col-sm-offset-1">
		<div id="custom_config" class="mui-panel" style="display:none; background-color: #FFC107;">
			This hardware configuration has been customised. This can be safely ignored if this is a custom hardware build or for testing purposes.<br>
			You can <a download href="/hardware.json">download</a> the configuration or <a href="/reset?hardware">reset</a> to pre-configured defaults and reboot.
		</div>
		<div class="mui-panel">
			<label>Upload target configuration (remember to press "Save Target Configuration" below):</label>
			<button class="mui-btn mui-btn--small mui-btn--primary upload">
				<label>
					Upload
					<input type="file" id="fileselect" name="fileselect[]" />
				</label>
			</button>
			<div id="filedrag">or drop files here</div>
		</div>
		<div class="mui-panel">
			<form id='upload_hardware' method='POST' action="/hardware">
				<input type="hidden" id="customised" name="customised" value="true"/>
				<table>
					<tr><td colspan='2'><b>CRSF Serial Pins</td></tr>
					<tr><td width="30"></td><td>RX pin<img class="icon-input"/></td><td><input size='3' id='serial_rx' name='serial_rx' type='text'/></td><td>Pin used to receive CRSF signal from the handset</td></tr>
					<tr><td></td><td>TX pin<img class="icon-output"/></td><td><input size='3' id='serial_tx' name='serial_tx' type='text'/></td><td>Pin used to transmit CRSF telemetry to the handset (may be the same as the RX PIN)</td></tr>

					<tr><td colspan='2'><b>Serial2 Pins</td></tr>
					<tr><td width="30"></td><td>RX pin<img class="icon-input"/></td><td><input size='3' id='serial1_rx' name='serial1_rx' type='text'/></td><td>Serial2 RX - ESP32 targets only</td></tr>
					<tr><td></td><td>TX pin<img class="icon-output"/></td><td><input size='3' id='serial1_tx' name='serial1_tx' type='text'/></td><td>Serial2 TX - ESP32 targets only</td></tr>

					<tr><td colspan='2'><b>Radio Chip Pins & Options</td></tr>
					<tr><td></td><td>BUSY pin<img class="icon-input"/></td><td><input size='3' id='radio_busy' name='radio_busy' type='text'/></td><td>GPIO Input connected to SX128x busy pin</td></tr>
					<tr><td></td><td>DIO0 pin<img class="icon-input"/></td><td><input size='3' id='radio_dio0' name='radio_dio0' type='text'/></td><td>Unused on SX128x, Interrupt pin for SX127x</td></tr>
					<tr><td></td><td>DIO1 pin<img class="icon-input"/></td><td><input size='3' id='radio_dio1' name='radio_dio1' type='text'/></td><td>Interrupt pin for SX128x</td></tr>
					<tr><td></td><td>DIO2 pin<img class="icon-input"/></td><td><input size='3' id='radio_dio2' name='radio_dio2' type='text'/></td><td>Unused on SX128x and 127x</td></tr>
					<tr><td></td><td>MISO pin<img class="icon-input"/></td><td><input size='3' id='radio_miso' name='radio_miso' type='text'/></td><td>MISO connected to (possibly) multiple SX1280/127x</td></tr>
					<tr><td></td><td>MOSI pin<img class="icon-output"/></td><td><input size='3' id='radio_mosi' name='radio_mosi' type='text'/></td><td>MOSI connected to (possibly) multiple SX1280/127x</td></tr>
					<tr><td></td><td>NSS pin<img class="icon-output"/></td><td><input size='3' id='radio_nss' name='radio_nss' type='text'/></td><td>Chip select pin for first SX1280/127x</td></tr>
					<tr><td></td><td>RST pin<img class="icon-output"/></td><td><input size='3' id='radio_rst' name='radio_rst' type='text'/></td><td>Reset pin connected to (possibly) multiple SX1280/127x</td></tr>
					<tr><td></td><td>SCK pin<img class="icon-output"/></td><td><input size='3' id='radio_sck' name='radio_sck' type='text'/></td><td>Clock pin connected to (possibly) multiple SX1280/127x</td></tr>
					<tr><td></td><td>BUSY_2 pin<img class="icon-input"/></td><td><input size='3' id='radio_busy_2' name='radio_busy_2' type='text'/></td><td>Busy pin for second SX1280</td></tr>
					<tr><td></td><td>DIO0_2 pin<img class="icon-input"/></td><td><input size='3' id='radio_dio0_2' name='radio_dio0_2' type='text'/></td><td>Interrupt pin for second SX127x</td></tr>
					<tr><td></td><td>DIO1_2 pin<img class="icon-input"/></td><td><input size='3' id='radio_dio1_2' name='radio_dio1_2' type='text'/></td><td>Interrupt pin for second SX1280</td></tr>
					<tr><td></td><td>NSS_2 pin<img class="icon-output"/></td><td><input size='3' id='radio_nss_2' name='radio_nss_2' type='text'/></td><td>Chip select pin for second SX1280</td></tr>
					<tr><td></td><td>RST_2 pin<img class="icon-output"/></td><td><input size='3' id='radio_rst_2' name='radio_rst_2' type='text'/></td><td>Reset pin connected to second SX1280/127x</td></tr>
					<tr><td></td><td>DCDC enabled</td><td><input size='3' id='radio_dcdc' name='radio_dcdc' type='checkbox'/></td><td>Use the SX1280 DC-DC converter rather than LDO voltage regulator (15uH inductor must be present)</td></tr>
					<tr><td></td><td>RFO_HF enabled</td><td><input size='3' id='radio_rfo_hf' name='radio_rfo_hf' type='checkbox'/></td><td>SX127x PA to use, either the RFO_HF or PA_BOOST (depends on circuit design)</td></tr>
					<tr><td></td><td>LR1121 RF Switch Controls</td><td><input size='40' id='radio_rfsw_ctrl' name='radio_rfsw_ctrl' type='text' class='array'/></td><td>Comma-separated list of 8 values used for setting the LR1121 RF switch controls</td></tr>

					<tr><td colspan='2'><b>Radio Antenna</td></tr>
					<tr><td></td><td>CTRL pin<img class="icon-output"/></td><td><input size='3' id='ant_ctrl' name='ant_ctrl' type='text'/></td><td>Pin connected to Antenna select pin on power amplifier</td></tr>
					<tr><td></td><td>CTRL_COMPL pin<img class="icon-output"/></td><td><input size='3' id='ant_ctrl_compl' name='ant_ctrl_compl' type='text'/></td><td>Inverted CTRL for devices using antenna selectors that need separate pins for A/B selection</td></tr>

					<tr><td colspan='2'><b>Radio Power</td></tr>
					<tr><td></td><td>PA enable pin<img class="icon-output"/></td><td><input size='3' id='power_enable' name='power_enable' type='text'/></td><td>Enable the power amplifier (active high)</td></tr>
					<tr><td></td><td>APC1 pin<img class="icon-output"/></td><td><input size='3' id='power_apc1' name='power_apc1' type='text'/></td><td>Power amplifier control voltage</td></tr>
					<tr><td></td><td>APC2 pin<img class="icon-output"/></td><td><input size='3' id='power_apc2' name='power_apc2' type='text'/></td><td>Power amplifier control voltage</td></tr>
					<tr><td></td><td>RXEN pin<img class="icon-output"/></td><td><input size='3' id='power_rxen' name='power_rxen' type='text'/></td><td>Enable RX mode LNA (active high)</td></tr>
					<tr><td></td><td>TXEN pin<img class="icon-output"/></td><td><input size='3' id='power_txen' name='power_txen' type='text'/></td><td>Enable TX mode PA (active high)</td></tr>
					<tr><td></td><td>RXEN_2 pin<img class="icon-output"/></td><td><input size='3' id='power_rxen_2' name='power_rxen_2' type='text'/></td><td>Enable RX mode LNA on second SX1280 (active high)</td></tr>
					<tr><td></td><td>TXEN_2 pin<img class="icon-output"/></td><td><input size='3' id='power_txen_2' name='power_txen_2' type='text'/></td><td>Enable TX mode PA on second SX1280 (active high)</td></tr>
					<tr><td></td><td>Min Power</td><td>
						<select id='power_min' name='power_min'>
							<option value='0'>10mW</option>
							<option value='1'>25mW</option>
							<option value='2'>50mW</option>
							<option value='3'>100mW</option>
							<option value='4'>250mW</option>
							<option value='5'>500mW</option>
							<option value='6'>1000mW</option>
							<option value='7'>2000mW</option>
						</select>
					</td><td>Minimum selectable power output</td></tr>
					<tr><td></td><td>High Power</td><td>
						<select id='power_high' name='power_high'>
							<option value='0'>10mW</option>
							<option value='1'>25mW</option>
							<option value='2'>50mW</option>
							<option value='3'>100mW</option>
							<option value='4'>250mW</option>
							<option value='5'>500mW</option>
							<option value='6'>1000mW</option>
							<option value='7'>2000mW</option>
						</select>
					</td><td>Highest selectable power output (if option for higher power is NOT enabled)</td></tr>
					<tr><td></td><td>Max Power</td><td>
						<select id='power_max' name='power_max'>
							<option value='0'>10mW</option>
							<option value='1'>25mW</option>
							<option value='2'>50mW</option>
							<option value='3'>100mW</option>
							<option value='4'>250mW</option>
							<option value='5'>500mW</option>
							<option value='6'>1000mW</option>
							<option value='7'>2000mW</option>
						</select>
					</td><td>Absolute maximum selectable power output (only available if 'higher power' option is enabled)</td></tr>
					<tr><td></td><td>Default Power</td><td>
						<select id='power_default' name='power_default'>
							<option value='0'>10mW</option>
							<option value='1'>25mW</option>
							<option value='2'>50mW</option>
							<option value='3'>100mW</option>
							<option value='4'>250mW</option>
							<option value='5'>500mW</option>
							<option value='6'>1000mW</option>
							<option value='7'>2000mW</option>
						</select>
					</td><td>Default power output when resetting or first flashing a module</td></tr>
					<tr><td></td><td>Power Level control</td><td>
						<select id='power_control' name='power_control'>
							<option value='0'>via SEMTECH</option>
							<!--<option value='1'>ANALOG</option>
							<option value='2'>DAC</option>-->
							<option value='3'>via ESP DACWRITE</option>
						</select>
					</td><td>How the power level is set</td></tr>
					<tr><td></td><td>Power Value(s)</td><td><input size='40' id='power_values' name='power_values' type='text' class='array'/></td><td>Comma-separated list of values that set the power output (if using a DAC these are the DAC values)</td></tr>
					<tr><td></td><td>Secondary Power Value(s)</td><td><input size='40' id='power_values2' name='power_values2' type='text' class='array'/></td><td>Comma-separated list of values that set the power output (if using a DAC then these set the Semtech power output)</td></tr>
					<tr><td></td><td>Dual Power Value(s)</td><td><input size='40' id='power_values_dual' name='power_values_dual' type='text' class='array'/></td><td>Comma-separated list of values that set the higher frequency power output of a dual band Tx/Rx</td></tr>
					<tr><td></td><td>PA LNA Gain</td><td><input size='20' id='power_lna_gain' name='power_lna_gain' type='text'/></td><td>The amount of dB gain provided by the LNA</td></tr>

					<tr><td colspan='2'><b>Radio Power Detection</td></tr>
					<tr><td></td><td>PDET pin<img class="icon-analog"/></td><td><input size='3' id='power_pdet' name='power_pdet' type='text'/></td><td>Analog input (up to 1.1V) connected to 'power detect' pin on PA for adjustment of the power output</td></tr>
					<tr><td></td><td>Intercept</td><td><input size='20' id='power_pdet_intercept' name='power_pdet_intercept' type='text'/></td><td>Intercept and Slope are used together to calculate the dBm from the measured mV on the PDET pin</td></tr>
					<tr><td></td><td>Slope</td><td><input size='20' id='power_pdet_slope' name='power_pdet_slope' type='text'/></td><td>dBm = mV * slope + intercept, this is then used to adjust the actual output power accordingly</td></tr>
@@if isTX:
					<tr><td colspan='2'><b>Analog Joystick</td></tr>
					<tr><td></td><td>ADC pin<img class="icon-analog"/></td><td><input size='3' id='joystick' name='joystick' type='text'/></td><td>Analog Input (3.3V max) use to read joystick direction using a resistor network</td></tr>
					<tr><td></td><td>Values</td><td><input size='40' id='joystick_values' name='joystick_values' type='text' class='array'/></td><td>Comma-separated list of ADC values (12-bit) for UP, DOWN, LEFT, RIGHT, ENTER, IDLE</td></tr>

					<tr><td colspan='2'><b>Digital Joystick</td></tr>
					<tr><td></td><td>Pin 1<img class="icon-input"/></td><td><input size='3' id='five_way1' name='five_way1' type='text'/></td><td>These 3 pins create a binary value for the joystick direction</td></tr>
					<tr><td></td><td>Pin 2<img class="icon-input"/></td><td><input size='3' id='five_way2' name='five_way2' type='text'/></td><td>7 = IDLE, 6 = OK, 5 = DOWN</td></tr>
					<tr><td></td><td>Pin 3<img class="icon-input"/></td><td><input size='3' id='five_way3' name='five_way3' type='text'/></td><td>4 = RIGHT, 3 = UP, 2 = LEFT</td></tr>
@@end
					<tr><td colspan='2'><b>Mood Lighting</td></tr>
					<tr><td></td><td>RGB LED pin<img class="icon-output"/></td><td><input size='3' id='led_rgb' name='led_rgb' type='text'/></td><td>Signal pin for WS2812 RGB LED or LED strip</td></tr>
					<tr><td></td><td>RGB LED is GRB</td><td><input size='3' id='led_rgb_isgrb' name='led_rgb_isgrb' type='checkbox'/></td><td>Most WS2812 RGB LEDs are actually GRB</td></tr>
					<tr><td></td><td>RGB indexes for Status</td><td><input size='40' id='ledidx_rgb_status' name='ledidx_rgb_status' type='text' class='array'/></td><td>Indexes into the "string" of RGB LEDs (if empty then only LED at 0 is used)</td></tr>
@@if not isTX:
					<tr><td></td><td>RGB indexes for VTX Status</td><td><input size='40' id='ledidx_rgb_vtx' name='ledidx_rgb_vtx' type='text' class='array'/></td><td>Indexes into the "string" of RGB LEDs (if empty then no VTX status)</td></tr>
@@end
					<tr><td></td><td>RGB indexes for Boot animation</td><td><input size='40' id='ledidx_rgb_boot' name='ledidx_rgb_boot' type='text' class='array'/></td><td>Indexes into the "string" of RGB LEDs (if empty status indexes are used)</td></tr>
					<tr><td></td><td>LED pin<img class="icon-output"/></td><td><input size='3' id='led' name='led' type='text'/></td><td>Only use when only a single LED is used</td></tr>
					<tr><td></td><td>Red LED pin<img class="icon-output"/></td><td><input size='3' id='led_red' name='led_red' type='text'/></td><td>If there are multiple LEDs, then this is the pin for the RED LED</td></tr>
					<tr><td></td><td>Red LED inverted</td><td><input size='3' id='led_red_invert' name='led_red_invert' type='checkbox'/></td><td>LEDs are active LOW unless this is checked</td></tr>
					<tr><td></td><td>Green LED pin<img class="icon-output"/></td><td><input size='3' id='led_green' name='led_green' type='text'/></td><td>If there is a GREEN LED as well as RED above</td></tr>
					<tr><td></td><td>Green LED inverted</td><td><input size='3' id='led_green_invert' name='led_green_invert' type='checkbox'/></td><td>Check if the LED is active HIGH</td></tr>
					<tr><td></td><td>Blue LED pin<img class="icon-output"/></td><td><input size='3' id='led_blue' name='led_blue' type='text'/></td><td>Pin for a 3rd, BLUE, LED!</td></tr>
					<tr><td></td><td>Blue LED inverted</td><td><input size='3' id='led_blue_invert' name='led_blue_invert' type='checkbox'/></td><td>Check if the LED is active HIGH</td></tr>
					<!-- SIYI things!
					<tr><td></td><td>Green/Red LED pin<img class="icon-output"/></td><td><input size='3' id='led_green_red' name='led_green_red' type='text'/></td></tr>
					<tr><td></td><td>Red/Green LED pin<img class="icon-output"/></td><td><input size='3' id='led_red_green' name='led_red_green' type='text'/></td></tr>
					-->
					<tr><td colspan='2'><b>Button(s)</td></tr>
					<tr><td></td><td>Button 1 pin<img class="icon-output"/></td><td><input size='3' id='button' name='button' type='text'/></td><td>Single/first (active low) button</td></tr>
					<tr><td></td><td>Button 1 RGB Index</td><td><input size='3' id='button_led_index' name='button_led_index' type='text'/></td><td>Index of button LED in RGB string, leave empty for no RGB LED</td></tr>
					<tr><td></td><td>Button 2 pin<img class="icon-output"/></td><td><input size='3' id='button2' name='button2' type='text'/></td><td>Second (active low) button</td></tr>
					<tr><td></td><td>Button 2 RGB Index</td><td><input size='3' id='button2_led_index' name='button2_led_index' type='text'/></td><td>Index of button LED in RGB string, leave empty for no RGB LED</td></tr>
@@if isTX:
					<tr><td colspan='2'><b>OLED/TFT (Crotch TV)</td></tr>
					<tr><td></td><td>Screen type</td><td>
						<select id='screen_type' name='screen_type'>
							<option value='0'>None</option>
							<option value='1'>I2C OLED (SSD1306 128x64)</option>
							<option value='2'>SPI OLED (SSD1306 128x64)</option>
							<option value='3'>SPI OLED (small SSD1306 128x32)</option>
							<option value='4'>SPI TFT (ST7735 160x80)</option>
						</select>
					</td><td>Type of OLED connected</td></tr>
					<tr><td></td><td>180 rotation</td><td><input size='3' id='screen_reversed' name='screen_reversed' type='checkbox'/></td><td>Select to rotate the display 180 degrees</td></tr>
					<tr><td></td><td>CS pin<img class="icon-output"/></td><td><input size='3' id='screen_cs' name='screen_cs' type='text'/></td><td>Chip Select (if using SPI)</td></tr>
					<tr><td></td><td>DC pin<img class="icon-output"/></td><td><input size='3' id='screen_dc' name='screen_dc' type='text'/></td><td>Data/Command Select (if using SPI)</td></tr>
					<tr><td></td><td>MOSI pin<img class="icon-output"/></td><td><input size='3' id='screen_mosi' name='screen_mosi' type='text'/></td><td>Data (if using SPI)</td></tr>
					<tr><td></td><td>RST pin<img class="icon-output"/></td><td><input size='3' id='screen_rst' name='screen_rst' type='text'/></td><td>Reset</td></tr>
					<tr><td></td><td>SCK pin<img class="icon-output"/></td><td><input size='3' id='screen_sck' name='screen_sck' type='text'/></td><td>Clock (either SPI or I2C)</td></tr>
					<tr><td></td><td>SDA pin<img class="icon-input"/><img class="icon-output"/></td><td><input size='3' id='screen_sda' name='screen_sda' type='text'/></td><td>Data (I2C)</td></tr>
					<tr><td></td><td>BL pin<img class="icon-output"/></td><td><input size='3' id='screen_bl' name='screen_bl' type='text'/></td><td>Backlight</td></tr>

					<tr><td colspan='2'><b>Backpack / Logging</td></tr>
					<tr><td></td><td>Enable Backpack</td><td><input size='3' id='use_backpack' name='use_backpack' type='checkbox'/></td><td>If a TX backpack is connected</td></tr>
					<tr><td></td><td>Baud Rate</td><td><input size='10' id='debug_backpack_baud' name='debug_backpack_baud' type='text'/></td><td>Baud rate used to communicate to the backpack (normally 460800)</td></tr>
					<tr><td></td><td>RX pin<img class="icon-input"/></td><td><input size='3' id='debug_backpack_rx' name='debug_backpack_rx' type='text'/></td><td>Connected to TX pin on backpack</td></tr>
					<tr><td></td><td>TX pin<img class="icon-output"/></td><td><input size='3' id='debug_backpack_tx' name='debug_backpack_tx' type='text'/></td><td>Connected to RX pin on backpack</td></tr>
					<tr><td></td><td>BOOT pin<img class="icon-output"/></td><td><input size='3' id='backpack_boot' name='backpack_boot' type='text'/></td><td>Pin connected to GPIO0 pin on backpack ESP8285, allows passthrough flashing</td></tr>
					<tr><td></td><td>EN pin<img class="icon-output"/></td><td><input size='3' id='backpack_en' name='backpack_en' type='text'/></td><td>Pin connected to EN pin on backpack ESP8285, allows passthrough flashing</td></tr>
					<tr><td></td><td>Passthrough baud</td><td><input size='7' id='passthrough_baud' name='passthrough_baud' type='text'/></td><td>Baud rate to flash the backpack ESP8285 (default is to use the baud rate above)</td></tr>

					<tr><td colspan='2'><b>I2C & Misc Devices</td></tr>
					<tr><td></td><td>SCL pin<img class="icon-output"/></td><td><input size='3' id='i2c_scl' name='i2c_scl' type='text'/></td><td>I2C clock pin used to communicate with I2C devices (may be the same as OLED I2C)</td></tr>
					<tr><td></td><td>SDA pin<img class="icon-input"/><img class="icon-output"/></td><td><input size='3' id='i2c_sda' name='i2c_sda' type='text'/></td><td>I2C data pin used to communicate with I2C devices (may be the same as OLED I2C)</td></tr>
					<!-- <tr><td></td><td>Buzzer pin</td><td><input size='3' id='misc_buzzer' name='misc_buzzer' type='text'/></td><td>Pin connected to a PWM controlled buzzer</td></tr> -->
					<tr><td></td><td>Fan enable pin<img class="icon-output"/></td><td><input size='3' id='misc_fan_en' name='misc_fan_en' type='text'/></td><td>Pin used to enable a cooling FAN (active HIGH)</td></tr>
					<tr><td></td><td>Fan PWM pin<img class="icon-pwm"/></td><td><input size='3' id='misc_fan_pwm' name='misc_fan_pwm' type='text'/></td><td>If the fan is controlled by PWM</td></tr>
					<tr><td></td><td>Fan PWM output values</td><td><input size='40' id='misc_fan_speeds' name='misc_fan_speeds' type='text' class='array'/></td><td>If the fan is PWM controlled, then this is the list of values for the PWM output for the matching power output levels</td></tr>
					<tr><td></td><td>Fan TACHO pin<img class="icon-input"/></td><td><input size='3' id='misc_fan_tacho' name='misc_fan_tacho' type='text'/></td><td>If the fan has a "tachometer" interrupt pin</td></tr>
					<tr><td></td><td>Has STK8xxx G-sensor</td><td><input size='3' id='gsensor_stk8xxx' name='gsensor_stk8xxx' type='checkbox'/></td><td>Checked if there is a STK8xxx g-sensor on the I2C bus</td></tr>
					<tr><td></td><td>G-sensor interrupt pin<img class="icon-input"/></td><td><input size='3' id='misc_gsensor_int' name='misc_gsensor_int' type='text'/></td><td>Pin connected the STK8xxx g-sensor for interrupts</td></tr>
					<tr><td></td><td>Has LM75A Thermal sensor</td><td><input size='3' id='thermal_lm75a' name='thermal_lm75a' type='checkbox'/></td><td>Checked if there is a LM75A thermal sensor on the I2C bus</td></tr>
@@end
@@if not isTX:
					<tr><td colspan='2'><b>PWM</td></tr>
					<tr><td></td><td>PWM output pins<img class="icon-pwm"/></td><td><input size='40' id='pwm_outputs' name='pwm_outputs' type='text' class='array'/></td><td>Comma-separated list of pins used for PWM output</td></tr>

					<tr><td colspan='2'><b>VBat</td></tr>
					<tr><td></td><td>VBat pin<img class="icon-analog"/></td><td><input size='3' id='vbat' name='vbat' type='text'/></td><td>Analog input pin for reading VBAT voltage (1V max on 8285, 3.3V max on ESP32)</td></tr>
					<tr><td></td><td>VBat offset</td><td><input size='7' id='vbat_offset' name='vbat_offset' type='text'/></td><td>Offset and scale are used together with the analog pin to calculate the voltage</td></tr>
					<tr><td></td><td>VBat scale</td><td><input size='7' id='vbat_scale' name='vbat_scale' type='text'/></td><td>voltage = (analog - offset) / scale</td></tr>
					<tr><td></td><td>VBat attenuation</td><td>
						<select id='vbat_atten' name='vbat_atten'>
							<option value='-1'>Default</option>
							<option value='0'>0 dB</option>
							<option value='1'>2.5 dB</option>
							<option value='2'>6 dB</option>
							<option value='3'>11 dB</option>
							<option value='4'>0 dB + calibration</option>
							<option value='5'>2.5 dB + calibration</option>
							<option value='6'>6 dB + calibration</option>
							<option value='7'>11 dB + calibration</option>
						</select>
					</td><td>ADC pin attenuation (ESP32) and optional efuse-based calibration adjustment</td></tr>

					<tr><td colspan='2'><b>SPI VTX</td></tr>
					<tr><td></td><td>RF amp PWM pin<img class="icon-pwm"/></td><td><input size='3' id='vtx_amp_pwm' name='vtx_amp_pwm' type='text'/></td><td>Set the power output level of the VTX PA (value is calculated based on power and frequency using VPD interpolation values)</td></tr>
					<tr><td></td><td>RF amp VPD pin<img class="icon-analog"/></td><td><input size='3' id='vtx_amp_vpd' name='vtx_amp_vpd' type='text'/></td><td>Analog input for VPD (power detect) from VTX PA</td></tr>
					<tr><td></td><td>RF amp VREF pin<img class="icon-output"/></td><td><input size='3' id='vtx_amp_vref' name='vtx_amp_vref' type='text'/></td><td>Active high enable pin the the VTX PA VREF (voltage reference)</td></tr>
					<tr><td></td><td>SPI NSS pin<img class="icon-output"/></td><td><input size='3' id='vtx_nss' name='vtx_nss' type='text'/></td><td>Chip select for RTC6705 VTx (leave undefined if sharing Radio SPI bus)</td></tr>
					<tr><td></td><td>SPI SCK pin<img class="icon-output"/></td><td><input size='3' id='vtx_sck' name='vtx_sck' type='text'/></td><td>Clock pin on RTC6705 VTx (leave undefined if sharing Radio SPI bus)</td></tr>
					<tr><td></td><td>SPI MISO pin<img class="icon-input"/></td><td><input size='3' id='vtx_miso' name='vtx_miso' type='text'/></td><td>MISO pin on RTC6705 VTx (leave undefined if sharing Radio SPI bus)</td></tr>
					<tr><td></td><td>SPI MOSI pin<img class="icon-output"/></td><td><input size='3' id='vtx_mosi' name='vtx_mosi' type='text'/></td><td>MOSI pin on RTC6705 VTx (leave undefined if sharing Radio SPI bus)</td></tr>
					<tr><td></td><td>25mW VPD interpolation values</td><td><input size='40' id='vtx_amp_vpd_25mW' name='vtx_amp_vpd_25mW' type='text' class='array'/></td><td>4 values for 5650, 5750, 5850, 5950 frequencies at 25mW</td></tr>
					<tr><td></td><td>100mW VPD interpolation values</td><td><input size='40' id='vtx_amp_vpd_100mW' name='vtx_amp_vpd_100mW' type='text' class='array'/></td><td>4 values for 5650, 5750, 5850, 5950 frequencies at 100mW</td></tr>
					<tr><td></td><td>25mW PWM interpolation values</td><td><input size='40' id='vtx_amp_pwm_25mW' name='vtx_amp_pwm_25mW' type='text' class='array'/></td><td>4 values for 5650, 5750, 5850, 5950 frequencies at 25mW</td></tr>
					<tr><td></td><td>100mW PWM interpolation values</td><td><input size='40' id='vtx_amp_pwm_100mW' name='vtx_amp_pwm_100mW' type='text' class='array'/></td><td>4 values for 5650, 5750, 5850, 5950 frequencies at 100mW</td></tr>

					<tr><td colspan='2'><b>I2C</td></tr>
					<tr><td></td><td>SCL pin<img class="icon-output"/></td><td><input size='3' id='i2c_scl' name='i2c_scl' type='text'/></td><td>I2C clock pin used to communicate with I2C devices</td></tr>
					<tr><td></td><td>SDA pin<img class="icon-input"/><img class="icon-output"/></td><td><input size='3' id='i2c_sda' name='i2c_sda' type='text'/></td><td>I2C data pin used to communicate with I2C devices</td></tr>
@@end
				</table>
				<br>
				<input type='button' value='Save Target Configuration' class='mui-btn mui-btn--primary' onclick="submitHardwareSettings()"/>
			</form>
		</div>
	</div>
</body>
<script src="hardware.js"></script>
</html>
