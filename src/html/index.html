@@require(PLATFORM, VERSION, isTX, hasSubGHz, is8285)
<!DOCTYPE HTML>
<html lang="en">

<head>
	<title>Welcome to your ExpressLRS System</title>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<link rel="stylesheet" href="elrs.css" />
</head>

<body>
	<header class="mui-appbar mui--z1 mui--text-center elrs-header">
		@@include("logo-template.svg")
		<h1><b>ExpressLRS</b></h1>
		<span id="product_name"></span><br/>
		<b>Firmware Rev. </b>@@{VERSION} <span id="reg_domain"></span>
	</header>
	<br/>
	<div class="mui-container-fluid">
		<div class="mui-panel mui-col-sm-10 mui-col-sm-offset-1">

			<ul class="mui-tabs__bar mui-tabs__bar--justified">
@@if not isTX:
				<li><a data-mui-toggle="tab" data-mui-controls="pane-justified-3">Model</a></li>
@@end
				<li><a data-mui-toggle="tab" data-mui-controls="pane-justified-1">Options</a></li>
				<li><a id="network-tab" data-mui-toggle="tab" data-mui-controls="pane-justified-4">WiFi</a></li>
@@if isTX:
				<li id="button-tab"><a data-mui-toggle="tab" data-mui-controls="pane-justified-3">Buttons</a></li>
@@end
				<li><a data-mui-toggle="tab" data-mui-controls="pane-justified-2">Update</a></li>
			</ul>

			<div class="mui-tabs__pane" id="pane-justified-1">
				<div class="mui-panel">
					<h2>Runtime Options</h2>
					This form <b>overrides</b> the options provided when the firmware was flashed. These changes will persist across reboots, but <b>will be reset</b> when the firmware is reflashed.
@@if isTX:
					Note: The Binding phrase is <b>not</b> remembered, it is a temporary field used to generate the binding UID.
					<br/><br/>
					<div class="mui-textfield">
						<input type="text" id="phrase" name="phrase" placeholder="Binding Phrase" />
						<label for="phrase">Binding Phrase</label>
					</div>
					<form id='upload_options' method='POST' action="/options">
						<div class="mui-textfield">
							<label for='uid'><span id="uid-text"></span></label>
							<span class="badge" id="uid-type"></span>
							<input size='40' id='uid' name='uid' type='text' class='array' readonly/>
						</div>
@@else:
					<br/><br/>
					<form id='upload_options' method='POST' action="/options">
@@end
@@if hasSubGHz:
						<div class="mui-select">
							<select id='domain' name='domain'>
								<option value='0'>AU915</option>
								<option value='1'>FCC915</option>
								<option value='2'>EU868</option>
								<option value='3'>IN866</option>
								<option value='4'>AU433</option>
								<option value='5'>EU433</option>
								<option value='6'>US433</option>
								<option value='7'>US433-Wide</option>
							</select>
							<label for="domain">Regulatory domain</label>
						</div>
@@end
						<div class="mui-textfield">
							<input size='3' id='wifi-on-interval' name='wifi-on-interval' type='text' placeholder="Disabled"/>
							<label for="wifi-on-interval">WiFi "auto on" interval in seconds (leave blank to disable)</label>
						</div>
@@if isTX:
						<div class="mui-textfield">
							<input size='5' id='tlm-interval' name='tlm-interval' type='text'/>
							<label for="tlm-interval">TLM report interval (ms)</label>
						</div>
						<div class="mui-textfield">
							<input size='3' id='fan-runtime' name='fan-runtime' type='text'/>
							<label for="fan-runtime">Fan runtime (s)</label>
						</div>
						<div id="has-highpower" class="mui-checkbox" style="display: none;">
							<input id='unlock-higher-power' name='unlock-higher-power' type='checkbox'/>
							<label for="unlock-higher-power">Unlock higher power</label>
						</div>
						<div class="mui-checkbox">
							<input id='is-airport' name='is-airport' type='checkbox'/>
							<label for="is-airport">Use as AirPort Serial device</label>
						</div>
						<div class="mui-textfield">
							<input size='7' id='airport-uart-baud' name='airport-uart-baud' type='text'/>
							<label for="airport-uart-baud">AirPort UART baud</label>
						</div>
@@else:
						<div id="baud-config" class="mui-textfield"  style="display: block;">
							<input size='7' id='rcvr-uart-baud' name='rcvr-uart-baud' type='text'/>
							<label for="rcvr-uart-baud">UART baud</label>
						</div>
						<div class="mui-checkbox">
							<input id='lock-on-first-connection' name='lock-on-first-connection' type='checkbox'/>
							<label for="lock-on-first-connection">Lock on first connection</label>
						</div>
						<div class="mui-checkbox">
							<input id='is-airport' name='is-airport' type='checkbox'/>
							<label for="is-airport">Use as AirPort Serial device</label>
						</div>
@@end
						<button id='submit-options' class="mui-btn mui-btn--primary" disabled>Save</button>
						<div id="reset-options" style="display: none;">
							<a class="mui-btn mui-btn--small mui-btn--danger">Reset runtime options to defaults</a>
						</div>
						<input type="hidden" id="flash-discriminator" name="flash-discriminator"/>
						<input type="hidden" id="wifi-ssid" name="wifi-ssid"/>
						<input type="hidden" id='wifi-password' name='wifi-password'/>
					</form>
				</div>
@@if isTX:
				<div class="mui-panel">
					<h2>Import/Export</h2>
					<br/>
					<div>
						<a href="/config?export" download="models.json" target="_blank" class="mui-btn mui-btn--small mui-btn--dark">Save model configuration file</a>
					</div>
					<div>
						<button class="mui-btn mui-btn--small mui-btn--primary upload">
							<label>
								Upload model configuration file
								<input type="file" id="fileselect" name="fileselect[]" />
							</label>
						</button>
					</div>
				</div>
@@end
			</div>

			<div class="mui-tabs__pane" id="pane-justified-2">
				<div class="mui-panel">
					<h2>Firmware Update</h2>
					Select the correct <strong>firmware.bin@@{ ".gz" if (is8285 and not isTX) else "" }</strong> for @@{PLATFORM} otherwise a bad flash may occur.
					If this happens you will need to recover via USB/Serial. You may also download the <a href="firmware.bin" title="Click to download firmware">currently running firmware</a>.
					<br/><br/>
@@if (is8285 and not isTX):
					<div class="mui-panel" style="background-color: #fcecae;text-align:center;">Do NOT decompress/unzip/extract the firmware.bin.gz file. Upload the file as it is.</div>
					<br/>
@@end
					<button id="upload_btn" class="mui-btn mui-btn--primary upload" style="margin: 0 auto; display:block;">
						<label>
							Select firmware file
							<input type="file" id="firmware_file" name="update[]" />
						</label>
					</button>
					<div id="filedrag">or drop firmware file here</div>
					<br/>
					<h3 id="status"></h3>
					<progress id="progressBar" value="0" max="100" style="width:100%;"></progress>
				</div>
			</div>

@@if isTX:
			<div class="mui-tabs__pane" id="pane-justified-3">
				<div class="mui-panel">
					<h2>Button Actions</h2>
					Specify which actions to perform when clicking or long pressing module buttons.
					<br/><br/>
					<form class="mui-form" id='button_actions'>
						<table class="mui-table">
							<tbody id="button-actions"></tbody>
						</table>
						<div id="button1-color-div" style="display: none;">
							<input id='button1-color' name='button1-color' type='color'/>
							<label for="button1-color">User button 1 color</label>
						</div>
						<div id ="button2-color-div" style="display: none;">
							<input id='button2-color' name='button2-color' type='color'/>
							<label for="button2-color">User button 2 color</label>
						</div>
						<button id="submit-actions" class="mui-btn mui-btn--primary">Save</button>
					</form>
				</div>
			</div>
@@end
@@if not isTX:
			<div class="mui-tabs__pane" id="pane-justified-3">
				<div class="mui-panel">
					<div id="model_tab">
						<h2>PWM Output</h2>
						Set PWM output mode and failsafe positions.
						<ul>
							<li><b>Output:</b> Receiver output pin</li>
							<li><b>Features:</b> If an output is capable of supporting another function, that is indicated here</li>
							<li><b>Mode:</b> Output frequency, 10KHz 0-100% duty cycle, binary On/Off, DShot, Serial, or I2C (some options are pin dependant)</li>
							<ul>
								<li>When enabling serial pins, be sure to select the <b>Serial Protocol</b> below and <b>UART baud</b> on the <b>Options</b> tab</li>
							</ul>
							<li><b>Input:</b> Input channel from the handset</li>
							<li><b>Invert:</b> Invert input channel position</li>
							<li><b>750us:</b> Use half pulse width (494-1006us) with center 750us instead of 988-2012us</li>
							<li><b>Failsafe</b>
								<ul>
									<li>"Set Position" sets the servo to an absolute "Failsafe Pos"
										<ul>
											<li>Does not use "Invert" flag</li>
											<li>Value will be halved if "750us" flag is set</li>
											<li>Will be converted to binary for "On/Off" mode (>1500us = HIGH)</li>
										</ul>
									</li>
									<li>"No Pulses" stops sending pulses
										<ul>
											<li>Unpowers servos</li>
											<li>May disarm ESCs</li>
										</ul>
									</li>
									<li>"Last Position" continues sending last received channel position</li>
								</ul>
							</li>
						</ul>
						<form action="/pwm" id="pwm" method="POST">
						</form>
					</div>
					<form class="mui-form" action='/config' id='config' method='POST'>
						<h2>Binding Phrase</h2>
						<div class="mui-select">
							<select id="vbind" name="vbind">
								<option value="0">Persistent (Default) - Bind information is stored across reboots</option>
								<option value="1">Volatile - Never store bind information across reboots</option>
								<option value="2">Returnable - Unbinding a receiver reverts to flashed binding phrase</option>
								<option value="3">Administered - Binding information can only be edited through web UI</option>
							</select>
							<label for="vbind">Binding storage</label>
						</div>
						<div id="bindphrase">
							Enter a new binding phrase to replace the current binding information.
							This will persist across reboots, but <b>will be reset</b> if the firmware is flashed with a binding phrase.
							Note: The Binding phrase is not remembered, it is a temporary field used to generate the binding UID.
							<br/><br/>
							<div class="mui-textfield">
								<input type="text" id="phrase" name="phrase" placeholder="Binding Phrase" />
								<label for="phrase">Binding Phrase</label>
							</div>
							<div class="mui-textfield">
								<label for="uid"><span id="uid-text"></span></label>
								<span class="badge" id="uid-type"></span>
								<input size="40" id="uid" name="uid" type="text" class="array" readonly/>
							</div>
						</div>
						<div id="serial-config">
							<h2>Serial Protocol</h2>
							Set the protocol used to communicate with the flight controller or other external devices.
							<br/><br/>
							<div class="mui-select">
								<select id='serial-protocol' name='serial-protocol'>
									<option value='0'>CRSF</option>
									<option value='1'>Inverted CRSF</option>
									<option value='2'>SBUS</option>
									<option value='3'>Inverted SBUS</option>
									<option value='4'>SUMD</option>
									<option value='5'>DJI RS Pro</option>
									<option value='6'>HoTT Telemetry</option>
									<option value='7'>MAVLINK</option>
								</select>
								<label for='serial-protocol'>Serial Protocol</label>
							</div>
						</div>
						<div id="serial1-config">
							<div class="mui-select">
								<select id='serial1-protocol' name='serial1-protocol'>
									<option value='0'>Off</option>
									<option value='1'>CRSF</option>
									<option value='2'>Inverted CRSF</option>
									<option value='3'>SBUS</option>
									<option value='4'>Inverted SBUS</option>
									<option value='5'>SUMD</option>
									<option value='6'>DJI RS Pro</option>
									<option value='7'>HoTT Telemetry</option>
									<option value='8'>Tramp</option>
									<option value='9'>SmartAudio</option>
								</select>
								<label for='serial1-protocol'>Serial2 Protocol</label>
							</div>
						</div>
						<div id="sbus-config" style="display: none;">
							<h2>SBUS Failsafe</h2>
							Set the failsafe behaviour when using the SBUS protocol:<br/>
							<ul>
								<li>"No Pulses" stops sending SBUS data when a connection to the transmitter is lost</li>
								<li>"Last Position" continues to send the last received channel data along with the FAILSAFE bit set</li>
							</ul>
							<br/>
							<div class="mui-select">
								<select id='sbus-failsafe' name='serial-failsafe'>
									<option value='0'>No Pulses</option>
									<option value='1'>Last Position</option>
								</select>
								<label for="sbus-failsafe">SBUS Failsafe</label>
							</div>
						</div>

						<h2>Model Match</h2>
						Specify the 'Receiver' number in OpenTX/EdgeTX model setup page and turn on the 'Model Match'
						in the ExpressLRS Lua script for that model. 'Model Match' is between 0 and 63 inclusive.
						<br/><br/>
						<div class="mui-checkbox">
							<input id='model-match' name='model-match' type='checkbox'/>
							<label for="model-match">Enable Model Match</label>
						</div>
						<div class="mui-textfield" id="modelNum">
							<input id='modelid' type='text' name='modelid' value="255" required/>
							<label for="modelid">Model ID</label>
						</div>
						<h2>Force telemetry off</h2>
						When running multiple receivers simultaneously from the same TX (to increase the number of PWM servo outputs), there can be at most one receiver with telemetry enabled.<br>Enable this option to ignore the "Telem Ratio" setting on the TX and never send telemetry from this receiver.
						<br/><br/>
						<div class="mui-checkbox">
							<input id='force-tlm' name='force-tlm' type='checkbox' value="1"/>
							<label for="force-tlm">Force telemetry OFF on this receiver</label>
						</div>
						<button type='submit' class="mui-btn mui-btn--small mui-btn--primary">Save</button>
					</form>
				</div>
				<div class="mui-panel">
					<a id="reset-model" href="#">Reset all model settings to defaults (includes binding).</a>
				</div>
			</div>
@@end

			<div class="mui-tabs__pane" id="pane-justified-4">
				<div class="mui-panel">
					<h2 id="apmode" style="display:none;">Currently in Access Point mode</h2>
					<h2 id="stamode" style="display:none;">Current Home network: <span id="ssid"></span></h2>
					Here you can join a network and it will be saved as your Home network. When you enable WiFi in range of your Home network,
					ExpressLRS will automatically connect to it. In Access Point (AP) mode, the network name is ExpressLRS TX or ExpressLRS RX
					with password "expresslrs".
					<br/>
					<br/>
					<form id="sethome" method="POST" autocomplete="off" class="mui-form">
						<div class="mui-radio">
							<input type="radio" id="nt0" name="networktype" value="0" checked>
							<label for="nt0">Set new Home network</label>
						</div>
						<div class="mui-radio">
							<input type="radio" id="nt1" name="networktype" value="1">
							<label for="nt1">One-time connect to network, retain Home network setting</label>
						</div>
						<div class="mui-radio">
							<input type="radio" id="nt2" name="networktype" value="2">
							<label for="nt2">Start AP mode, retain Home network setting</label>
						</div>
						<div class="mui-radio">
							<input type="radio" id="nt3" name="networktype" value="3">
							<label for="nt3">Forget Home network setting, always use AP mode</label>
						</div>
						<br/>
						<div id="credentials">
							<div class="autocomplete mui-textfield" style="position:relative;">
								<div id="loader" style="position:absolute;right:0;width: 28px;height: 28px;" class="loader"></div>
								<input id="network" type="text" name="network" placeholder="SSID"/>
								<label for="network">WiFi SSID</label>
							</div>
							<div class="mui-textfield">
								<input size='64' id='password' name='password' type='password'/>
								<label for="password">WiFi password</label>
							</div>
						</div>
						<button type="submit" class="mui-btn mui-btn--primary">Confirm</button>
					</form>
				</div>
				<div class="mui-panel">
					<a id="connect" href="#">Connect to Home network: <span id="homenet"></span></a>
				</div>
			</div>
		</div>
	</div>
	@@include("footer-template.html")
</body>
<script src="mui.js"></script>
<script src="scan.js"></script>
</html>
