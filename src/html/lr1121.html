@@require(PLATFORM, VERSION)
<!DOCTYPE HTML>
<html lang="en">

<head>
	<title>Welcome to your ExpressLRS System</title>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<link rel="stylesheet" href="elrs.css" />
</head>

<body>
	<header class="mui-appbar mui--z1 mui--text-center elrs-header">
		@@include("logo-template.svg")
		<h1><b>ExpressLRS</b></h1>
		<span id="product_name"></span><br/>
		<b>Firmware Rev. </b>@@{VERSION} <span id="reg_domain"></span>
	</header>
	<br>
	<div class="mui-container-fluid mui-col-sm-10 mui-col-sm-offset-1">
		<div class="mui-panel">
			<div id="radios" style="display: none;">
				<div class="mui-radio">
					<label>
						<input type="radio"
							   name="optionsRadio"
							   value="1"
							   checked>
						Update Radio 1
					</label>
				</div>
				<div class="mui-radio">
					<label>
						<input type="radio"
							   name="optionsRadio"
							   value="2">
						Update Radio 2
					</label>
				</div>
			</div>
			<label>Upload LR1121 firmware binary:</label>
			<button id="upload_btn" class="mui-btn mui-btn--small mui-btn--primary upload">
				<label>
					Upload
					<input type="file" id="firmware_file" name="update[]" />
				</label>
			</button>
			<div id="filedrag">or drop files here</div>
			<br/>
			<h3 id="status"></h3>
			<progress id="progressBar" value="0" max="100" style="width:100%;"></progress>
		</div>
		<div class="mui-panel">
			<table class="mui-table mui-table--bordered">
				<thead>
					<tr><th>Parameter</th><th>Radio 1</th><th>Radio 2</th></tr>
				</thead>
				<tbody>
					<tr><td>Type</td><td><span id="radio_type1"></span></td><td><span id="radio_type2"></span></td></tr>
					<tr><td>Hardware</td><td><span id="radio_hardware1"></span></td><td><span id="radio_hardware2"></span></td></tr>
					<tr><td>Firmware</td><td><span id="radio_firmware1"></span></td><td><span id="radio_firmware2"></span></td></tr>
<!--				<tr><td>PIN</td><td><span id="radio_pin1"></span></td><td><span id="radio_pin2"></span></td></tr>-->
<!--				<tr><td>Chip EUI</td><td><span id="radio_ceui1"></span></td><td><span id="radio_ceui2"></span></td></tr>-->
<!--				<tr><td>Join EUI</td><td><span id="radio_jeui1"></span></td><td><span id="radio_jeui2"></span></td></tr>-->
				</tbody>
			</table>
		</div>
	</div>
</body>
<script src="lr1121.js"></script>
</html>
