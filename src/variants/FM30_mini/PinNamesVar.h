/* Alternate pin name */
PA_0_ALT1  = PA_0  | ALT1,
PA_0_ALT2  = PA_0  | ALT2,
PA_1_ALT1  = PA_1  | ALT1,
PA_1_ALT2  = PA_1  | ALT2,
PA_1_ALT3  = PA_1  | ALT3,
PA_2_ALT1  = PA_2  | ALT1,
PA_2_ALT2  = PA_2  | ALT2,
PA_2_ALT3  = PA_2  | ALT3,
PA_3_ALT1  = PA_3  | ALT1,
PA_3_ALT2  = PA_3  | ALT2,
PA_3_ALT3  = PA_3  | ALT3,
PA_4_ALT1  = PA_4  | ALT1,
PA_5_ALT1  = PA_5  | ALT1,
PA_5_ALT2  = PA_5  | ALT2,
PA_6_ALT1  = PA_6  | ALT1,
PA_6_ALT2  = PA_6  | ALT2,
PA_9_ALT1  = PA_9  | ALT1,
PA_10_ALT1 = PA_10 | ALT1,
PA_11_ALT1 = PA_11 | ALT1,
PA_12_ALT1 = PA_12 | ALT1,
PA_12_ALT2 = PA_12 | ALT2,
PA_13_ALT1 = PA_13 | ALT1,
PA_13_ALT2 = PA_13 | ALT2,
PA_15_ALT1 = PA_15 | ALT1,
PB_0_ALT1  = PB_0  | ALT1,
PB_3_ALT1  = PB_3  | ALT1,
PB_4_ALT1  = PB_4  | ALT1,
PB_4_ALT2  = PB_4  | ALT2,
PB_5_ALT1  = PB_5  | ALT1,
PB_6_ALT1  = PB_6  | ALT1,
PB_6_ALT2  = PB_6  | ALT2,
PB_6_ALT3  = PB_6  | ALT3,
PB_6_ALT4  = PB_6  | ALT4,
PB_7_ALT1  = PB_7  | ALT1,
PB_7_ALT2  = PB_7  | ALT2,
PB_7_ALT3  = PB_7  | ALT3,
PB_7_ALT4  = PB_7  | ALT4,
PB_8_ALT1  = PB_8  | ALT1,
PB_8_ALT2  = PB_8  | ALT2,
PB_9_ALT1  = PB_9  | ALT1,
PB_9_ALT2  = PB_9  | ALT2,
PB_14_ALT1 = PB_14 | ALT1,
PB_15_ALT1 = PB_15 | ALT1,
PB_15_ALT2 = PB_15 | ALT2,

/* SYS_WKUP */
#ifdef PWR_WAKEUP_PIN1
  SYS_WKUP1 = PA_0,
#endif
#ifdef PWR_WAKEUP_PIN2
  SYS_WKUP2 = PC_13,
#endif
#ifdef PWR_WAKEUP_PIN3
  SYS_WKUP3 = NC,
#endif
#ifdef PWR_WAKEUP_PIN4
  SYS_WKUP4 = NC,
#endif
#ifdef PWR_WAKEUP_PIN5
  SYS_WKUP5 = NC,
#endif
#ifdef PWR_WAKEUP_PIN6
  SYS_WKUP6 = NC,
#endif
#ifdef PWR_WAKEUP_PIN7
  SYS_WKUP7 = NC,
#endif
#ifdef PWR_WAKEUP_PIN8
  SYS_WKUP8 = NC,
#endif

/* USB */
#ifdef USBCON
  USB_DM = PA_11,
  USB_DP = PA_12,
#endif
