{"build": {"core": "stm32", "cpu": "cortex-m4", "extra_flags": "-DSTM32L4xx -DSTM32L43x -DSTM32L433xx", "f_cpu": "80000000L", "ldscript": "variants/r9mx/ldscript.ld", "mcu": "stm32l433cby6", "product_line": "STM32L433xx", "variants_dir": "variants", "variant": "r9mx"}, "debug": {"default_tools": ["stlink"], "jlink_device": "STM32L433CB", "onboard_tools": ["stlink"], "openocd_board": "st_nucleo_l4", "svd_path": "STM32L4x3.svd"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "cmsis", "mbed", "stm32cube"], "name": "FrSky R9MX receiver", "upload": {"maximum_ram_size": 65536, "maximum_size": 98304, "protocol": "custom", "protocols": ["jlink", "stlink", "blackmagic", "mbed"]}, "url": "https://www.st.com/en/microcontrollers-microprocessors/stm32l433cb.html", "vendor": "ST"}