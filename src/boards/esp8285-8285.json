{"comment": "esp8285 variant for ExpressLRS that uses variant=esp8285 instead of variant=generic", "build": {"arduino": {"ldscript": "eagle.flash.1m256.ld"}, "core": "esp8266", "extra_flags": "-DESP8266 -<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_ARCH_ESP8266 -<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_ESP8266_ESP01", "f_cpu": "80000000L", "f_flash": "40000000L", "flash_mode": "dout", "mcu": "esp8266", "variant": "esp8285"}, "connectivity": ["wifi"], "frameworks": ["a<PERSON><PERSON><PERSON>", "esp8266-rtos-sdk", "esp8266-nonos-sdk"], "name": "Generic ESP8285 Module", "upload": {"maximum_ram_size": 81920, "maximum_size": 1048576, "require_upload_port": true, "resetmethod": "ck", "speed": 115200}, "url": "http://www.esp8266.com/wiki/doku.php?id=esp8266-module-family", "vendor": "E<PERSON>ress<PERSON>"}