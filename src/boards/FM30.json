{"comment": "STM32F103C8 definition for use with 16MHz crystal (DIV2 prescaler)", "build": {"core": "stm32", "extra_flags": "-DSTM32F103xB", "cpu": "cortex-m3", "f_cpu": "72000000L", "ldscript": "variants/FM30/ldscript.ld", "mcu": "stm32f103c8t6", "variants_dir": "variants", "variant": "FM30"}, "debug": {"default_tools": ["stlink"], "jlink_device": "STM32F103C8", "openocd_extra_args": ["-c", "reset_config none"], "openocd_target": "stm32f1x", "svd_path": "STM32F103xx.svd"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "mbed", "cmsis", "libopencm3", "stm32cube"], "name": "FM30", "upload": {"maximum_ram_size": 20480, "maximum_size": 65536, "protocol": "stlink", "protocols": ["jlink", "cmsis-dap", "stlink", "blackmagic", "mbed", "dfu"]}, "url": "http://www.st.com/content/st_com/en/products/microcontrollers/stm32-32-bit-arm-cortex-mcus/stm32f1-series/stm32f103/stm32f103c8.html", "vendor": "Generic"}