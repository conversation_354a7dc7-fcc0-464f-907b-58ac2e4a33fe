; ExpressLRS PlatformIO Project Configuration File

[platformio]
extra_configs =
	# keep common first
	targets/common.ini
	# defined one by one to maintain the order
	targets/unified.ini
	targets/diy_900.ini
	targets/diy_2400.ini
	targets/frsky.ini
	targets/happymodel_900.ini
	targets/happymodel_2400.ini
	targets/imrc.ini
	targets/namimnorc_900.ini
	targets/namimnorc_2400.ini
	targets/siyi.ini

# ------------------------- TARGET ENV DEFINITIONS -----------------

[env:native]
platform = native
framework =
test_ignore = test_embedded
lib_ignore = BUTTON, DAC, LQCALC, LBT, SPIEx, PWM, WIFI, TCPSOCKET, LR1121Driver
build_src_filter = ${common_env_data.build_src_filter} -<ESP32*.*> -<STM32*.*> -<ESP8*.*> -<tx_*.cpp> -<rx_*.cpp> -<common.*> -<config.*>
build_flags =
	-std=c++11
	-Iinclude
	-D PROGMEM=""
	-D UNIT_TEST=1
	-D TARGET_NATIVE
	-D CRSF_RX_MODULE
	-D CRSF_TX_MODULE
