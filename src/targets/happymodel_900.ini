
# ********************************
# Transmitter targets
# ********************************

[env:HappyModel_TX_ES915TX_via_STLINK]
extends = env_common_stm32, radio_900
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
		${common_env_data.build_flags_tx}
	-include target/HappyModel_TX_ES915TX.h
	-flto
	-D HSE_VALUE=12000000U
	-D VECT_TAB_OFFSET=0x4000U
board_build.ldscript = variants/R9M_ldscript.ld
board_build.flash_offset = 0x4000
build_src_filter = ${env_common_stm32.build_src_filter} -<rx_*.cpp>
upload_flags =
	BOOTLOADER=bootloader/r9m_bootloader.bin
	VECT_OFFSET=0x4000

[env:HappyModel_TX_ES915TX_via_stock_BL]
extends = env:HappyModel_TX_ES915TX_via_STLINK

[env:HappyModel_TX_ES915TX_via_WIFI]
extends = env:HappyModel_TX_ES915TX_via_STLINK

# ********************************
# Receiver targets
# ********************************

[env:HappyModel_RX_ES915RX_via_STLINK]
extends = env_common_stm32, radio_900
board = R9MM
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Frsky_RX_R9M.h
	-D HSE_VALUE=24000000U
	-DVECT_TAB_OFFSET=0x08008000U
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>
upload_flags =
    BOOTLOADER=bootloader/r9mm_bootloader.bin
    VECT_OFFSET=0x8000

[env:HappyModel_RX_ES915RX_via_BetaflightPassthrough]
extends = env:HappyModel_RX_ES915RX_via_STLINK
