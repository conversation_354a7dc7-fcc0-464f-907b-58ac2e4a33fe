
# ********************************
# Transmitter targets
# ********************************

[env:NamimnoRC_FLASH_2400_TX_via_STLINK]
extends = env_common_stm32, radio_2400
build_flags =
	${env_common_stm32.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_tx}
	-flto
	-D HSE_VALUE=12000000U
	-D VECT_TAB_OFFSET=0x4000U
	-include target/NamimnoRC_FLASH_2400_TX.h
board_build.ldscript = variants/NamimnoRC_Alpha.ld
build_src_filter = ${env_common_stm32.build_src_filter} -<rx_*.cpp>
upload_flags =
    BOOTLOADER=bootloader/namimnorc/tx/namimnorc_tx_bootloader.bin
    VECT_OFFSET=0x4000

[env:NamimnoRC_FLASH_2400_TX_via_WIFI]
extends = env:NamimnoRC_FLASH_2400_TX_via_STLINK

# ********************************
# Receiver targets
# ********************************

[env:NamimnoRC_FLASH_2400_RX_via_STLINK]
extends = env_common_stm32, radio_2400
build_flags =
	${env_common_stm32.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_rx}
	-D HSE_VALUE=12000000U
	-D VECT_TAB_OFFSET=0x8000U
	-include target/NamimnoRC_FLASH_2400_RX_STM32.h
board_build.ldscript = variants/R9MM/R9MM_ldscript.ld
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>
upload_flags =
    BOOTLOADER=bootloader/namimnorc/rx/flash_2400_bootloader.bin
    VECT_OFFSET=0x8000

[env:NamimnoRC_FLASH_2400_RX_via_BetaflightPassthrough]
extends = env:NamimnoRC_FLASH_2400_RX_via_STLINK
