
# ********************************
# Transmitter targets
# ********************************

# ********************************
# Receiver targets
# ********************************

[env:DIY_2400_RX_STM32_CCG_Nano_v0_5_via_STLINK]
extends = env_common_stm32, radio_2400
board = l432kb
# max size = 131072 - 0x4000 = 114688
board_upload.maximum_size = 114688
build_flags =
	${env_common_stm32.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_rx}
	-include target/DIY_2400_RX_STM32_CCG_Nano_v0_5.h
	-D HAL_RTC_MODULE_DISABLED=1
	-D HAL_ADC_MODULE_DISABLED=1
	-D DISABLE_GENERIC_SERIALUSB=1
	-D HSI_VALUE=16000000U
	-D USE_HSI=1
	-O3
	-D FLASH_BASE_ADDRESS='((uint32_t)((FLASH_SIZE) - FLASH_PAGE_SIZE))'
	-D VECT_TAB_OFFSET=0x4000U
    -D FLASH_APP_OFFSET=0x4000U
    -Wl,--defsym=FLASH_APP_OFFSET=16K
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>
upload_flags =
    BOOTLOADER=bootloader/sx1280_rx_nano_pcb_v0.5_bootloader.bin
    VECT_OFFSET=0x4000

[env:DIY_2400_RX_STM32_CCG_Nano_v0_5_via_BetaflightPassthrough]
extends = env:DIY_2400_RX_STM32_CCG_Nano_v0_5_via_STLINK
