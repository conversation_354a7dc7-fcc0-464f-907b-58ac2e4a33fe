# ********************************
# Transmitter targets
# ********************************

[env:Unified_ESP32_2400_TX_via_ETX]
extends = env_common_esp32, radio_2400
build_flags =
	${env_common_esp32.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Unified_ESP32_TX.h
	-D VTABLES_IN_FLASH=1
	-O2
build_src_filter = ${env_common_esp32.build_src_filter} -<rx_*.cpp>
lib_deps =
    ${env_common_esp32.tft_lib_deps}
    ${env_common_esp32.oled_lib_deps}
	SPIFFS
upload_speed = 460800
monitor_speed = 420000

[env:Unified_ESP32_2400_TX_via_UART]
extends = env:Unified_ESP32_2400_TX_via_ETX

[env:Unified_ESP32_2400_TX_via_WIFI]
extends = env:Unified_ESP32_2400_TX_via_ETX

[env:Unified_ESP32_900_TX_via_ETX]
extends = env_common_esp32, radio_900
build_flags =
	${env_common_esp32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Unified_ESP32_TX.h
	-D VTABLES_IN_FLASH=1
	-O2
build_src_filter = ${env_common_esp32.build_src_filter} -<rx_*.cpp>
lib_deps =
    ${env_common_esp32.tft_lib_deps}
    ${env_common_esp32.oled_lib_deps}
	SPIFFS
upload_speed = 460800
monitor_speed = 460800

[env:Unified_ESP32_900_TX_via_UART]
extends = env:Unified_ESP32_900_TX_via_ETX

[env:Unified_ESP32_900_TX_via_WIFI]
extends = env:Unified_ESP32_900_TX_via_ETX

[env:Unified_ESP8285_900_TX_via_UART]
extends = env_common_esp82xx, radio_900
board_build.f_cpu = 80000000L
build_flags =
	${env_common_esp82xx.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Unified_ESP8285_TX.h
build_src_filter = ${env_common_esp82xx.build_src_filter} -<rx_*.cpp>
board_build.ldscript = ./elrs.flash.1m64.ld

[env:Unified_ESP8285_900_TX_via_WIFI]
extends = env:Unified_ESP8285_900_TX_via_UART

[env:Unified_ESP32_LR1121_TX_via_ETX]
extends = env_common_esp32, radio_LR1121
build_flags =
	${env_common_esp32.build_flags}
	${radio_LR1121.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Unified_ESP32_TX.h
	-D VTABLES_IN_FLASH=1
	-O2
build_src_filter = ${env_common_esp32.build_src_filter} -<rx_*.cpp>
lib_deps =
    ${env_common_esp32.tft_lib_deps}
    ${env_common_esp32.oled_lib_deps}
	SPIFFS
upload_speed = 460800
monitor_speed = 460800

[env:Unified_ESP32_LR1121_TX_via_UART]
extends = env:Unified_ESP32_LR1121_TX_via_ETX

[env:Unified_ESP32_LR1121_TX_via_WIFI]
extends = env:Unified_ESP32_LR1121_TX_via_ETX

[env:Unified_ESP8285_2400_TX_via_UART]
extends = env_common_esp82xx, radio_2400
board_build.f_cpu = 80000000L
build_flags =
	${env_common_esp82xx.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Unified_ESP8285_TX.h
build_src_filter = ${env_common_esp82xx.build_src_filter} -<rx_*.cpp>
board_build.ldscript = ./elrs.flash.1m64.ld

[env:Unified_ESP8285_2400_TX_via_WIFI]
extends = env:Unified_ESP8285_2400_TX_via_UART

[env:Unified_ESP32S3_2400_TX_via_ETX]
extends = env_common_esp32s3tx, radio_2400
build_flags =
	${env_common_esp32s3tx.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Unified_ESP32_TX.h
	-D VTABLES_IN_FLASH=1
	-O2
build_src_filter = ${env_common_esp32s3tx.build_src_filter} -<rx_*.cpp>
lib_deps =
    ${env_common_esp32s3tx.tft_lib_deps}
    ${env_common_esp32s3tx.oled_lib_deps}
	SPIFFS
upload_speed = 460800
monitor_speed = 420000

[env:Unified_ESP32S3_2400_TX_via_UART]
extends = env:Unified_ESP32S3_2400_TX_via_ETX

[env:Unified_ESP32S3_2400_TX_via_WIFI]
extends = env:Unified_ESP32S3_2400_TX_via_ETX

[env:Unified_ESP32S3_900_TX_via_ETX]
extends = env_common_esp32s3tx, radio_900
build_flags =
	${env_common_esp32s3tx.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Unified_ESP32_TX.h
	-D VTABLES_IN_FLASH=1
	-O2
build_src_filter = ${env_common_esp32s3tx.build_src_filter} -<rx_*.cpp>
lib_deps =
    ${env_common_esp32s3tx.tft_lib_deps}
    ${env_common_esp32s3tx.oled_lib_deps}
	SPIFFS
upload_speed = 460800
monitor_speed = 460800

[env:Unified_ESP32S3_900_TX_via_UART]
extends = env:Unified_ESP32S3_900_TX_via_ETX

[env:Unified_ESP32S3_900_TX_via_WIFI]
extends = env:Unified_ESP32S3_900_TX_via_ETX

[env:Unified_ESP32S3_LR1121_TX_via_ETX]
extends = env_common_esp32s3tx, radio_LR1121
build_flags =
	${env_common_esp32s3tx.build_flags}
	${radio_LR1121.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Unified_ESP32_TX.h
	-D VTABLES_IN_FLASH=1
	-O2
build_src_filter = ${env_common_esp32s3tx.build_src_filter} -<rx_*.cpp>
lib_deps =
    ${env_common_esp32s3tx.tft_lib_deps}
    ${env_common_esp32s3tx.oled_lib_deps}
	SPIFFS
upload_speed = 460800
monitor_speed = 460800

[env:Unified_ESP32S3_LR1121_TX_via_UART]
extends = env:Unified_ESP32S3_LR1121_TX_via_ETX

[env:Unified_ESP32S3_LR1121_TX_via_WIFI]
extends = env:Unified_ESP32S3_LR1121_TX_via_ETX

# ********************************
# Receiver targets
# ********************************

[env:Unified_ESP8285_2400_RX_via_UART]
extends = env_common_esp82xx, radio_2400
build_flags =
	${env_common_esp82xx.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp82xx.build_src_filter} -<tx_*.cpp>
board_build.ldscript = ./elrs.flash.1m64.ld

[env:Unified_ESP8285_2400_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP8285_2400_RX_via_UART

[env:Unified_ESP8285_2400_RX_via_WIFI]
extends = env:Unified_ESP8285_2400_RX_via_UART

[env:Unified_ESP8285_900_RX_via_UART]
extends = env_common_esp82xx, radio_900
build_flags =
	${env_common_esp82xx.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp82xx.build_src_filter} -<tx_*.cpp>
board_build.ldscript = ./elrs.flash.1m64.ld

[env:Unified_ESP8285_900_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP8285_900_RX_via_UART

[env:Unified_ESP8285_900_RX_via_WIFI]
extends = env:Unified_ESP8285_900_RX_via_UART

[env:Unified_ESP32_2400_RX_via_UART]
extends = env_common_esp32rx, radio_2400
build_flags =
	${env_common_esp32rx.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp32rx.build_src_filter} -<tx_*.cpp>

[env:Unified_ESP32_2400_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP32_2400_RX_via_UART

[env:Unified_ESP32_2400_RX_via_WIFI]
extends = env:Unified_ESP32_2400_RX_via_UART

[env:Unified_ESP32_900_RX_via_UART]
extends = env_common_esp32rx, radio_900
build_flags =
	${env_common_esp32rx.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp32rx.build_src_filter} -<tx_*.cpp>

[env:Unified_ESP32_900_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP32_900_RX_via_UART

[env:Unified_ESP32_900_RX_via_WIFI]
extends = env:Unified_ESP32_900_RX_via_UART

[env:Unified_ESP32_LR1121_RX_via_UART]
extends = env_common_esp32rx, radio_LR1121
build_flags =
	${env_common_esp32rx.build_flags}
	${radio_LR1121.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp32rx.build_src_filter} -<tx_*.cpp>

[env:Unified_ESP32_LR1121_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP32_LR1121_RX_via_UART

[env:Unified_ESP32_LR1121_RX_via_WIFI]
extends = env:Unified_ESP32_LR1121_RX_via_UART

# ESP32-S3

[env:Unified_ESP32S3_2400_RX_via_UART]
extends = env_common_esp32s3rx, radio_2400
build_flags =
	${env_common_esp32s3rx.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp32rx.build_src_filter} -<tx_*.cpp>

[env:Unified_ESP32S3_2400_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP32S3_2400_RX_via_UART

[env:Unified_ESP32S3_2400_RX_via_WIFI]
extends = env:Unified_ESP32S3_2400_RX_via_UART

[env:Unified_ESP32S3_900_RX_via_UART]
extends = env_common_esp32s3rx, radio_900
build_flags =
	${env_common_esp32s3rx.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp32rx.build_src_filter} -<tx_*.cpp>

[env:Unified_ESP32S3_900_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP32S3_900_RX_via_UART

[env:Unified_ESP32S3_900_RX_via_WIFI]
extends = env:Unified_ESP32S3_900_RX_via_UART

[env:Unified_ESP32S3_LR1121_RX_via_UART]
extends = env_common_esp32s3rx, radio_LR1121
build_flags =
	${env_common_esp32s3rx.build_flags}
	${radio_LR1121.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp32s3rx.build_src_filter} -<tx_*.cpp>

[env:Unified_ESP32S3_LR1121_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP32S3_LR1121_RX_via_UART

[env:Unified_ESP32S3_LR1121_RX_via_WIFI]
extends = env:Unified_ESP32S3_LR1121_RX_via_UART

# ESP32-C3

[env:Unified_ESP32C3_2400_RX_via_UART]
extends = env_common_esp32c3rx, radio_2400
build_flags =
	${env_common_esp32c3rx.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp32rx.build_src_filter} -<tx_*.cpp>

[env:Unified_ESP32C3_2400_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP32C3_2400_RX_via_UART

[env:Unified_ESP32C3_2400_RX_via_WIFI]
extends = env:Unified_ESP32C3_2400_RX_via_UART

[env:Unified_ESP32C3_900_RX_via_UART]
extends = env_common_esp32c3rx, radio_900
build_flags =
	${env_common_esp32c3rx.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp32rx.build_src_filter} -<tx_*.cpp>

[env:Unified_ESP32C3_900_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP32C3_900_RX_via_UART

[env:Unified_ESP32C3_900_RX_via_WIFI]
extends = env:Unified_ESP32C3_900_RX_via_UART

[env:Unified_ESP32C3_LR1121_RX_via_UART]
extends = env_common_esp32c3rx, radio_LR1121
build_flags =
	${env_common_esp32c3rx.build_flags}
	${radio_LR1121.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Unified_ESP_RX.h
build_src_filter = ${env_common_esp32rx.build_src_filter} -<tx_*.cpp>

[env:Unified_ESP32C3_LR1121_RX_via_BetaflightPassthrough]
extends = env:Unified_ESP32C3_LR1121_RX_via_UART

[env:Unified_ESP32C3_LR1121_RX_via_WIFI]
extends = env:Unified_ESP32C3_LR1121_RX_via_UART
