
# ------------------------- COMMON ENV DEFINITIONS -----------------
[env]
#upload_port =
#upload_speed =
framework = arduino
extra_scripts =
	pre:python/build_flags.py
	python/build_env_setup.py
monitor_dtr = 0
monitor_rts = 0


[common_env_data]
build_src_filter = +<*> -<.git/> -<svn/> -<example/> -<examples/> -<test/> -<tests/> -<*.py> -<*test*.*>
build_flags = -Wall -Iinclude
build_flags_tx = -DTARGET_TX=1 ${common_env_data.build_flags}
build_flags_rx = -DTARGET_RX=1 ${common_env_data.build_flags}

mavlink_lib_dep = https://github.com/mavlink/c_library_v2.git#e54a8d2e8cf7985e689ad1c8c8f37dc0800ea87b

[radio_900]
build_flags = -DRADIO_SX127X=1
lib_ignore = 
	LR1121Driver
	SX1280Driver

[radio_LR1121]
build_flags = -DRADIO_LR1121=1
lib_ignore = 
	SX127xDriver
	SX1280Driver

[radio_2400]
build_flags = -DRADIO_SX128X=1
lib_ignore = 
	SX127xDriver
	LR1121Driver

# ------------------------- COMMON ESP32 DEFINITIONS -----------------

[env_common_esp32]
platform = espressif32@6.4.0
board = esp32dev
board_build.partitions = min_spiffs.csv
upload_speed = 460800
monitor_speed = 460800
upload_resetmethod = nodemcu
board_build.f_cpu = 240000000L
extra_scripts =
	${env.extra_scripts}
	pre:python/build_html.py
build_flags =
	-D PLATFORM_ESP32=1
	-D CONFIG_TCPIP_LWIP=1
	-D BEARSSL_SSL_BASIC
	-D CONFIG_DISABLE_HAL_LOCKS=1
	-I ${PROJECTSRC_DIR}/hal
build_src_filter = ${common_env_data.build_src_filter} -<ESP8266*.*> -<STM32*.*>
lib_deps =
	esphome/AsyncTCP-esphome @ 2.0.1 # use specific version - an update to this library breaks the build
	ottowinter/ESPAsyncWebServer-esphome @ 3.0.0
	h2zero/NimBLE-Arduino @ 1.4.1
	lemmingdev/ESP32-BLE-Gamepad @ 0.5.2
	bblanchon/ArduinoJson @ 7.0.4
	${common_env_data.mavlink_lib_dep}
oled_lib_deps =
	${env_common_esp32.lib_deps}
	olikraus/U8g2 @ 2.34.4
tft_lib_deps =
	${env_common_esp32.lib_deps}
	moononournation/GFX Library for Arduino @ 1.2.8

[env_common_esp32s3tx]
platform = espressif32@6.4.0
extends = env_common_esp32
board = esp32-s3-devkitc-1
build_flags =
	${env_common_esp32.build_flags}
	-D PLATFORM_ESP32_S3=1
	-D ARDUINO_USB_CDC_ON_BOOT

[env_common_esp32rx]
platform = espressif32@6.4.0
board = esp32dev
board_build.partitions = min_spiffs.csv
upload_speed = 460800
monitor_speed = 420000
upload_resetmethod = nodemcu
board_build.f_cpu = 240000000L
extra_scripts =
	${env.extra_scripts}
	pre:python/build_html.py
build_flags =
	-D PLATFORM_ESP32=1
	-D CONFIG_TCPIP_LWIP=1
	-D BEARSSL_SSL_BASIC
	-D USE_MSP_WIFI=1
	-D CONFIG_DISABLE_HAL_LOCKS=1
	-I ${PROJECTSRC_DIR}/hal
build_src_filter = ${common_env_data.build_src_filter} -<ESP8266*.*> -<STM32*.*>
lib_deps =
	esphome/AsyncTCP-esphome @ 2.0.1 # use specific version - an update to this library breaks the build
	ottowinter/ESPAsyncWebServer-esphome @ 3.0.0
	bblanchon/ArduinoJson @ 7.0.4
	${common_env_data.mavlink_lib_dep}

[env_common_esp32s3rx]
platform = espressif32@6.4.0
extends = env_common_esp32rx
board = esp32-s3-devkitc-1
build_flags =
	${env_common_esp32rx.build_flags}
	-D PLATFORM_ESP32_S3=1

[env_common_esp32c3rx]
extends = env_common_esp32rx
platform = espressif32@6.4.0
board = esp32-c3-devkitm-1
build_flags =
	${env_common_esp32rx.build_flags}
	-D PLATFORM_ESP32_C3=1
	-D ARDUINO_USB_MODE

# ------------------------- COMMON ESP82xx DEFINITIONS -----------------
[env_common_esp82xx]
platform = espressif8266@4.2.0
board = esp8285-8285
build_flags =
	-D PLATFORM_ESP8266=1
	-D VTABLES_IN_FLASH=1
	-D PIO_FRAMEWORK_ARDUINO_MMU_CACHE16_IRAM48
	-D CONFIG_TCPIP_LWIP=1
	-D BEARSSL_SSL_BASIC
	-D USE_MSP_WIFI=1
	-O2
	-I ${PROJECTSRC_DIR}/hal
board_build.f_cpu = 160000000L
board_build.ldscript = eagle.flash.1m.ld
build_src_filter = ${common_env_data.build_src_filter} -<ESP32*.*> -<STM32*.*> -<WS281B*.*>
extra_scripts =
	${env.extra_scripts}
	pre:python/build_html.py
lib_deps =
	makuna/NeoPixelBus @ 2.7.0
	ottowinter/ESPAsyncWebServer-esphome @ 3.0.0
	bblanchon/ArduinoJson @ 7.0.4
	${common_env_data.mavlink_lib_dep}
upload_speed = 460800
monitor_speed = 420000
monitor_filters = esp8266_exception_decoder
upload_resetmethod = nodemcu

# ------------------------- COMMON STM32 DEFINITIONS -----------------
[env_common_stm32]
platform = ststm32@15.1.0
board = bluepill_f103c8
build_flags =
	-D PLATFORM_STM32=1
	-Wl,-Map,"'${BUILD_DIR}/firmware.map'"
	-O2
	-I ${PROJECTSRC_DIR}/hal
	-D __FILE__='""'
	-Wno-builtin-macro-redefined
build_src_filter = ${common_env_data.build_src_filter} -<ESP32*.*> -<ESP8266*.*> -<WS281B*.*>
lib_ignore = VTXSPI, VTX, WIFI, TCPSOCKET, PWM, ServoOutput, LR1121Driver
lib_deps =
	paolop74/extEEPROM @ 3.4.1
	${common_env_data.mavlink_lib_dep}
oled_lib_deps =
	${env_common_stm32.lib_deps}
	olikraus/U8g2 @ 2.34.4
monitor_speed = 420000
