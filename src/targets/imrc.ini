
# ********************************
# Transmitter targets
# ********************************

[env:GHOST_2400_TX_via_STLINK]
extends = env_common_stm32, radio_2400
board = GHOST_TX
build_flags =
	${env_common_stm32.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_tx}
	-include target/GHOST_2400_TX.h
	-D DEBUG=1
 	-D HSE_VALUE=32000000U
	-DVECT_TAB_OFFSET=0x08004000U
	-Wl,--defsym=FLASH_APP_OFFSET=0x4000
build_src_filter = ${env_common_stm32.build_src_filter} -<rx_*.cpp>
upload_flags =
	BOOTLOADER=bootloader/ghost/ghost_tx_bootloader.bin
	VECT_OFFSET=0x4000
lib_deps =
	${env_common_stm32.oled_lib_deps}

[env:GHOST_2400_TX_LITE_via_STLINK]
extends = env:GHOST_2400_TX_via_STLINK
build_flags =
	-include target/GHOST_2400_TX_LITE.h
	${env:GHOST_2400_TX_via_STLINK.build_flags}

# ********************************
# Receiver targets
# ********************************

[env:GHOST_ATTO_2400_RX_via_STLINK]
extends = env_common_stm32, radio_2400
board = GHOST_ATTO
build_flags =
	${env_common_stm32.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_rx}
 	-include target/GHOST_ATTO_2400_RX.h
 	-D HSE_VALUE=32000000U
	-DVECT_TAB_OFFSET=0x08004000U
	-Wl,--defsym=FLASH_APP_OFFSET=0x4000
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>
lib_deps =
upload_flags =
	BOOTLOADER=bootloader/ghost/ghost_atto_bootloader.bin
	VECT_OFFSET=0x4000

[env:GHOST_ATTO_2400_RX_via_BetaflightPassthrough]
extends = env:GHOST_ATTO_2400_RX_via_STLINK
