
# ********************************
# Transmitter targets
# ********************************

[env:NamimnoRC_VOYAGER_900_TX_via_STLINK]
extends = env_common_stm32, radio_900
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_tx}
	-include target/NamimnoRC_VOYAGER_900_TX.h
	-flto
	-D HSE_VALUE=12000000U
	-D VECT_TAB_OFFSET=0x4000U
board_build.ldscript = variants/NamimnoRC_Alpha.ld
build_src_filter = ${env_common_stm32.build_src_filter} -<rx_*.cpp>
upload_flags =
    BOOTLOADER=bootloader/namimnorc/tx/namimnorc_tx_bootloader.bin
    VECT_OFFSET=0x4000
lib_deps =

[env:NamimnoRC_VOYAGER_900_TX_via_WIFI]
extends = env:NamimnoRC_VOYAGER_900_TX_via_STLINK

# ********************************
# Receiver targets
# ********************************

[env:NamimnoRC_VOYAGER_900_RX_via_STLINK]
extends = env_common_stm32, radio_900
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-D HSE_VALUE=12000000U
	-D VECT_TAB_OFFSET=0x8000U
	-include target/NamimnoRC_VOYAGER_900_RX.h
board_build.ldscript = variants/R9MM/R9MM_ldscript.ld
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>
upload_flags =
    BOOTLOADER=bootloader/namimnorc/rx/voyager_900_bootloader.bin
    VECT_OFFSET=0x8000
lib_deps =

[env:NamimnoRC_VOYAGER_900_RX_via_BetaflightPassthrough]
extends = env:NamimnoRC_VOYAGER_900_RX_via_STLINK
