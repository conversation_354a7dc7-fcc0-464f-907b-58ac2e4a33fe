
# ********************************
# Transmitter targets
# ********************************

## TODO: R9M STLINK/stock and R9M Lite targets can be merged
[env:Frsky_TX_R9M_via_STLINK]
extends = env_common_stm32, radio_900
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Frsky_TX_R9M.h
	-flto
	-D HSE_VALUE=12000000U
	-DVECT_TAB_OFFSET=0x4000U
board_build.ldscript = variants/R9M_ldscript.ld
board_build.flash_offset = 0x4000
build_src_filter = ${env_common_stm32.build_src_filter} -<rx_*.cpp>
upload_flags =
	BOOTLOADER=bootloader/r9m_bootloader.bin
	VECT_OFFSET=0x4000

[env:Frsky_TX_R9M_via_stock_BL]
extends = env:Frsky_TX_R9M_via_STLINK

[env:Frsky_TX_R9M_via_WIFI]
extends = env:Frsky_TX_R9M_via_STLINK

[env:Frsky_TX_R9M_LITE_via_STLINK]
extends = env:Frsky_TX_R9M_via_STLINK
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Frsky_TX_R9M_LITE.h
	-flto
	-D HSE_VALUE=12000000U
	-DVECT_TAB_OFFSET=0x4000U

[env:Frsky_TX_R9M_LITE_via_stock_BL]
extends = env:Frsky_TX_R9M_LITE_via_STLINK

[env:Frsky_TX_R9M_LITE_PRO_via_STLINK]
extends = env_common_stm32, radio_900
board = robotdyn_blackpill_f303cc
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_tx}
	-include target/Frsky_TX_R9M_LITE_PRO.h
	-D HSE_VALUE=12000000U
	-DVECT_TAB_OFFSET=0x8000U
board_build.ldscript = variants/R9M_Lite_Pro_ldscript.ld
board_build.flash_offset = 0x8000
upload_flags =
	BOOTLOADER=bootloader/r9m_lite_pro_bootloader.bin
	VECT_OFFSET=0x8000
build_src_filter = ${env_common_stm32.build_src_filter} -<rx_*.cpp>


# ********************************
# Receiver targets
# ********************************

[env:Frsky_RX_R9MM_R9MINI_via_STLINK]
extends = env_common_stm32, radio_900
board = R9MM
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-include target/Frsky_RX_R9M.h
	-D HSE_VALUE=24000000U
	-DVECT_TAB_OFFSET=0x08008000U
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>
upload_flags =
    BOOTLOADER=bootloader/r9mm_bootloader.bin
    VECT_OFFSET=0x8000

[env:Frsky_RX_R9MM_R9MINI_via_BetaflightPassthrough]
extends = env:Frsky_RX_R9MM_R9MINI_via_STLINK

[env:Frsky_RX_R9SLIM_via_STLINK]
extends = env:Frsky_RX_R9SLIM_via_BetaflightPassthrough
upload_flags =
    BOOTLOADER=bootloader/r9slim_no_btn_bootloader.bin
    VECT_OFFSET=0x8000

[env:Frsky_RX_R9SLIM_via_BetaflightPassthrough]
extends = env_common_stm32, radio_900
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-D TARGET_R9SLIM_RX
	-include target/Frsky_RX_R9M.h
	-D HSE_VALUE=12000000U
	-DVECT_TAB_OFFSET=0x08008000U
board_build.ldscript = variants/R9MM/R9MM_ldscript.ld
build_src_filter = ${common_env_data.build_src_filter} -<ESP32*.*> -<ESP8266*.*> -<WS281B*.*> -<tx_*.cpp>

[env:Frsky_RX_R9SLIMPLUS_via_STLINK]
extends = env:Frsky_RX_R9SLIMPLUS_via_BetaflightPassthrough
upload_flags =
    BOOTLOADER=bootloader/r9slim_plus_bootloader.bin
    VECT_OFFSET=0x8000

[env:Frsky_RX_R9SLIMPLUS_via_BetaflightPassthrough]
extends = env_common_stm32, radio_900
board_build.ldscript = variants/R9MM/R9MM_ldscript.ld
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-D TARGET_R9SLIMPLUS_RX
	-include target/Frsky_RX_R9M.h
	-D HSE_VALUE=12000000U
	-DVECT_TAB_OFFSET=0x8000U
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>

[env:Frsky_RX_R9SLIMPLUS_OTA_via_STLINK]
extends = env:Frsky_RX_R9SLIMPLUS_via_BetaflightPassthrough
board = robotdyn_blackpill_f303cc
board_build.ldscript = variants/stm32f303xx.ld
upload_flags =
    BOOTLOADER=bootloader/r9slim_plus_ota_bootloader.bin
    VECT_OFFSET=0x8000

[env:Frsky_RX_R9SLIMPLUS_OTA_via_BetaflightPassthrough]
extends = env:Frsky_RX_R9SLIMPLUS_OTA_via_STLINK

[env:Frsky_RX_R9MX_via_STLINK]
extends = env_common_stm32, radio_900
board = r9mx
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-D TARGET_R9MX_RX
	-include target/Frsky_RX_R9M.h
	-DVECT_TAB_OFFSET=0x08008000U
	-D HSI_VALUE=16000000
	-Wl,--defsym=FLASH_OFFSET=0x8000
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>
upload_flags =
	BOOTLOADER=bootloader/r9mx_bootloader.bin
	VECT_OFFSET=0x8000

[env:Frsky_RX_R9MX_via_BetaflightPassthrough]
extends = env:Frsky_RX_R9MX_via_STLINK

[env:Jumper_RX_R900MINI_via_STLINK]
extends = env:Jumper_RX_R900MINI_via_BetaflightPassthrough
upload_flags =
    BOOTLOADER=bootloader/jumper_r900_bootloader.bin
    VECT_OFFSET=0x8000

[env:Jumper_RX_R900MINI_via_BetaflightPassthrough]
extends = env_common_stm32, radio_900
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-D TARGET_R900MINI_RX
	-include target/Frsky_RX_R9M.h
	-D HSE_VALUE=12000000U
	-DVECT_TAB_OFFSET=0x8000U
board_build.ldscript = variants/R9MM/R9MM_ldscript.ld
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>
