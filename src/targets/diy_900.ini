
# ********************************
# Transmitter targets
# ********************************

[env:DIY_900_TX_STM32_SX1272_via_STLINK]
extends = env_common_stm32, radio_900
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_tx}
	-include target/DIY_900_TX_STM32_SX1272.h
	-flto
	-DVECT_TAB_OFFSET=0x4000U
debug_build_flags = -O1 -ggdb3 -g3
board_build.ldscript = variants/R9M_ldscript.ld
build_src_filter = ${env_common_stm32.build_src_filter} -<rx_*.cpp>
upload_flags =
    BOOTLOADER=bootloader/sx1272_pcb_bootloader.bin
    VECT_OFFSET=0x4000

# ********************************
# Receiver targets
# ********************************

[env:DIY_900_RX_BluePill_via_STLINK]
extends = env_common_stm32, radio_900
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-include target/DIY_900_RX_STM32.h
	-DVECT_TAB_OFFSET=0x08008000U
board_build.ldscript = variants/R9MM/R9MM_ldscript.ld
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>
upload_flags =
    BOOTLOADER=bootloader/bluepill_bootloader.bin
    VECT_OFFSET=0x8000

[env:DIY_900_RX_STM32_SX1272_via_STLINK]
extends = env_common_stm32, radio_900
build_flags =
	${env_common_stm32.build_flags}
	${radio_900.build_flags}
	${common_env_data.build_flags_rx}
	-include target/DIY_900_RX_STM32_SX1272.h
	-DVECT_TAB_OFFSET=0x4000U
board_build.ldscript = variants/R9M_ldscript.ld
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>
upload_flags =
    BOOTLOADER=bootloader/sx1272_pcb_bootloader.bin
    VECT_OFFSET=0x4000