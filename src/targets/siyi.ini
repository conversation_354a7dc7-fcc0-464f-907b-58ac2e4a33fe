# ********************************
# Transmitter targets
# ********************************

[env:FM30_TX_via_STLINK]
extends = env_common_stm32, radio_2400
board = FM30
debug_tool = stlink
build_flags =
	${env_common_stm32.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_tx}
	#-flto
	#-DUSBCON
	#-DPIO_FRAMEWORK_ARDUINO_ENABLE_CDC
	-include target/FM30_TX.h
	-DHSE_VALUE=16000000U
	-DVECT_TAB_OFFSET=0x1000U
	-Wl,--defsym=FLASH_APP_OFFSET=0x1000
build_src_filter = ${env_common_stm32.build_src_filter} -<rx_*.cpp>
upload_flags =
	BOOTLOADER=bootloader/fm30_bootloader.bin
	VECT_OFFSET=0x1000

[env:FM30_TX_via_DFU]
extends = env:FM30_TX_via_STLINK

# ********************************
# Receiver targets
# ********************************

[env:FM30_RX_MINI_via_STLINK]
extends = env_common_stm32, radio_2400
board = FM30_mini
build_flags =
	${env_common_stm32.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_rx}
	-include target/FM30_RX_MINI.h
	-D HSE_VALUE=16000000U
	-D VECT_TAB_OFFSET=0x4000U
	-Wl,--defsym=FLASH_APP_OFFSET=0x4000
upload_flags =
	BOOTLOADER=bootloader/fm30_mini_bootloader.bin
	VECT_OFFSET=0x4000
build_src_filter = ${env_common_stm32.build_src_filter} -<tx_*.cpp>

[env:FM30_RX_MINI_via_BetaflightPassthrough]
extends = env:FM30_RX_MINI_via_STLINK

[env:FM30_RX_MINI_AS_TX_via_STLINK]
extends = env_common_stm32, radio_2400
board = FM30_mini
build_flags =
	${env_common_stm32.build_flags}
	${radio_2400.build_flags}
	${common_env_data.build_flags_tx}
	-D RX_AS_TX
	-include target/FM30_RX_MINI.h
	-D HSE_VALUE=16000000U
	-D VECT_TAB_OFFSET=0x4000U
	-Wl,--defsym=FLASH_APP_OFFSET=0x4000
upload_flags =
	BOOTLOADER=bootloader/fm30_mini_rxtx_bootloader.bin
	VECT_OFFSET=0x4000
build_src_filter = ${env_common_stm32.build_src_filter} -<rx_*.cpp>

[env:FM30_RX_MINI_AS_TX_via_UART]
extends = env:FM30_RX_MINI_AS_TX_via_STLINK
