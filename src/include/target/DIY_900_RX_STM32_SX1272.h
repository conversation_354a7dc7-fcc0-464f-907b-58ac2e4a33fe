#ifndef DEVICE_NAME
#define DEVICE_NAME "ELRS 900RX"
#endif

#define TARGET_DIY_900_RX_STM32_PCB

// GPIO pin definitions
#define GPIO_PIN_NSS         PA4
#define GP<PERSON>_PIN_MOSI        PA7
#define GP<PERSON>_PIN_MISO        PA6
#define GP<PERSON>_PIN_SCK         PA5

#define GP<PERSON>_PIN_RST         PB0
#define GPIO_PIN_DIO0        PB1
#define GPIO_PIN_DIO1        PB2
#define GPIO_PIN_BUSY        PB3

#define GPIO_PIN_RX_ENABLE   PA2
#define GPIO_PIN_TX_ENABLE   PA3

#define GPIO_PIN_RCSIGNAL_RX PB11  // UART 3
#define GPIO_PIN_RCSIGNAL_TX PB10  // UART 3

#define GPIO_PIN_DEBUG_RX    PB7  // UART 1
#define GPIO_PIN_DEBUG_TX    PB6  // UART 1

#define GPIO_PIN_LED_GREEN   PA10
#define GP<PERSON>_LED_GREEN_INVERTED 0

#define GP<PERSON>_PIN_LED GPIO_PIN_LED_GREEN

#define RADIO_SX1272

// Output Power
#define MinPower                PWR_10mW
#define MaxPower                PWR_50mW
#define DefaultPower            PWR_50mW
#define POWER_OUTPUT_VALUES     {120,124,127}