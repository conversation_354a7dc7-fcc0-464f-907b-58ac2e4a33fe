/*
 * This file is part of the ExpressLRS distribution (https://github.com/ExpressLRS/ExpressLRS).
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, version 3.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

#pragma once

#ifdef TARGET_TX_GHOST

  static unsigned char ghost[] = {
    0x00, 0xF0, 0x0F, 0x00, 0x00, 0x7E, 0x7C, 0x00, 0x80, 0x03, 0xC0, 0x01,
    0xE0, 0x00, 0x00, 0x07, 0x70, 0x00, 0x00, 0x0E, 0x18, 0x00, 0x00, 0x18,
    0x0C, 0x38, 0x80, 0x31, 0x0C, 0xFC, 0xE0, 0x37, 0x06, 0x86, 0x31, 0x6C,
    0x06, 0x82, 0x31, 0x6C, 0x02, 0x82, 0x31, 0xCC, 0x03, 0xE2, 0x31, 0xCE,
    0x03, 0xE2, 0x31, 0xCF, 0x03, 0xE6, 0x31, 0xCF, 0x03, 0xCC, 0x60, 0xC6,
    0x03, 0x78, 0xC0, 0xC3, 0x03, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0xC0,
    0x03, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0xC0,
    0x03, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0xC0,
    0x03, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0xC0,
    0x03, 0x00, 0x00, 0xC0, 0x03, 0x1F, 0xF8, 0xC0, 0x86, 0x31, 0x9C, 0xC1,
    0xEC, 0x60, 0x06, 0x77, 0x78, 0xC0, 0x03, 0x1E, };

#ifndef TARGET_TX_GHOST_LITE
  static unsigned char elrs40[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0xC0,
    0xFF, 0x03, 0x00, 0x00, 0xF0, 0xE3, 0x0F, 0x00, 0x00, 0x78, 0x00, 0x1E,
    0x00, 0x00, 0x1C, 0x7E, 0x38, 0x00, 0x00, 0x8C, 0xFF, 0x31, 0x00, 0x00,
    0xE0, 0xC3, 0x07, 0x00, 0x00, 0xE0, 0x00, 0x07, 0x00, 0x00, 0x20, 0x7E,
    0x06, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x80, 0xC3, 0x01, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00,
    0x3C, 0x00, 0x00, 0x00, 0x00, 0x24, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x00, 0x01, 0x18, 0x80, 0x00, 0xC0, 0x07, 0x3C, 0xE0, 0x03, 0x60,
    0xF4, 0xFF, 0x2F, 0x06, 0x30, 0xF6, 0xFF, 0x6F, 0x0C, 0x18, 0x02, 0x00,
    0x40, 0x18, 0x08, 0x39, 0xFF, 0x9C, 0x10, 0x8C, 0x2C, 0x81, 0x34, 0x31,
    0xC4, 0x3C, 0x81, 0x3C, 0x23, 0x44, 0x18, 0x81, 0x18, 0x22, 0x44, 0x30,
    0xFF, 0x0C, 0x22, 0x44, 0xFC, 0x00, 0x3F, 0x22, 0x46, 0x86, 0x81, 0x61,
    0x62, 0x46, 0x3A, 0x81, 0x5C, 0x62, 0x46, 0x6C, 0x81, 0x36, 0x62, 0xC6,
    0x46, 0x99, 0x62, 0x63, 0x86, 0x63, 0x81, 0xC6, 0x61, 0xE6, 0xB1, 0x81,
    0x8D, 0x67, 0x06, 0xF8, 0x00, 0x1F, 0x60, 0x06, 0x0C, 0x00, 0x30, 0x60,
    0x06, 0x86, 0xFF, 0x61, 0x60, 0x06, 0x83, 0x81, 0xC1, 0x60, 0x06, 0x81,
    0x81, 0x81, 0x60, 0x86, 0xFD, 0xFF, 0xBF, 0x61, };

  static unsigned char elrs48[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x07, 0x00, 0x00,
    0x00, 0x00, 0xFC, 0x3F, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00,
    0x00, 0xC0, 0x1F, 0xF8, 0x03, 0x00, 0x00, 0xE0, 0x03, 0xC0, 0x07, 0x00,
    0x00, 0xF0, 0xF0, 0x0F, 0x0F, 0x00, 0x00, 0x70, 0xFC, 0x3F, 0x0E, 0x00,
    0x00, 0x00, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x80, 0x0F, 0xF0, 0x00, 0x00,
    0x00, 0x80, 0x83, 0xE1, 0x01, 0x00, 0x00, 0x00, 0xF1, 0x8F, 0x00, 0x00,
    0x00, 0x00, 0xF8, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x1C, 0x00, 0x00,
    0x00, 0x00, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00,
    0x00, 0x00, 0x40, 0x02, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00,
    0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x0F, 0xC0, 0x03, 0xF0, 0x00,
    0x80, 0x1B, 0x40, 0x02, 0xD8, 0x01, 0xC0, 0x91, 0xFF, 0xFF, 0x89, 0x03,
    0xE0, 0x98, 0xFF, 0xFF, 0x19, 0x07, 0x30, 0x18, 0x00, 0x00, 0x18, 0x0C,
    0x30, 0xCE, 0xF8, 0x1F, 0x33, 0x0C, 0x18, 0xE6, 0xF9, 0x9F, 0xE7, 0x18,
    0x08, 0xB3, 0x19, 0x98, 0xCD, 0x10, 0x8C, 0xE1, 0x19, 0x98, 0x87, 0x31,
    0x8C, 0xE1, 0x18, 0x18, 0x87, 0x31, 0x8C, 0x01, 0xF8, 0x1F, 0x80, 0x31,
    0x84, 0xE1, 0x03, 0xC2, 0x87, 0x21, 0x84, 0x39, 0x0E, 0x70, 0x9C, 0x21,
    0x84, 0x19, 0x0C, 0x30, 0x98, 0x21, 0x84, 0xED, 0x19, 0x98, 0xB7, 0x21,
    0x84, 0x31, 0x1B, 0xD8, 0x8C, 0x21, 0x84, 0x19, 0x9B, 0xD9, 0x98, 0x21,
    0x04, 0x0F, 0x9B, 0xD9, 0xF0, 0x20, 0xC4, 0x87, 0x09, 0x90, 0xE1, 0x23,
    0xC4, 0xC3, 0x0E, 0x70, 0xC3, 0x23, 0x04, 0x60, 0x07, 0xE0, 0x06, 0x20,
    0x04, 0x30, 0x00, 0x00, 0x0C, 0x20, 0x04, 0x18, 0xF8, 0x1F, 0x18, 0x20,
    0x04, 0x0C, 0xF8, 0x1F, 0x30, 0x20, 0x04, 0x06, 0x08, 0x10, 0x60, 0x20,
    0x04, 0xF6, 0xFF, 0xFF, 0x6F, 0x20, 0x04, 0xF2, 0xFF, 0xFF, 0x4F, 0x20,
    };

  static unsigned char elrs56[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0xF8,
    0xFF, 0x0F, 0x00, 0x00, 0x00, 0x00, 0xFC, 0xFF, 0x3F, 0x00, 0x00, 0x00,
    0x00, 0x7F, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x80, 0x1F, 0x00, 0xF8, 0x01,
    0x00, 0x00, 0xC0, 0x07, 0xFF, 0xE0, 0x03, 0x00, 0x00, 0xC0, 0xE3, 0xFF,
    0xC3, 0x03, 0x00, 0x00, 0xC0, 0xF1, 0xFF, 0x8F, 0x01, 0x00, 0x00, 0x00,
    0xF8, 0x81, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x7C, 0x00, 0x3E, 0x00, 0x00,
    0x00, 0x00, 0x1C, 0x3E, 0x38, 0x00, 0x00, 0x00, 0x00, 0x88, 0xFF, 0x10,
    0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0xE0,
    0xE7, 0x03, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x81, 0x03, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00,
    0x00, 0x18, 0x00, 0x3C, 0x00, 0x18, 0x00, 0x00, 0x3E, 0x00, 0x3C, 0x00,
    0x7C, 0x00, 0x00, 0x67, 0x00, 0x00, 0x00, 0xE6, 0x00, 0x80, 0x43, 0xFE,
    0xFF, 0x7F, 0xC2, 0x01, 0xC0, 0x61, 0xFE, 0xFF, 0x7F, 0x86, 0x03, 0xE0,
    0x60, 0x00, 0x00, 0x00, 0x06, 0x07, 0x70, 0x30, 0x00, 0x00, 0x00, 0x1C,
    0x0E, 0x30, 0x9C, 0x87, 0xFF, 0xE1, 0x19, 0x0C, 0x18, 0x8C, 0x8F, 0x00,
    0xF1, 0x31, 0x18, 0x18, 0xC6, 0x8C, 0x00, 0x31, 0x63, 0x18, 0x18, 0x83,
    0x8F, 0x00, 0xF1, 0xC1, 0x18, 0x08, 0x03, 0x87, 0x00, 0xE1, 0xC0, 0x10,
    0x0C, 0x03, 0x80, 0xFF, 0x01, 0xC0, 0x30, 0x0C, 0x03, 0x9F, 0xFF, 0xF9,
    0xC0, 0x30, 0x0C, 0xC3, 0x3F, 0x00, 0xFC, 0xC3, 0x30, 0x0C, 0xE3, 0x60,
    0x00, 0x06, 0xC7, 0x30, 0x0C, 0x73, 0xC0, 0x00, 0x03, 0xCE, 0x30, 0x0C,
    0xB3, 0xCF, 0x00, 0xF3, 0xCD, 0x30, 0x0C, 0xE3, 0x8D, 0x00, 0xB1, 0xC7,
    0x30, 0x0C, 0xE6, 0x98, 0x18, 0x19, 0x67, 0x30, 0x0C, 0x36, 0xD8, 0x18,
    0x1B, 0x6E, 0x30, 0x0C, 0x3C, 0xCC, 0x00, 0x33, 0x38, 0x30, 0x8C, 0x1F,
    0x6E, 0x00, 0x76, 0xF8, 0x31, 0x8C, 0x07, 0x77, 0x00, 0xEE, 0xE0, 0x31,
    0x0C, 0x80, 0x3B, 0x00, 0xDC, 0x01, 0x30, 0x0C, 0xC0, 0x01, 0x00, 0x80,
    0x03, 0x30, 0x0C, 0xE0, 0x00, 0x42, 0x01, 0x07, 0x30, 0x0C, 0x70, 0xC0,
    0xFF, 0x03, 0x0E, 0x30, 0x0C, 0x38, 0x40, 0x00, 0x02, 0x1C, 0x30, 0x0C,
    0x18, 0x40, 0x00, 0x02, 0x18, 0x30, 0x0C, 0xCC, 0xFF, 0xFF, 0xFF, 0x33,
    0x30, 0x0C, 0xCC, 0xFF, 0xFF, 0xFF, 0x33, 0x30, };
#endif

#endif

#if defined(USE_OLED_SPI_SMALL) || defined(TARGET_TX_GHOST)

  static unsigned char elrs32[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x0F, 0x00, 0x00, 0xFC, 0x3F, 0x00,
    0x00, 0x0E, 0x70, 0x00, 0x00, 0xE7, 0xE7, 0x00, 0x00, 0xF9, 0x9F, 0x00,
    0x00, 0x1C, 0x38, 0x00, 0x00, 0xCC, 0x33, 0x00, 0x00, 0xF0, 0x07, 0x00,
    0x00, 0x30, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00,
    0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0xE0, 0x80, 0x01, 0x07,
    0x30, 0xFD, 0xBF, 0x0C, 0x18, 0x01, 0x84, 0x19, 0xCC, 0x60, 0x05, 0x31,
    0x44, 0xE7, 0xE7, 0x22, 0x24, 0x27, 0xE4, 0x24, 0x22, 0x22, 0x44, 0x64,
    0x22, 0xEE, 0x77, 0x44, 0xA2, 0x11, 0x88, 0x44, 0xA2, 0x26, 0x64, 0x45,
    0x22, 0x29, 0x94, 0x44, 0xA2, 0xA8, 0x15, 0x45, 0x72, 0x14, 0x28, 0x4E,
    0x02, 0x1A, 0x58, 0x40, 0x02, 0x01, 0x80, 0x40, 0x82, 0xF0, 0x0F, 0x41,
    0x42, 0x10, 0x08, 0x42, 0x42, 0xFF, 0xFF, 0x42, };

#endif

#if defined(USE_OLED_SPI) || defined(USE_OLED_I2C)

  static unsigned char elrs64[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x3F, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x80, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0xFF,
    0xFF, 0x07, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x7F, 0xFE, 0x0F, 0x00, 0x00,
    0x00, 0x00, 0xFC, 0x03, 0xC0, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x00,
    0x00, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xF8, 0x0F, 0xFC, 0x00, 0x00,
    0x00, 0x00, 0x0F, 0xFE, 0x7F, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x87, 0xFF,
    0xFF, 0xF1, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0xFF, 0x03, 0x00, 0x00,
    0x00, 0x00, 0xE0, 0x07, 0xE0, 0x07, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x01,
    0x80, 0x0F, 0x00, 0x00, 0x00, 0x00, 0xF0, 0xF0, 0x07, 0x07, 0x00, 0x00,
    0x00, 0x00, 0x60, 0xF8, 0x1F, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE,
    0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE, 0x7F, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x1E, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06,
    0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0,
    0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x60, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0,
    0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x00, 0xC0,
    0x03, 0x00, 0x1F, 0x00, 0x00, 0xFC, 0x01, 0xE0, 0x07, 0x80, 0x3F, 0x00,
    0x00, 0x8E, 0x01, 0x00, 0x00, 0x80, 0x71, 0x00, 0x00, 0x87, 0xF3, 0xFF,
    0xFF, 0x8F, 0xE1, 0x00, 0x80, 0x83, 0xF1, 0xFF, 0xFF, 0x8F, 0xC1, 0x01,
    0xC0, 0x81, 0x01, 0x00, 0x00, 0x80, 0x81, 0x03, 0xE0, 0xE0, 0x00, 0x00,
    0x00, 0x00, 0x07, 0x07, 0x60, 0x60, 0x1C, 0xFC, 0x3F, 0x38, 0x06, 0x06,
    0x30, 0x30, 0x3E, 0xFC, 0x3F, 0x7C, 0x0C, 0x0C, 0x30, 0x18, 0x76, 0x0C,
    0x30, 0x6E, 0x18, 0x0C, 0x18, 0x0C, 0x66, 0x0C, 0x30, 0x66, 0x38, 0x18,
    0x18, 0x0C, 0x3E, 0x0C, 0x30, 0x7C, 0x30, 0x18, 0x18, 0x0E, 0x3C, 0x0C,
    0x30, 0x3C, 0x70, 0x18, 0x18, 0x06, 0x00, 0xFC, 0x3F, 0x00, 0x60, 0x18,
    0x18, 0x06, 0x78, 0xFC, 0x3F, 0x1E, 0x60, 0x18, 0x1C, 0x06, 0xFE, 0x01,
    0x80, 0x7F, 0x60, 0x38, 0x0C, 0x86, 0xC7, 0x03, 0xC0, 0xE3, 0x61, 0x30,
    0x0C, 0x86, 0x01, 0x07, 0xE0, 0x80, 0x61, 0x30, 0x0C, 0xC6, 0x18, 0x06,
    0x60, 0x18, 0x63, 0x30, 0x0C, 0xC6, 0x7E, 0x0E, 0x70, 0x7E, 0x63, 0x30,
    0x0C, 0xCE, 0x77, 0x0C, 0x30, 0xEE, 0x73, 0x30, 0x0C, 0x8C, 0xE3, 0x8C,
    0x31, 0xC7, 0x31, 0x30, 0x0C, 0xCC, 0xC1, 0x8C, 0x31, 0x83, 0x3B, 0x30,
    0x0C, 0xF8, 0xE0, 0x8E, 0x71, 0x07, 0x1F, 0x30, 0x0C, 0x78, 0x70, 0x06,
    0x60, 0x0E, 0x1E, 0x30, 0x0C, 0x3F, 0x38, 0x07, 0xE0, 0x1C, 0xFC, 0x30,
    0x0C, 0x0F, 0xDC, 0x03, 0xC0, 0x3B, 0xF0, 0x30, 0x0C, 0x00, 0xCE, 0x01,
    0x80, 0x73, 0x00, 0x30, 0x0C, 0x00, 0x07, 0x00, 0x00, 0xE0, 0x00, 0x30,
    0x0C, 0x80, 0x03, 0x00, 0x00, 0xC0, 0x01, 0x30, 0x0C, 0xC0, 0x01, 0xFE,
    0x7F, 0x80, 0x03, 0x30, 0x0C, 0xE0, 0x00, 0xFE, 0x7F, 0x00, 0x07, 0x30,
    0x0C, 0x60, 0x00, 0x06, 0x60, 0x00, 0x06, 0x30, 0x0C, 0x30, 0x00, 0x06,
    0x60, 0x00, 0x0E, 0x30, 0x0C, 0x30, 0xFE, 0xFF, 0xFF, 0x7F, 0x0C, 0x30,
    0x0C, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0x0C, 0x30, };

#endif


#if defined(USE_OLED_SPI) || defined(USE_OLED_SPI_SMALL) || defined(USE_OLED_I2C)
  // .header.w = 65,
  // .header.h = 43,
  // Use a tool to convert svg to xbm string format
  // Make sure to flip it vertically
  // https://javl.github.io/image2cpp/
  // Need to set that to horizontal - 1 bit per pixel
  // Then reverse byte order
  // https://yupana-engineering.com/online-reverse-byte-array

// size 64x44
const unsigned char rate_img64[] =
{
   0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x80, 0xf3, 0x71, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xf7,
   0xf1, 0x03, 0x00, 0x00, 0x00, 0x00, 0x20, 0xe7, 0xf1, 0x1f, 0x00, 0x00, 0x00,
   0x00, 0x38, 0xef, 0xf1, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x3e, 0xcf, 0xf3, 0xff,
   0x01, 0x00, 0x00, 0x00, 0x7e, 0xdf, 0xf3, 0xff, 0x03, 0x00, 0x00, 0x00, 0x7c,
   0x9f, 0xf3, 0xff, 0x07, 0x00, 0x00, 0x60, 0x7c, 0xbe, 0xf1, 0xff, 0x07, 0x00,
   0x00, 0xf0, 0x78, 0x3e, 0x00, 0xfc, 0x03, 0x00, 0x00, 0xf8, 0x70, 0x3e, 0x00,
   0xe0, 0x63, 0x00, 0x00, 0xfc, 0xf1, 0x7e, 0x00, 0x80, 0xf1, 0x00, 0x00, 0xfe,
   0xe3, 0x7c, 0x00, 0x00, 0xf8, 0x01, 0x00, 0xff, 0x07, 0xfc, 0x00, 0x00, 0xf8,
   0x03, 0x80, 0xff, 0x0f, 0xfc, 0x00, 0x00, 0xf0, 0x03, 0x80, 0xff, 0x0f, 0xfc,
   0x01, 0x00, 0xe0, 0x07, 0xc0, 0xff, 0x07, 0xfc, 0x01, 0x00, 0xc0, 0x0f, 0xe0,
   0xff, 0x03, 0xf8, 0x03, 0x00, 0x80, 0x0f, 0xe0, 0xff, 0x03, 0xf8, 0x03, 0x00,
   0x00, 0x1f, 0xf0, 0xff, 0x01, 0xf8, 0x07, 0x00, 0x00, 0x1f, 0xf0, 0xff, 0x00,
   0xf8, 0x07, 0x00, 0x00, 0x3e, 0xf8, 0xff, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0x3c,
   0xe0, 0x7f, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0x3c, 0x80, 0x7f, 0x00, 0xf0, 0x1f,
   0x00, 0x00, 0x38, 0x00, 0x3c, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0x00, 0x0c, 0x30,
   0x00, 0xf0, 0x3f, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00,
   0x40, 0xfe, 0x07, 0x00, 0xe0, 0x7f, 0x00, 0x00, 0xe0, 0xfe, 0x1f, 0x00, 0xe0,
   0x7f, 0x00, 0x00, 0xe0, 0xfe, 0x1f, 0x00, 0xe0, 0x7f, 0x00, 0x00, 0xe0, 0xff,
   0x1f, 0x00, 0xc0, 0xff, 0x00, 0x00, 0xe0, 0xff, 0x1f, 0x00, 0xc0, 0xe3, 0x00,
   0x00, 0xe0, 0xff, 0x1f, 0x00, 0xc0, 0xc1, 0x01, 0x00, 0xc0, 0xff, 0x1f, 0x00,
   0xc0, 0xc1, 0x01, 0x00, 0xc0, 0xff, 0x1f, 0x00, 0xc0, 0xc1, 0x01, 0x00, 0xc0,
   0xff, 0x1f, 0x00, 0x80, 0xe3, 0x00, 0x00, 0xc0, 0xff, 0x1f, 0x00, 0x80, 0xff,
   0x00, 0x00, 0xc0, 0xff, 0x1f, 0x00, 0x00, 0x7f, 0x00, 0x00, 0xc0, 0xff, 0x1f,
   0x00, 0x00, 0x3c, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00
   };

// size 32x22
const unsigned char rate_img32[] =
{
  0x00, 0x08, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x98, 0x00, 0x00, 0x00, 0xd0,
  0x3d, 0x00, 0x00, 0xb6, 0xfd, 0x00, 0x00, 0xb6, 0xfd, 0x01, 0xc0, 0x7e, 0xf0, 0x01,
  0xe0, 0x6c, 0x80, 0x0d, 0xf0, 0xe9, 0x00, 0x1c, 0xf8, 0xe3, 0x00, 0x1c, 0xf8, 0xe1,
  0x01, 0x38, 0xfc, 0xe1, 0x01, 0x30, 0xfc, 0xc0, 0x03, 0x60, 0xf8, 0xc0, 0x03, 0x60,
  0x60, 0xc0, 0x07, 0x00, 0x1e, 0xc0, 0x07, 0xc0, 0x7f, 0x80, 0x0f, 0xc0, 0x7f, 0x80,
  0x0f, 0x80, 0x7f, 0x80, 0x09, 0x80, 0x7f, 0x80, 0x09, 0x80, 0x7f, 0x00, 0x0f, 0x80,
  0x3f, 0x00, 0x00, 0x00
  };

// 64x64
const unsigned char switch_img64[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0xC0, 0x03, 0x00, 0x00,
    0x00, 0x00, 0xC0, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07,
    0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x60, 0xF0, 0xFF, 0xFF,
    0xFF, 0xFF, 0x1F, 0x06, 0x60, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x06,
    0x60, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x30, 0x06, 0x60, 0x04, 0x00, 0x00,
    0x00, 0x00, 0x60, 0x06, 0x60, 0x06, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06,
    0x60, 0x06, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06, 0x60, 0x06, 0x00, 0x00,
    0x00, 0x00, 0x60, 0x06, 0x60, 0x86, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06,
    0x60, 0xC6, 0xFF, 0x0F, 0xFE, 0xFF, 0x61, 0x06, 0x60, 0xC6, 0x01, 0x18,
    0x06, 0x00, 0x61, 0x06, 0x60, 0xC6, 0x01, 0x18, 0x06, 0x00, 0x61, 0x06,
    0x60, 0xC6, 0x03, 0x10, 0x06, 0x00, 0x61, 0x06, 0x60, 0xC6, 0x03, 0x30,
    0x06, 0x00, 0x61, 0x06, 0x60, 0xC6, 0x06, 0x30, 0x06, 0x0F, 0x61, 0x06,
    0x60, 0xC6, 0x06, 0x60, 0x06, 0x0D, 0x61, 0x06, 0x60, 0xC6, 0xFC, 0x7F,
    0x86, 0x89, 0x61, 0x06, 0x60, 0xC6, 0xFC, 0x7F, 0x86, 0x89, 0x61, 0x06,
    0x60, 0xC6, 0x06, 0x60, 0x06, 0x8F, 0x61, 0x06, 0x60, 0xC6, 0x06, 0x20,
    0x06, 0x87, 0x61, 0x06, 0x60, 0xC6, 0x03, 0x20, 0x06, 0x80, 0x61, 0x06,
    0x60, 0xC6, 0x43, 0x30, 0x06, 0x80, 0x61, 0x06, 0x60, 0xC6, 0x41, 0x30,
    0x0E, 0x82, 0x61, 0x06, 0x60, 0xC6, 0x41, 0x30, 0x0E, 0x86, 0x61, 0x06,
    0x60, 0xC6, 0x40, 0x30, 0x1E, 0x06, 0x61, 0x06, 0x60, 0xC6, 0x00, 0x30,
    0x1E, 0x06, 0x61, 0x06, 0x60, 0xC6, 0x00, 0x30, 0x36, 0x00, 0x63, 0x06,
    0x60, 0xC6, 0xE0, 0x30, 0x36, 0x00, 0x63, 0x06, 0x60, 0xC6, 0xF0, 0x31,
    0x26, 0x00, 0x63, 0x06, 0x60, 0xC6, 0xB0, 0x31, 0x66, 0x00, 0x63, 0x06,
    0x60, 0xC6, 0xB0, 0x31, 0xE6, 0xFF, 0x63, 0x06, 0x60, 0xC6, 0xB0, 0x31,
    0x66, 0x00, 0x63, 0x06, 0x60, 0xC6, 0xE0, 0x30, 0x36, 0x00, 0x63, 0x06,
    0x60, 0xC6, 0x40, 0x30, 0x36, 0x80, 0x61, 0x06, 0x60, 0xC6, 0x00, 0x30,
    0x1E, 0x80, 0x61, 0x06, 0x60, 0xC6, 0x00, 0x30, 0x1E, 0x80, 0x61, 0x06,
    0x60, 0xC6, 0x00, 0x30, 0x0E, 0xC0, 0x60, 0x06, 0x60, 0xC6, 0xFF, 0x3F,
    0xFE, 0xFF, 0x60, 0x06, 0x60, 0x86, 0xFF, 0x3F, 0xFC, 0x7F, 0x60, 0x06,
    0x60, 0x06, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06, 0x60, 0x06, 0x00, 0x00,
    0x00, 0x00, 0x60, 0x06, 0x60, 0x06, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06,
    0x60, 0x04, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06, 0x60, 0x0C, 0x00, 0x00,
    0x00, 0x00, 0x60, 0x06, 0x60, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x38, 0x06,
    0x60, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x1F, 0x06, 0x60, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x06, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06,
    0xC0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0x80, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

// 32x32
const unsigned char switch_img32[] = {
    0x00, 0x00, 0x00, 0x00, 0xF0, 0xFF, 0xFF, 0x0F, 0x08, 0x00, 0x00, 0x10,
    0xC8, 0xFF, 0xFF, 0x23, 0x68, 0x00, 0x00, 0x24, 0x28, 0x00, 0x00, 0x24,
    0x28, 0x00, 0x00, 0x28, 0xA8, 0x3F, 0xFE, 0x29, 0xA8, 0x61, 0x02, 0x29,
    0xA8, 0x41, 0x02, 0x29, 0xA8, 0x43, 0x32, 0x29, 0xA8, 0xFE, 0x32, 0x29,
    0xA8, 0x43, 0x32, 0x29, 0xA8, 0x41, 0x02, 0x29, 0xA8, 0x49, 0x02, 0x29,
    0xA8, 0x40, 0x12, 0x29, 0xA8, 0x40, 0x06, 0x29, 0xA8, 0x4C, 0x0E, 0x29,
    0xA8, 0x5C, 0xFE, 0x29, 0xA8, 0x4C, 0x06, 0x29, 0xA8, 0x40, 0x82, 0x28,
    0xA8, 0x7F, 0xFE, 0x28, 0x28, 0x00, 0x00, 0x28, 0x28, 0x00, 0x00, 0x28,
    0x28, 0x00, 0x00, 0x24, 0xE8, 0xFF, 0xFF, 0x27, 0x08, 0x00, 0x00, 0x10,
    0xF8, 0xFF, 0xFF, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

// 64x64
const unsigned char antenna_img64[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0xE0, 0x00, 0x00,
    0x00, 0x80, 0x0F, 0x00, 0x00, 0xE0, 0x01, 0x00, 0x00, 0xC0, 0x0F, 0x00,
    0x00, 0xE0, 0x03, 0x00, 0x00, 0xC0, 0x07, 0x00, 0x00, 0xE0, 0x07, 0x00,
    0x00, 0xE0, 0x03, 0x00, 0x00, 0xC0, 0x07, 0x00, 0x00, 0xE0, 0xE1, 0x80,
    0x01, 0x8F, 0x0F, 0x00, 0x00, 0xF0, 0xF1, 0xF0, 0x0F, 0x1F, 0x0F, 0x00,
    0x00, 0xF0, 0xF8, 0xF8, 0x3F, 0x1F, 0x1F, 0x00, 0x00, 0xF8, 0xF8, 0xFC,
    0x7F, 0x3E, 0x1E, 0x00, 0x00, 0x78, 0x7C, 0xFE, 0x7F, 0x3C, 0x1E, 0x00,
    0x00, 0x78, 0x3C, 0x3E, 0xF8, 0x7C, 0x1E, 0x00, 0x00, 0x78, 0x3C, 0x1F,
    0xF0, 0x78, 0x3E, 0x00, 0x00, 0x78, 0x3C, 0x0F, 0xF0, 0x78, 0x3C, 0x00,
    0x00, 0x78, 0x3E, 0x0F, 0xF0, 0x78, 0x3C, 0x00, 0x00, 0x78, 0x3C, 0x0F,
    0xF0, 0x78, 0x3C, 0x00, 0x00, 0x78, 0x3C, 0x1F, 0xF0, 0x78, 0x3E, 0x00,
    0x00, 0x78, 0x3C, 0x3E, 0xF8, 0x7C, 0x1E, 0x00, 0x00, 0x78, 0x7C, 0xFE,
    0x7F, 0x3C, 0x1E, 0x00, 0x00, 0xF8, 0xF8, 0xFC, 0x7F, 0x3E, 0x1E, 0x00,
    0x00, 0xF0, 0xF8, 0xF8, 0x3F, 0x1F, 0x1F, 0x00, 0x00, 0xF0, 0xF1, 0xF0,
    0x0F, 0x1F, 0x0F, 0x00, 0x00, 0xE0, 0xE1, 0xF0, 0x1F, 0x8F, 0x0F, 0x00,
    0x00, 0xE0, 0x03, 0xF0, 0x1F, 0xC0, 0x07, 0x00, 0x00, 0xC0, 0x07, 0xF8,
    0x1E, 0xC0, 0x07, 0x00, 0x00, 0xC0, 0x0F, 0x78, 0x3E, 0xE0, 0x03, 0x00,
    0x00, 0x80, 0x0F, 0x7C, 0x3C, 0xE0, 0x01, 0x00, 0x00, 0x00, 0x07, 0x3C,
    0x3C, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x7C, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x3E, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3E,
    0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3E, 0xFC, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
    0xFF, 0x01, 0x00, 0x00, 0x00, 0x00, 0x80, 0xFF, 0xFF, 0x01, 0x00, 0x00,
    0x00, 0x00, 0x80, 0xF7, 0xEF, 0x01, 0x00, 0x00, 0x00, 0x00, 0x80, 0xE7,
    0xE7, 0x03, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xE7, 0xCF, 0x03, 0x00, 0x00,
    0x00, 0x00, 0xC0, 0xF3, 0xDF, 0x07, 0x00, 0x00, 0x00, 0x00, 0xE0, 0xFB,
    0x9F, 0x07, 0x00, 0x00, 0x00, 0x00, 0xE0, 0xFF, 0xFE, 0x07, 0x00, 0x00,
    0x00, 0x00, 0xE0, 0x7F, 0xFC, 0x0F, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x3F,
    0xF8, 0x0F, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x1F, 0xF0, 0x0F, 0x00, 0x00,
    0x00, 0x00, 0xF0, 0x0F, 0xF0, 0x1F, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x07,
    0xE0, 0x1F, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x03, 0xC0, 0x3F, 0x00, 0x00,
    0x00, 0x00, 0xFC, 0x01, 0x80, 0x3F, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x01,
    0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0xFC, 0xFF, 0xFF, 0x7F, 0x00, 0x00,
    0x00, 0x00, 0xFE, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0xFE, 0xFF,
    0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0xFE, 0xFF, 0xFF, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0x1F, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x00,
    0x00, 0xF0, 0x01, 0x00, 0x00, 0x80, 0x0F, 0x00, 0x00, 0xE0, 0x01, 0x00,
    0x00, 0x80, 0x07, 0x00, 0x00, 0xE0, 0x01, 0x00, 0x00, 0x00, 0x07, 0x00,
    0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

//32x32
const unsigned char antenna_img32[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x80, 0x03, 0x80, 0x01,
    0xC0, 0x09, 0xA0, 0x03, 0xC0, 0xCC, 0x33, 0x03, 0xC0, 0xEE, 0x67, 0x07,
    0xE0, 0x76, 0x6C, 0x06, 0x60, 0x36, 0x6C, 0x06, 0x60, 0x36, 0x6C, 0x06,
    0xC0, 0x76, 0x6E, 0x06, 0xC0, 0xEE, 0x77, 0x03, 0xC0, 0xCD, 0x33, 0x03,
    0x80, 0xC1, 0x87, 0x03, 0x80, 0xE3, 0xC6, 0x01, 0x00, 0x60, 0x86, 0x00,
    0x00, 0x60, 0x0E, 0x00, 0x00, 0x70, 0x0E, 0x00, 0x00, 0xF0, 0x0F, 0x00,
    0x00, 0xF8, 0x1B, 0x00, 0x00, 0xD8, 0x1B, 0x00, 0x00, 0xF8, 0x3F, 0x00,
    0x00, 0x7C, 0x3E, 0x00, 0x00, 0x3C, 0x3C, 0x00, 0x00, 0x1C, 0x78, 0x00,
    0x00, 0x1E, 0x70, 0x00, 0x00, 0xFE, 0x7F, 0x00, 0x00, 0xFF, 0xFF, 0x00,
    0x00, 0x03, 0xC0, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

// size 44x44
const unsigned char wifi_img64[] =
{
  0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xff, 0x03, 0x00, 0x00, 0xc0,
  0xff, 0xff, 0x1f, 0x00, 0x00, 0xf0, 0xff, 0xff, 0x7f, 0x00, 0x00, 0xfc, 0xff, 0xff,
  0xff, 0x01, 0x00, 0xfe, 0xff, 0xff, 0xff, 0x07, 0x80, 0xff, 0x07, 0x00, 0xff, 0x0f,
  0xc0, 0xff, 0x00, 0x00, 0xf8, 0x1f, 0xe0, 0x3f, 0x00, 0x00, 0xe0, 0x7f, 0xf0, 0x0f,
  0x80, 0x0f, 0x80, 0xff, 0xf0, 0x07, 0xf8, 0xff, 0x00, 0xfe, 0xf0, 0x03, 0xff, 0xff,
  0x07, 0xfc, 0xe0, 0x80, 0xff, 0xff, 0x1f, 0x78, 0x40, 0xe0, 0xff, 0xff, 0x3f, 0x30,
  0x00, 0xf0, 0xff, 0xff, 0xff, 0x00, 0x00, 0xf8, 0x1f, 0xc0, 0xff, 0x01, 0x00, 0xfc,
  0x07, 0x00, 0xfe, 0x01, 0x00, 0xfc, 0x01, 0x00, 0xf8, 0x01, 0x00, 0x78, 0x00, 0x00,
  0xf0, 0x01, 0x00, 0x30, 0xe0, 0x3f, 0xe0, 0x00, 0x00, 0x00, 0xf8, 0xff, 0x00, 0x00,
  0x00, 0x00, 0xfc, 0xff, 0x03, 0x00, 0x00, 0x00, 0xfe, 0xff, 0x07, 0x00, 0x00, 0x00,
  0xfe, 0xff, 0x07, 0x00, 0x00, 0x00, 0x7e, 0xf0, 0x03, 0x00, 0x00, 0x00, 0x3c, 0xc0,
  0x03, 0x00, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00, 0x00, 0x00,
  0xe0, 0x3f, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x7f, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f,
  0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, 0x00, 0x00,
  0x00, 0x00, 0xe0, 0x7f, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0x00, 0x00,
  0xc0, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x80, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

// size 22x22
const unsigned char wifi_img32[] =
{
  0x00, 0xfc, 0x00, 0xc0, 0xff, 0x07, 0xe0, 0xff, 0x1f, 0xf8, 0x01, 0x7e, 0x3c, 0x00, 0xf8,
  0x1c, 0xfe, 0xe1, 0x8c, 0xff, 0x47, 0xc0, 0xcf, 0x0f, 0xe0, 0x01, 0x1e, 0x40, 0x38, 0x0c,
  0x00, 0xfe, 0x00, 0x00, 0xff, 0x01, 0x00, 0x86, 0x01, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00,
  0x00, 0x7c, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

// size 64x64
const unsigned char ratio_img64[] =
{
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00,
 0x00, 0xe0, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x0c, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x1c, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x10, 0x00, 0x00,
 0x00, 0x00, 0x7f, 0x06, 0x00, 0x10, 0x00, 0x00, 0x00, 0xc0, 0xc1, 0x03, 0x00, 0xf0, 0x00,
 0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0xf8, 0x03, 0x00, 0x00, 0x30, 0x80, 0x01, 0x00, 0x1c,
 0x07, 0x00, 0x00, 0x10, 0x80, 0x01, 0x00, 0x04, 0x0e, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
 0x00, 0x0c, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x3f, 0x00, 0x00,
 0x00, 0x00, 0x3e, 0x00, 0x80, 0xe0, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x40, 0x80, 0x00,
 0x00, 0x00, 0x00, 0xe1, 0x01, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0x03, 0x20, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x20,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06,
 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x06, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x07, 0x80, 0x01, 0x00, 0x00, 0xe0, 0x00, 0x80, 0x03, 0x80, 0x07, 0x00, 0x00, 0xf8,
 0x03, 0x80, 0x01, 0x00, 0x0f, 0x00, 0x00, 0xfe, 0x0f, 0xc0, 0x01, 0x00, 0x7c, 0x00, 0x80,
 0x7f, 0x3f, 0xf0, 0x00, 0x00, 0xf8, 0x0f, 0xfc, 0x0f, 0xfc, 0x3f, 0x00, 0x00, 0xe0, 0xff,
 0xff, 0x03, 0xf0, 0x0f, 0x00, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x10, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x1e, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x10, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x80, 0x3f, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x7f, 0x18, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x80, 0x6c, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xdb,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04,
 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x04, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00
 };

// size 32x32
const unsigned char ratio_img32[] =
{
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x80, 0x11, 0x00, 0x00, 0x60, 0x20, 0x00, 0x00, 0x22,
 0x00, 0x00, 0x80, 0x10, 0xc0, 0x00, 0x40, 0x10, 0x20, 0x03, 0x00, 0x00, 0x00, 0x02, 0xf0,
 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x19, 0x04, 0x00, 0x00, 0x10, 0x04, 0x00, 0x00, 0x30,
 0x04, 0x00, 0x00, 0x30, 0x08, 0x00, 0x00, 0x10, 0x10, 0x00, 0x0c, 0x10, 0x60, 0x00, 0x3f,
 0x0c, 0xc0, 0xff, 0xe3, 0x07, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20,
 0x00, 0x00, 0x00, 0x70, 0x04, 0x00, 0x00, 0x78, 0x04, 0x00, 0x00, 0x20, 0x0f, 0x00, 0x00,
 0x20, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
 };

// size 64x64
const unsigned char bind_img64[] =
{
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x30,
   0x00, 0x00, 0x00, 0x00, 0xe0, 0x1f, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x00,
   0x38, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x78,
   0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x1e, 0xf0, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x0f,
   0xe0, 0x00, 0x38, 0x80, 0x03, 0x00, 0x00, 0x07, 0xe0, 0x01, 0x38, 0xc0, 0x03, 0x00, 0x00,
   0x07, 0xc0, 0x03, 0x38, 0xe0, 0x03, 0x00, 0x00, 0x07, 0xc0, 0x03, 0x00, 0xf0, 0x01, 0x00,
   0x00, 0x0f, 0x80, 0x07, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x07, 0x00, 0x78, 0x00,
   0x00, 0x00, 0x1e, 0x00, 0x0f, 0x00, 0x38, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x3c, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x80, 0x0f,
   0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x00, 0xf0,
   0x7f, 0x00, 0x7f, 0x00, 0x00, 0xe0, 0x00, 0xfc, 0xf8, 0x80, 0xff, 0x00, 0x00, 0xe0, 0x01,
   0x3e, 0xf0, 0xc0, 0xff, 0x00, 0x00, 0xc0, 0x01, 0x1e, 0xe0, 0x81, 0x7f, 0x00, 0x00, 0xc0,
   0x03, 0x0e, 0xc0, 0x01, 0x00, 0x00, 0x00, 0x80, 0x07, 0x0f, 0xc1, 0x03, 0x00, 0x00, 0x00,
   0x80, 0x07, 0x8f, 0x83, 0x03, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x8f, 0x87, 0x07, 0x00, 0x00,
   0x00, 0x00, 0x0e, 0x0e, 0x07, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x04, 0x07, 0x0f, 0x00,
   0x00, 0x00, 0x00, 0x1c, 0x80, 0x07, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x80, 0x03, 0x1c,
   0x00, 0x00, 0x00, 0x00, 0x78, 0xe0, 0x03, 0x3c, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xf8, 0x01,
   0x38, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x3f,
   0x00, 0xf0, 0x00, 0x00, 0x00, 0xfe, 0xc0, 0x1f, 0x00, 0xf0, 0x00, 0x00, 0x00, 0xff, 0x01,
   0x00, 0x01, 0xe0, 0x01, 0x00, 0x00, 0xff, 0x01, 0x80, 0x03, 0xc0, 0x01, 0x00, 0x00, 0xfe,
   0x00, 0x80, 0x07, 0xc0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x03, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x0f, 0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x07, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x07, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x3c, 0x00, 0x07,
   0x00, 0x00, 0x00, 0x1e, 0x00, 0x38, 0x00, 0x07, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x78, 0x80,
   0x07, 0x00, 0x00, 0x80, 0x0f, 0x00, 0x70, 0xe0, 0x03, 0x00, 0x00, 0xc0, 0x07, 0x1c, 0xf0,
   0xf9, 0x01, 0x00, 0x00, 0xc0, 0x03, 0x1c, 0xe0, 0xff, 0x00, 0x00, 0x00, 0xc0, 0x01, 0x1c,
   0xc0, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
   0x00, 0x00
   };

// size 32x32
const unsigned char bind_img32[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0xc0, 0x07, 0x04, 0x00,
  0xe0, 0x0e, 0x04, 0x00, 0x30, 0x0c, 0x04, 0x01, 0x30, 0x18, 0x84, 0x01, 0x30, 0x18, 0xc0, 0x00,
  0x30, 0x30, 0x60, 0x00, 0x60, 0x20, 0x00, 0x00, 0x40, 0x80, 0x07, 0x00, 0xc0, 0xe0, 0x0f, 0x0f,
  0x80, 0x61, 0x8c, 0x0f, 0x80, 0x31, 0x18, 0x00, 0x00, 0x33, 0x19, 0x00, 0x00, 0x23, 0x31, 0x00,
  0x00, 0x86, 0x21, 0x00, 0x00, 0xcc, 0x61, 0x00, 0x00, 0xfc, 0xc0, 0x00, 0xf0, 0x30, 0xc0, 0x00,
  0xf0, 0x01, 0x81, 0x01, 0x00, 0x00, 0x03, 0x01, 0x00, 0x00, 0x02, 0x03, 0x00, 0x06, 0x06, 0x03,
  0x00, 0x03, 0x8c, 0x01, 0x80, 0x21, 0xfc, 0x00, 0x80, 0x20, 0x78, 0x00, 0x00, 0x20, 0x00, 0x00,
  0x00, 0x20, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

// size 50x50
const unsigned char power_img64[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80,
  0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0,
  0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x0f, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xfe, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc,
  0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x1f, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xf0, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xfc, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x07,
  0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0x03, 0x00, 0x00,
  0x00, 0x00, 0x80, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x07, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xfc, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0,
  0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x01, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xf0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

//  size 25x25
const unsigned char power_img32[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0xe0, 0x00, 0x00, 0x00, 0xc0, 0x01, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0x80, 0x07, 0x00,
  0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x7c, 0x00,
  0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x80, 0x1f, 0x00,
  0x00, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0xf0, 0x00,
  0x00, 0x00, 0xc0, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x04,
  0x00, 0x00, 0x00, 0x00};

const unsigned char vtx_img64[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x07, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x80, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x03,
  0xE0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x0F, 0x00, 0x00,
  0x00, 0x00, 0x1C, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0x80, 0x01, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x20, 0x00, 0x00,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x10, 0x00, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00,
  0x00, 0xD8, 0xFF, 0xFF, 0xFF, 0xFF, 0x07, 0x00, 0x00, 0xF8, 0xFF, 0xFF,
  0xFF, 0xFF, 0x0F, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x00, 0xF0, 0xFF, 0xFF,
  0xFF, 0xFF, 0x07, 0x00, 0x00, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x0F, 0x00,
  0x00, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x1F, 0x00, 0xE0, 0xFF, 0xF7, 0xFB,
  0xFF, 0xFF, 0xFF, 0x03, 0xF0, 0xFF, 0xE3, 0xF1, 0xFF, 0xFF, 0xFF, 0x03,
  0xF0, 0xFF, 0xF1, 0xF8, 0xFF, 0xFF, 0xFF, 0x03, 0xF0, 0xFF, 0x78, 0xFC,
  0xFF, 0xFF, 0xFF, 0x03, 0xF0, 0xFF, 0x7C, 0xFE, 0xFF, 0xFF, 0xFF, 0x03,
  0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x03, 0xF0, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0x03, 0xE0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x03,
  0x00, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x1F, 0x00, 0x00, 0xF8, 0xFF, 0xFF,
  0xFF, 0xFF, 0x0F, 0x00, 0x00, 0xE0, 0xFF, 0xFF, 0xFF, 0xFF, 0x03, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
  0x00, 0x80, 0x01, 0x00, 0x00, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00,
  0x00, 0xC0, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0xC0, 0x00, 0x00,
  0x00, 0x80, 0x01, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x30, 0x00, 0x00,
  0x00, 0x00, 0x06, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00,
  0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x80, 0x03, 0x00, 0x00,
  0x00, 0x00, 0xC0, 0x01, 0xC0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x80, 0x07,
  0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x3F, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xF8, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

const unsigned char vtx_img32[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x78, 0x0F, 0x00, 0x00, 0x06, 0x20, 0x00, 0x00, 0x01, 0x40, 0x00,
  0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x01, 0x40, 0x00, 0x00, 0x01,
  0x40, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x02, 0x40, 0x00, 0x00, 0x03,
  0xC0, 0xFF, 0xFF, 0x01, 0xE0, 0xFF, 0xFF, 0x03, 0xFC, 0xDD, 0xFF, 0x1F,
  0xFC, 0xEC, 0xFF, 0x1F, 0xFC, 0xFE, 0xFF, 0x1F, 0xFC, 0xFF, 0xFF, 0x1F,
  0xE0, 0xFF, 0xFF, 0x03, 0x80, 0xFF, 0xFF, 0x00, 0x80, 0xFF, 0xFF, 0x01,
  0x80, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x01, 0x40, 0x00,
  0x00, 0x02, 0x40, 0x00, 0x00, 0x02, 0x20, 0x00, 0x00, 0x04, 0x10, 0x00,
  0x00, 0x18, 0x0C, 0x00, 0x00, 0xE0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };


const unsigned char joystick_img64[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xE0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0,
  0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x1F, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x70, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70,
  0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0x3C, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0x3E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x07, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFE, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0x3F, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x3E, 0x72, 0x3C, 0x3C, 0x00, 0x00, 0x00, 0x80, 0x7F, 0x70,
  0x3C, 0xFE, 0x01, 0x00, 0x00, 0xE0, 0x7F, 0x70, 0x3E, 0xFE, 0x03, 0x00,
  0x00, 0xF0, 0x7F, 0xF0, 0x1F, 0xFE, 0x07, 0x00, 0x00, 0xF0, 0x3F, 0xF0,
  0x07, 0xFC, 0x0F, 0x00, 0x00, 0xF8, 0x01, 0xF0, 0x01, 0xC0, 0x0F, 0x00,
  0x00, 0xF8, 0x00, 0xC0, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x7C, 0x00, 0x00,
  0x00, 0x00, 0x1F, 0x00, 0x00, 0x7C, 0x60, 0x00, 0x00, 0x06, 0x3E, 0x00,
  0x00, 0x3E, 0xF0, 0x00, 0x00, 0x0F, 0x3E, 0x00, 0x00, 0x3E, 0xF0, 0x00,
  0x00, 0x0F, 0x3C, 0x00, 0x00, 0x3E, 0xF0, 0x00, 0x00, 0x0F, 0x7C, 0x00,
  0x00, 0x1F, 0xF0, 0x01, 0x00, 0x0F, 0x7C, 0x00, 0x00, 0x1F, 0xFF, 0x0F,
  0xF0, 0xFF, 0xF8, 0x00, 0x00, 0x9F, 0xFF, 0x1F, 0xF8, 0xFF, 0xF9, 0x00,
  0x80, 0x8F, 0xFF, 0x1F, 0xF8, 0xFF, 0xF9, 0x00, 0x80, 0x0F, 0xFF, 0x0F,
  0xF0, 0xFF, 0xF0, 0x01, 0x80, 0x07, 0xF8, 0x01, 0x80, 0x1F, 0xF0, 0x01,
  0xC0, 0x07, 0xF0, 0x00, 0x00, 0x0F, 0xF0, 0x01, 0xC0, 0x07, 0xF0, 0x00,
  0x00, 0x0F, 0xE0, 0x03, 0xE0, 0x03, 0xF0, 0x00, 0x00, 0x0F, 0xE0, 0x03,
  0xE0, 0x03, 0x60, 0x00, 0x00, 0x06, 0xE0, 0x03, 0xE0, 0x03, 0x00, 0x00,
  0x00, 0x00, 0xC0, 0x07, 0xF0, 0x01, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x07,
  0xF0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x80, 0x07, 0xF0, 0x01, 0x00, 0x00,
  0x00, 0x00, 0x80, 0x0F, 0xF0, 0x00, 0x00, 0xFE, 0x7F, 0x00, 0x80, 0x0F,
  0xF8, 0x00, 0x80, 0xFF, 0xFF, 0x01, 0x80, 0x0F, 0xF8, 0x00, 0xC0, 0xFF,
  0xFF, 0x03, 0x00, 0x0F, 0xF8, 0x00, 0xE0, 0xFF, 0xFF, 0x03, 0x80, 0x0F,
  0xF0, 0x00, 0xE0, 0xFF, 0xFF, 0x07, 0x80, 0x0F, 0xF0, 0x01, 0xF0, 0x01,
  0xC0, 0x07, 0x80, 0x0F, 0xF0, 0x01, 0xF0, 0x01, 0x80, 0x0F, 0xC0, 0x07,
  0xF0, 0x03, 0xF8, 0x01, 0x80, 0x1F, 0xE0, 0x07, 0xE0, 0x0F, 0xFE, 0x00,
  0x80, 0x3F, 0xF0, 0x03, 0xC0, 0xFF, 0x7F, 0x00, 0x00, 0xFF, 0xFF, 0x03,
  0x80, 0xFF, 0x3F, 0x00, 0x00, 0xFE, 0xFF, 0x01, 0x00, 0xFF, 0x1F, 0x00,
  0x00, 0xFC, 0xFF, 0x00, 0x00, 0xFE, 0x0F, 0x00, 0x00, 0xF0, 0x3F, 0x00,
  0x00, 0xF0, 0x01, 0x00, 0x00, 0xC0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

const unsigned char joystick_img32[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xC0, 0x01, 0x00, 0x00, 0xC0, 0x07, 0x00, 0x00, 0x40, 0x06, 0x00,
  0x00, 0xF0, 0x07, 0x00, 0x00, 0xE0, 0x01, 0x00, 0x00, 0xF0, 0x07, 0x00,
  0x00, 0xC7, 0xE6, 0x00, 0xC0, 0xC7, 0xF7, 0x01, 0xC0, 0xC1, 0xC1, 0x03,
  0xE0, 0x00, 0x00, 0x07, 0x60, 0x0C, 0x30, 0x07, 0x70, 0x0C, 0x30, 0x06,
  0x70, 0x3F, 0xFC, 0x0E, 0x30, 0x3F, 0xFC, 0x0C, 0x38, 0x3F, 0xFC, 0x0C,
  0x18, 0x0C, 0x30, 0x1C, 0x18, 0x0C, 0x30, 0x18, 0x1C, 0x00, 0x00, 0x38,
  0x0C, 0x00, 0x00, 0x38, 0x0C, 0xF0, 0x0F, 0x30, 0x0C, 0xF8, 0x1F, 0x30,
  0x0C, 0x1C, 0x18, 0x38, 0x1C, 0x0C, 0x38, 0x38, 0x78, 0x0F, 0xF0, 0x1E,
  0xF0, 0x07, 0xE0, 0x0F, 0xE0, 0x01, 0xC0, 0x07, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

const unsigned char rxwifi_img64[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xFE, 0x0F, 0x00, 0x00, 0x00, 0x00, 0xF0, 0xFF, 0xFF, 0xFF,
  0xFF, 0x01, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x03, 0x00, 0x00,
  0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x03, 0x00, 0x00, 0xF0, 0xFF, 0xFF, 0xFF,
  0xFF, 0x01, 0x00, 0x00, 0xE0, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xF8, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x03,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x03, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xF8, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x01,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x01, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x01,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x01, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x01,
  0xF8, 0x1F, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x01, 0xFC, 0xFF, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x01, 0xFC, 0xFF, 0x03, 0x00, 0x00, 0x00, 0xF0, 0x01,
  0xF8, 0xFF, 0x0F, 0x00, 0x00, 0x00, 0xF0, 0x01, 0x00, 0xF8, 0x1F, 0x00,
  0x00, 0x00, 0xF0, 0x01, 0x00, 0xC0, 0x3F, 0x00, 0x00, 0x00, 0xF0, 0x01,
  0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0xF0, 0x01, 0x00, 0x00, 0xFC, 0x00,
  0x00, 0x00, 0xF0, 0x01, 0xFC, 0x01, 0xF8, 0x01, 0x00, 0x00, 0xF0, 0x01,
  0xFC, 0x0F, 0xF0, 0x01, 0x00, 0x00, 0xF0, 0x01, 0xFE, 0x3F, 0xE0, 0x03,
  0x00, 0x00, 0xF0, 0x01, 0xFC, 0x7F, 0xE0, 0x03, 0x00, 0x00, 0xF0, 0x01,
  0xF8, 0xFF, 0xC0, 0x07, 0x00, 0x00, 0xF0, 0x01, 0x00, 0xFC, 0x81, 0x07,
  0x00, 0x00, 0xF0, 0x01, 0x00, 0xF0, 0x83, 0x0F, 0x00, 0x00, 0xF0, 0x01,
  0x00, 0xE0, 0x83, 0x0F, 0x00, 0x00, 0xF0, 0x01, 0x00, 0xC0, 0x07, 0x0F,
  0x00, 0x00, 0xF0, 0x01, 0x00, 0xC0, 0x07, 0x0F, 0x00, 0x00, 0xF0, 0x01,
  0xE0, 0x80, 0x0F, 0x1F, 0x00, 0x00, 0xFF, 0x1F, 0xF8, 0x83, 0x0F, 0x1F,
  0x00, 0x80, 0xFF, 0x3F, 0xFC, 0x03, 0x0F, 0x1E, 0x00, 0xC0, 0xFF, 0x3F,
  0xFC, 0x07, 0x0F, 0x1E, 0x00, 0xC0, 0xFF, 0x7F, 0xFC, 0x07, 0x0F, 0x1E,
  0x00, 0xC0, 0xFF, 0x7F, 0xFC, 0x07, 0x1F, 0x1E, 0x00, 0xC0, 0xFF, 0x7F,
  0xFC, 0x03, 0x1F, 0x1E, 0x00, 0xC0, 0xFF, 0x7F, 0xFC, 0x03, 0x1F, 0x1E,
  0x00, 0xC0, 0xFF, 0x7F, 0xF8, 0x01, 0x0F, 0x0C, 0x00, 0xC0, 0xFF, 0x7F,
  0x00, 0x00, 0x0E, 0x00, 0x00, 0xC0, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xC0, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0x7F,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xC0, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0x7F,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xC0, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0x7F,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xC0, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0x3F,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xFE, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

const unsigned char rxwifi_img32[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0xFC, 0xFF, 0x1F, 0x00,
  0xFC, 0xFF, 0x1F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00,
  0x00, 0x1C, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
  0x00, 0x0C, 0x1C, 0x00, 0x00, 0x0C, 0xFE, 0x00, 0x00, 0x0C, 0xFC, 0x03,
  0x00, 0x0C, 0x80, 0x07, 0x00, 0x0C, 0x00, 0x0E, 0x00, 0x0C, 0x3E, 0x1C,
  0x00, 0x0C, 0xFE, 0x18, 0x00, 0x0C, 0xE0, 0x39, 0x00, 0x0C, 0x80, 0x31,
  0x00, 0x0C, 0x80, 0x33, 0x00, 0x7F, 0x1E, 0x33, 0x80, 0x7F, 0x1E, 0x33,
  0x80, 0x7F, 0x1E, 0x33, 0x80, 0x7F, 0x1E, 0x23, 0x80, 0x7F, 0x00, 0x00,
  0x80, 0x7F, 0x00, 0x00, 0x80, 0x7F, 0x00, 0x00, 0x80, 0x7F, 0x00, 0x00,
  0x80, 0x7F, 0x00, 0x00, 0x80, 0x7F, 0x00, 0x00, 0x80, 0x7F, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

const unsigned char backpack_img64[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x0F, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xF8, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC,
  0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3E, 0xFC, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F,
  0xE0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xE0, 0x01, 0x00, 0x00,
  0x00, 0x00, 0x80, 0x07, 0xE0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x80, 0x07,
  0xE0, 0x01, 0x00, 0x00, 0x00, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x00,
  0x00, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00, 0x00, 0xFE, 0xFF, 0xFF,
  0xFF, 0xFF, 0x7F, 0x00, 0x00, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00,
  0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x1E, 0x00, 0xF8,
  0x1F, 0x00, 0x78, 0x00, 0x00, 0x1E, 0x00, 0xFE, 0xFF, 0x00, 0x78, 0x00,
  0x00, 0x1E, 0x80, 0xFF, 0xFF, 0x03, 0x78, 0x00, 0x00, 0x1E, 0xE0, 0xFF,
  0xFF, 0x0F, 0x78, 0x00, 0x00, 0x1E, 0xF0, 0x0F, 0xE0, 0x1F, 0x78, 0x00,
  0x00, 0x1E, 0xF8, 0x01, 0x80, 0x3F, 0x78, 0x00, 0x00, 0x1E, 0x7C, 0x00,
  0x00, 0x7E, 0x78, 0x00, 0x00, 0x1E, 0x3E, 0x00, 0x00, 0xFC, 0x78, 0x00,
  0x00, 0x1E, 0x1F, 0xF0, 0x1F, 0xF0, 0x78, 0x00, 0x00, 0x3E, 0x0E, 0xFC,
  0x7F, 0xE0, 0x78, 0x00, 0x00, 0x3E, 0x00, 0xFF, 0xFF, 0x00, 0x7C, 0x00,
  0x00, 0x7E, 0x80, 0xFF, 0xFF, 0x03, 0x7C, 0x00, 0x00, 0xFE, 0xC0, 0x0F,
  0xF0, 0x07, 0x7F, 0x00, 0x00, 0xFE, 0xE3, 0x07, 0xC0, 0x87, 0x7F, 0x00,
  0x00, 0xFE, 0xEF, 0xF1, 0x9E, 0xEF, 0x7F, 0x00, 0x00, 0xDE, 0xEF, 0x3C,
  0x38, 0xE7, 0x73, 0x00, 0x00, 0x1E, 0x0F, 0xCE, 0x73, 0xF0, 0x70, 0x00,
  0x00, 0x1E, 0x70, 0xEF, 0xE7, 0x1C, 0x70, 0x00, 0x00, 0x1E, 0x70, 0xE0,
  0x0F, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0x70, 0xF0, 0x0F, 0x1E, 0x78, 0x00,
  0x00, 0x1E, 0xF0, 0xF0, 0x0F, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0xF0, 0xE0,
  0x0F, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0xF0, 0xE0, 0x0F, 0x0E, 0x70, 0x00,
  0x00, 0x1E, 0x70, 0xC0, 0x03, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0x70, 0x00,
  0x00, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0x70, 0x00,
  0x00, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0x70, 0x00,
  0x00, 0x1E, 0x78, 0x00, 0x00, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0x70, 0x00,
  0x00, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0x70, 0x00, 0x00, 0x1E, 0x70, 0x00,
  0x00, 0x0E, 0x70, 0x00, 0x00, 0x1E, 0x70, 0x00, 0x00, 0x0E, 0x70, 0x00,
  0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x1E, 0x00, 0x00,
  0x00, 0x00, 0x70, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00,
  0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x1E, 0x00, 0x00,
  0x00, 0x00, 0x70, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00,
  0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x1E, 0x00, 0x00,
  0x00, 0x00, 0x70, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00,
  0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x3C, 0x00, 0x00,
  0x00, 0x00, 0x78, 0x00, 0x00, 0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0x7F, 0x00,
  0x00, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0xF0, 0xFF, 0xFF,
  0xFF, 0xFF, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

const unsigned char backpack_img32[] =
{
  0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0xE0, 0x0F, 0x00,
  0x00, 0x30, 0x0C, 0x00, 0x00, 0x30, 0x08, 0x00, 0x00, 0x3F, 0x9F, 0x00,
  0xE0, 0xFF, 0xFF, 0x0F, 0x70, 0x0C, 0xC0, 0x0E, 0x70, 0xE0, 0x07, 0x0C,
  0x70, 0xF8, 0x1F, 0x0C, 0x70, 0x1C, 0x78, 0x0C, 0x70, 0x06, 0xE0, 0x0C,
  0x60, 0xE3, 0xC7, 0x0E, 0x60, 0xF0, 0x0F, 0x0E, 0xE0, 0x38, 0x1C, 0x0F,
  0xE0, 0x6B, 0xF6, 0x0F, 0x20, 0xB6, 0x6D, 0x0C, 0x20, 0xCC, 0x33, 0x0C,
  0x20, 0xCC, 0x33, 0x0C, 0x20, 0x8C, 0x31, 0x0C, 0x60, 0x0C, 0x30, 0x0C,
  0x60, 0x0C, 0x30, 0x0C, 0x60, 0x0C, 0x20, 0x0C, 0x60, 0x0C, 0x20, 0x0C,
  0x20, 0x00, 0x00, 0x0C, 0x60, 0x00, 0x00, 0x0C, 0x20, 0x00, 0x00, 0x0C,
  0x20, 0x00, 0x00, 0x0C, 0x20, 0x00, 0x00, 0x0C, 0xE0, 0xFF, 0xFF, 0x07,
  0xC0, 0xFF, 0xFF, 0x07, 0x00, 0x00, 0x00, 0x00, };

const unsigned char vrxwifi_img64[] =
{
  0x00, 0x00, 0x00, 0x00, 0xC0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFE, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xFF, 0xFF, 0x03, 0x00,
  0x00, 0x00, 0x00, 0xE0, 0xFF, 0xFF, 0x0F, 0x00, 0x00, 0x00, 0x00, 0xF8,
  0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0xFE, 0x0F, 0xE0, 0x7F, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x01, 0x00, 0xFF, 0x01, 0x00, 0x00, 0x80, 0x7F,
  0x00, 0x00, 0xFC, 0x03, 0x00, 0x00, 0xC0, 0x1F, 0x00, 0x00, 0xF0, 0x07,
  0x00, 0x00, 0xE0, 0x0F, 0x00, 0x00, 0xE0, 0x0F, 0x00, 0x00, 0xE0, 0x07,
  0xF0, 0x1F, 0xC0, 0x0F, 0x00, 0x00, 0xE0, 0x03, 0xFC, 0x7F, 0x80, 0x0F,
  0x00, 0x00, 0xE0, 0x01, 0xFF, 0xFF, 0x01, 0x0F, 0x00, 0x00, 0x00, 0xC0,
  0xFF, 0xFF, 0x07, 0x00, 0x00, 0x00, 0x00, 0xE0, 0xFF, 0xFF, 0x0F, 0x00,
  0x00, 0x00, 0x00, 0xF0, 0x0F, 0xE0, 0x1F, 0x00, 0x00, 0x00, 0x00, 0xF8,
  0x03, 0x80, 0x3F, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x01, 0x00, 0x7F, 0x00,
  0x00, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x38,
  0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xE0, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xF0, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x1F, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xF0, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xF0, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x1F, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xF0, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xE0, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x07, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x01,
  0x80, 0x3F, 0x00, 0x00, 0x00, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00,
  0x00, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0x03, 0x00, 0x00, 0xE0, 0xFF, 0xFF,
  0xFF, 0xFF, 0x07, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00,
  0xE0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x07, 0xF8, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0x1F, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x1F,
  0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F, 0xFC, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0x3F, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F,
  0x7E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x7E, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7E, 0x3E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7C,
  0x3E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7C, 0x7E, 0x00, 0x00, 0x80,
  0x01, 0x00, 0x00, 0x7E, 0x7E, 0x00, 0x00, 0xE0, 0x07, 0x00, 0x00, 0x7E,
  0x7E, 0x00, 0x00, 0xF0, 0x0F, 0x00, 0x00, 0x7E, 0x7E, 0x00, 0x00, 0xF8,
  0x1F, 0x00, 0x00, 0x7E, 0xFE, 0x00, 0x00, 0xFC, 0x3F, 0x00, 0x00, 0x7F,
  0xFC, 0x7F, 0xC0, 0xFF, 0xFF, 0x07, 0xFE, 0x3F, 0xFC, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0x3F, 0xF8, 0xFF, 0xFF, 0x7F, 0xFE, 0xFF, 0xFF, 0x1F,
  0xF0, 0xFF, 0xFF, 0x3F, 0xFC, 0xFF, 0xFF, 0x0F, 0xC0, 0xFF, 0xFF, 0x0F,
  0xF0, 0xFF, 0xFF, 0x03, 0x00, 0xFE, 0xFF, 0x07, 0xE0, 0xFF, 0x7F, 0x00,
  0x00, 0xFC, 0xFF, 0x03, 0xE0, 0xFF, 0x1F, 0x00, 0x00, 0xF0, 0xFF, 0x01,
  0x80, 0xFF, 0x0F, 0x00, 0x00, 0xC0, 0x7F, 0x00, 0x00, 0xFE, 0x03, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

const unsigned char vrxwifi_img32[] =
{
  0x00, 0x00, 0x7E, 0x00, 0x00, 0x80, 0xFF, 0x03, 0x00, 0xE0, 0xFF, 0x07,
  0x00, 0xF0, 0x00, 0x1F, 0x00, 0x38, 0x00, 0x3C, 0x00, 0x1C, 0x7E, 0x38,
  0x00, 0x88, 0xFF, 0x01, 0x00, 0xC0, 0xE7, 0x03, 0x00, 0xE0, 0x01, 0x07,
  0x00, 0x60, 0x00, 0x06, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x3C, 0x00,
  0x00, 0x00, 0x7C, 0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x38, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00,
  0x80, 0xFF, 0xFF, 0x01, 0xF8, 0xFF, 0xFF, 0x1F, 0xFE, 0xFF, 0xFF, 0x7F,
  0xFE, 0xFF, 0xFF, 0x7F, 0x0E, 0x00, 0x00, 0x70, 0x07, 0x00, 0x00, 0xE0,
  0x07, 0x00, 0x00, 0xE0, 0x07, 0xC0, 0x03, 0xE0, 0x0E, 0xE0, 0x07, 0x70,
  0xFE, 0xFF, 0xFF, 0x7F, 0xFC, 0x7F, 0xFE, 0x3F, 0xF8, 0x3F, 0xFC, 0x1F,
  0xE0, 0x1F, 0xF8, 0x03, 0x00, 0x02, 0x40, 0x00, };

//  size 64x64
const unsigned char powersaving_img64[] = {
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0,
  0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x0F, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x1F,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x83, 0x0F, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xF8, 0xF8, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0xFE,
  0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9E, 0x3F, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xCE, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE6, 0x01,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x70, 0x80, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xC0,
  0x9F, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0xFC, 0x07, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x70, 0xF8, 0x0F, 0x00, 0x00, 0x00, 0x80, 0x03, 0x38,
  0x78, 0x0E, 0x00, 0x00, 0x00, 0xC0, 0x07, 0x1C, 0x3C, 0x1C, 0x00, 0x00,
  0x00, 0xE0, 0x0F, 0x0E, 0x1E, 0x1C, 0x00, 0x00, 0x00, 0xE0, 0x0E, 0x07,
  0x0F, 0x1C, 0x00, 0x00, 0x00, 0x70, 0x8C, 0x83, 0x07, 0x0E, 0x00, 0x00,
  0x00, 0x70, 0xCC, 0xC1, 0x03, 0x1F, 0x00, 0x00, 0x00, 0x38, 0xFE, 0xE0,
  0x81, 0x3F, 0x00, 0x00, 0x00, 0x38, 0x7E, 0xF0, 0xC0, 0x3F, 0x00, 0x00,
  0x00, 0x1C, 0x3F, 0x78, 0xE0, 0x71, 0x00, 0x00, 0x00, 0x1C, 0x1F, 0x3C,
  0xF0, 0x70, 0x00, 0x00, 0x00, 0x8E, 0x0F, 0x1E, 0x78, 0x70, 0x60, 0x06,
  0x00, 0x8E, 0x07, 0x0F, 0x3C, 0x38, 0x60, 0x0E, 0x00, 0xC7, 0x83, 0x07,
  0x1E, 0x3C, 0x60, 0x0E, 0x00, 0xC7, 0xC1, 0x03, 0x0F, 0x1E, 0x60, 0x0E,
  0x80, 0x83, 0x80, 0x81, 0x07, 0x3F, 0x60, 0x0E, 0x80, 0x03, 0x00, 0xC0,
  0x83, 0x7F, 0x60, 0x0E, 0xC0, 0x01, 0x00, 0xE0, 0xC1, 0x73, 0x70, 0x0E,
  0xC0, 0x01, 0x00, 0xF0, 0xE0, 0xE1, 0x70, 0x06, 0xC0, 0x01, 0x00, 0x78,
  0xF0, 0x60, 0x70, 0x07, 0xC0, 0x00, 0x00, 0x10, 0x78, 0x70, 0x38, 0x07,
  0xE0, 0x00, 0x00, 0x00, 0x3C, 0x78, 0xB8, 0x03, 0xE0, 0x00, 0x00, 0x00,
  0x1E, 0x3C, 0xB8, 0x03, 0xE0, 0x00, 0x00, 0x00, 0x0F, 0x1E, 0xDC, 0x01,
  0xE0, 0x00, 0x00, 0x80, 0x07, 0x0F, 0xDE, 0x01, 0xE0, 0x00, 0x00, 0xC0,
  0x83, 0x07, 0xEF, 0x00, 0xE0, 0x00, 0x00, 0xC0, 0xC1, 0x03, 0xF7, 0x00,
  0xC0, 0x00, 0x00, 0x00, 0xE0, 0x81, 0x73, 0x00, 0xC0, 0x01, 0x00, 0x00,
  0xF0, 0x00, 0x39, 0x00, 0xC0, 0x01, 0x00, 0x00, 0x78, 0x00, 0x1C, 0x00,
  0x80, 0x03, 0x00, 0x00, 0x3C, 0x00, 0x0C, 0x00, 0x80, 0x03, 0x00, 0x00,
  0x1E, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00,
  0x00, 0x0F, 0x00, 0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x1E, 0x00, 0xC0,
  0x03, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x00, 0xE0, 0x01, 0x00, 0x00, 0x00,
  0x00, 0xF8, 0x00, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x07, 0x7F,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0x1F, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

//  size 32x32
const unsigned char powersaving_img32[] = {
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0x00,
  0x00, 0xF8, 0x00, 0x00, 0x00, 0xCC, 0x03, 0x00, 0x00, 0xF6, 0x01, 0x00,
  0x00, 0x1B, 0x00, 0x00, 0x00, 0x0C, 0x03, 0x00, 0x00, 0x80, 0x1F, 0x00,
  0x00, 0x40, 0x3E, 0x00, 0x80, 0x23, 0x26, 0x00, 0xC0, 0x12, 0x23, 0x00,
  0x40, 0x8E, 0x71, 0x00, 0x60, 0xC7, 0x58, 0x00, 0x20, 0x63, 0x4C, 0x00,
  0xB0, 0x33, 0x66, 0x2C, 0x90, 0x18, 0x73, 0x3C, 0x18, 0x80, 0xF9, 0x3C,
  0x08, 0xC0, 0xCC, 0x34, 0x08, 0x00, 0x46, 0x14, 0x08, 0x00, 0x63, 0x1E,
  0x08, 0x80, 0x31, 0x0B, 0x08, 0x00, 0x18, 0x0D, 0x08, 0x00, 0x0C, 0x06,
  0x18, 0x00, 0x06, 0x02, 0x30, 0x00, 0x03, 0x00, 0x60, 0x80, 0x01, 0x00,
  0xC0, 0xE1, 0x00, 0x00, 0x80, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

//  size 64x64
const unsigned char fan_img64[] = {
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x03,
  0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0F, 0x78, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x1E, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38,
  0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x1C, 0x03, 0x00, 0xF0,
  0x0F, 0x00, 0x80, 0x30, 0x1C, 0x07, 0x00, 0xFF, 0xFF, 0x00, 0xC0, 0x31,
  0x1C, 0x07, 0xC0, 0x7F, 0xFF, 0x07, 0xC0, 0x31, 0x1C, 0x03, 0xF0, 0x03,
  0x80, 0x1F, 0x80, 0x30, 0x1C, 0x00, 0x7C, 0x00, 0x00, 0x3E, 0x00, 0x30,
  0x1C, 0x00, 0x1E, 0x00, 0x00, 0x78, 0x00, 0x30, 0x1C, 0x00, 0x07, 0xE0,
  0x3F, 0xE0, 0x01, 0x30, 0x1C, 0x80, 0x03, 0xF0, 0xFF, 0xC0, 0x03, 0x30,
  0x1C, 0xC0, 0x01, 0x3C, 0xE0, 0x01, 0x07, 0x30, 0x1C, 0xE0, 0x00, 0x1C,
  0x80, 0x03, 0x0E, 0x30, 0x1C, 0x70, 0x00, 0x0E, 0x00, 0x03, 0x0E, 0x30,
  0x1C, 0x38, 0x00, 0x06, 0x00, 0x07, 0x1C, 0x30, 0x1C, 0x18, 0x00, 0x06,
  0x00, 0x06, 0x38, 0x30, 0x1C, 0x1C, 0x00, 0x06, 0x00, 0x07, 0x30, 0x30,
  0x1C, 0x0C, 0x00, 0x0E, 0x00, 0x03, 0x70, 0x30, 0x1C, 0x0E, 0x00, 0x0E,
  0xC0, 0x03, 0x60, 0x30, 0x1C, 0x06, 0x00, 0x0C, 0xE0, 0x01, 0xE0, 0x30,
  0x1C, 0x06, 0x00, 0x1C, 0x70, 0x00, 0xE0, 0x30, 0x1C, 0x07, 0x00, 0x18,
  0x38, 0x00, 0xC0, 0x30, 0x1C, 0x03, 0x00, 0xB8, 0x1B, 0x00, 0xC0, 0x30,
  0x1C, 0x03, 0x00, 0xF0, 0x1F, 0x00, 0xC0, 0x31, 0x1C, 0x03, 0x00, 0xF0,
  0x1F, 0x00, 0xC0, 0x31, 0x1C, 0x03, 0x00, 0x38, 0x38, 0x00, 0x80, 0x31,
  0x9C, 0x03, 0x1E, 0x18, 0xF0, 0x0F, 0x80, 0x31, 0x9C, 0x03, 0x7F, 0x1C,
  0xF0, 0x7F, 0x80, 0x31, 0x9C, 0x83, 0xF3, 0x1F, 0x70, 0xFC, 0x80, 0x31,
  0x9C, 0xC3, 0xC1, 0x1F, 0x70, 0xE0, 0x81, 0x31, 0x1C, 0xC3, 0x00, 0x18,
  0x30, 0x80, 0x83, 0x31, 0x1C, 0xC3, 0x00, 0x38, 0x38, 0x00, 0xC3, 0x31,
  0x1C, 0xC3, 0x00, 0xF0, 0x1F, 0x00, 0xC3, 0x31, 0x1C, 0xC3, 0x00, 0xE0,
  0x1F, 0x00, 0xC3, 0x30, 0x1C, 0xC7, 0x00, 0xE0, 0x1F, 0x00, 0xC3, 0x30,
  0x1C, 0xC6, 0x01, 0x60, 0x38, 0x00, 0xE3, 0x30, 0x1C, 0xC6, 0x01, 0x70,
  0x38, 0x80, 0xE3, 0x30, 0x1C, 0x8E, 0x03, 0x38, 0x30, 0x80, 0x61, 0x30,
  0x1C, 0x8C, 0x03, 0x1C, 0x30, 0xC0, 0x71, 0x30, 0x1C, 0x1C, 0x07, 0x1E,
  0x30, 0xE0, 0x30, 0x30, 0x1C, 0x18, 0x1E, 0x0F, 0x30, 0x70, 0x38, 0x30,
  0x1C, 0x38, 0xFC, 0x03, 0xF0, 0x3C, 0x1C, 0x30, 0x1C, 0x70, 0xF0, 0x01,
  0xE0, 0x1F, 0x0E, 0x30, 0x1C, 0xE0, 0x00, 0x00, 0x80, 0x07, 0x0F, 0x30,
  0x1C, 0xC0, 0x01, 0x00, 0x00, 0x00, 0x07, 0x30, 0x1C, 0x80, 0x03, 0x00,
  0x00, 0xC0, 0x03, 0x30, 0x1C, 0x00, 0x07, 0x00, 0x00, 0xE0, 0x01, 0x30,
  0x1C, 0x00, 0x1E, 0x00, 0x00, 0xF8, 0x00, 0x30, 0x1C, 0x00, 0x7C, 0x00,
  0x00, 0x3C, 0x00, 0x30, 0x1C, 0x03, 0xF0, 0x03, 0x80, 0x1F, 0x80, 0x30,
  0x1C, 0x07, 0xC0, 0xFF, 0xFF, 0x07, 0xC0, 0x31, 0x1C, 0x07, 0x00, 0xFF,
  0xFF, 0x00, 0xC0, 0x31, 0x1C, 0x02, 0x00, 0xF0, 0x0F, 0x00, 0x80, 0x30,
  0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x38, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x38, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1E,
  0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0F, 0xC0, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, };

//  size 32x32
const unsigned char fan_img32[] = {
  0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xFF, 0x3F, 0x04, 0x00, 0x00, 0x60,
  0x02, 0x00, 0x00, 0x40, 0x12, 0xF0, 0x1F, 0x48, 0x02, 0x0C, 0x70, 0x40,
  0x02, 0x83, 0xC3, 0x40, 0x82, 0xE1, 0x8E, 0x41, 0xC2, 0x20, 0x10, 0x43,
  0x42, 0x20, 0x10, 0x46, 0x22, 0x20, 0x10, 0x44, 0x22, 0x20, 0x18, 0x4C,
  0x12, 0x20, 0x04, 0x48, 0x12, 0xC0, 0x07, 0x48, 0x12, 0xC0, 0x06, 0x48,
  0x12, 0x27, 0x7C, 0x48, 0x92, 0x3D, 0xC4, 0x48, 0x92, 0x60, 0x04, 0x49,
  0x92, 0xC0, 0x03, 0x49, 0x92, 0xC0, 0x06, 0x49, 0xA2, 0x41, 0x84, 0x4D,
  0x22, 0x21, 0x84, 0x44, 0x42, 0x3E, 0x4C, 0x46, 0xC2, 0x00, 0x38, 0x43,
  0x82, 0x01, 0x80, 0x41, 0x02, 0x03, 0xC0, 0x40, 0x02, 0x0C, 0x70, 0x40,
  0x12, 0xF0, 0x1F, 0x48, 0x02, 0x00, 0x00, 0x40, 0x04, 0x00, 0x00, 0x60,
  0xF8, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, };

#endif
