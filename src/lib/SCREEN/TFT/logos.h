#include <pgmspace.h>

// 60x60x1 monochrome, extended to 64x60 for drawBitmap()
const unsigned char elrs_banner_bmp[] PROGMEM ={
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x07, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xff, 0xfc, 0x00, 0x00, 0x00,
0x00, 0x00, 0x3f, 0xe1, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x1f, 0x80, 0x00, 0x00,
0x00, 0x01, 0xf8, 0x00, 0x07, 0xc0, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x7f, 0x83, 0xe0, 0x00, 0x00,
0x00, 0x03, 0xc3, 0xff, 0xe0, 0xf0, 0x00, 0x00, 0x00, 0x03, 0x8f, 0xff, 0xf8, 0x60, 0x00, 0x00,
0x00, 0x00, 0x1f, 0xc0, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x3e, 0x00, 0x00, 0x00,
0x00, 0x00, 0x3c, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x7f, 0x86, 0x00, 0x00, 0x00,
0x00, 0x00, 0x01, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0xe0, 0x00, 0x00, 0x00,
0x00, 0x00, 0x03, 0xc1, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x60, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x00,
0x00, 0x7c, 0x00, 0x16, 0x00, 0x0f, 0x00, 0x00, 0x00, 0xee, 0x00, 0x00, 0x00, 0x19, 0xc0, 0x00,
0x01, 0xc6, 0x3f, 0xe9, 0xff, 0x10, 0xe0, 0x00, 0x03, 0x86, 0x7f, 0xff, 0xff, 0x30, 0x70, 0x00,
0x07, 0x06, 0x00, 0x00, 0x00, 0x18, 0x30, 0x00, 0x0e, 0x0c, 0x00, 0x00, 0x00, 0x1c, 0x18, 0x00,
0x1c, 0x18, 0xc1, 0xff, 0xc1, 0x8e, 0x0c, 0x00, 0x18, 0x31, 0xf1, 0xff, 0xc3, 0xe7, 0x06, 0x00,
0x30, 0x61, 0x31, 0x80, 0x43, 0x63, 0x86, 0x00, 0x30, 0xc3, 0x31, 0x80, 0x42, 0x61, 0x83, 0x00,
0x30, 0xc1, 0xf1, 0x80, 0x43, 0xe0, 0xc3, 0x00, 0x20, 0x80, 0xe1, 0x80, 0x41, 0xc0, 0xc3, 0x00,
0x20, 0x80, 0x01, 0xff, 0xc0, 0x00, 0xc3, 0x00, 0x60, 0x80, 0xf9, 0xff, 0xc7, 0xc0, 0xc3, 0x00,
0x60, 0x83, 0xfc, 0x00, 0x1f, 0xf0, 0xc1, 0x00, 0x60, 0x87, 0x06, 0x00, 0x38, 0x38, 0xc1, 0x00,
0x60, 0x8e, 0x03, 0x00, 0x30, 0x18, 0xc1, 0x80, 0x60, 0x8d, 0xe1, 0x00, 0x63, 0xcc, 0xc1, 0x80,
0x60, 0xcb, 0xf1, 0x80, 0x67, 0xec, 0xc1, 0x80, 0x60, 0xc7, 0x19, 0x80, 0x46, 0x30, 0xc1, 0x80,
0x60, 0xce, 0x19, 0x8c, 0x64, 0x19, 0x81, 0x80, 0x60, 0x7c, 0x19, 0x88, 0x66, 0x0f, 0x81, 0x80,
0x60, 0x78, 0x33, 0x00, 0x67, 0x07, 0x01, 0x80, 0x61, 0xf0, 0x67, 0x00, 0x33, 0x83, 0xe1, 0x80,
0x60, 0x00, 0xee, 0x00, 0x1d, 0xc0, 0x01, 0x80, 0x60, 0x01, 0xdc, 0x00, 0x0c, 0xe0, 0x01, 0x80,
0x60, 0x03, 0x80, 0x00, 0x00, 0x70, 0x01, 0x80, 0x60, 0x07, 0x00, 0x00, 0x00, 0x38, 0x01, 0x80,
0x60, 0x0e, 0x03, 0xff, 0xe0, 0x1c, 0x01, 0x80, 0x60, 0x1c, 0x03, 0x00, 0x20, 0x0c, 0x01, 0x80,
0x60, 0x18, 0x03, 0x00, 0x20, 0x06, 0x01, 0x80, 0x60, 0x31, 0xff, 0xff, 0xff, 0xe2, 0x01, 0x80,
0x60, 0x33, 0xff, 0xff, 0xff, 0xe3, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

const unsigned short elrs_rate[0xE10] PROGMEM ={
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0010 (16)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0020 (32)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0030 (48)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0040 (64)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0050 (80)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0060 (96)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0070 (112)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0080 (128)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0090 (144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00A0 (160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00B0 (176)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x00C0 (192)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xC6F5, 0xAE71, 0xA630,   // 0x00D0 (208)
0x9DF0, 0x9DF0, 0x9DF1, 0x95B1, 0x95B1, 0x95B1, 0x8D71, 0x95B2, 0xBE77, 0xE77D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00E0 (224)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00F0 (240)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0100 (256)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xDF79, 0xB6B2, 0x9DEE, 0x95EE, 0x95EE, 0x95AF, 0x8DAF, 0x8D6F,   // 0x0110 (272)
0x8D6F, 0x8570, 0x8530, 0x7D30, 0x9DB4, 0xCEFA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0120 (288)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0130 (304)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0140 (320)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xCF36, 0xA66F, 0x9DEE, 0x95EE, 0x95EE, 0x95AF, 0x8DAF, 0x8D6F, 0x8D6F, 0x8570, 0x8530, 0x7D30,   // 0x0150 (336)
0x8531, 0xBE78, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0160 (352)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0170 (368)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0180 (384)
0xDF7A, 0xB6B2, 0x9E2E, 0x95EE, 0x95EE, 0x95AF, 0x8DAF, 0x8D6F, 0x8D6F, 0x8570, 0x8530, 0x8530, 0x9DB4, 0xD6FB, 0xFFFF, 0xFFFF,   // 0x0190 (400)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01A0 (416)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01B0 (432)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xE77B, 0xD738, 0xC6F5,   // 0x01C0 (448)
0xA631, 0x95AF, 0x8DAF, 0x8D6F, 0x8D6F, 0x95B2, 0xBE76, 0xCEF9, 0xE77C, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01D0 (464)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01E0 (480)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01F0 (496)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xEFBC, 0xE77A, 0xC6F6, 0x9DF0, 0x95AF, 0x8DAF, 0x8D6F,   // 0x0200 (512)
0x8D6F, 0x95B2, 0xC6B7, 0xDF3B, 0xE77C, 0xEFBD, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0210 (528)
0xFFFF, 0xFFFF, 0xEFBE, 0xC6BB, 0xC67B, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0220 (544)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0230 (560)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE77A, 0xC6F4, 0xAE71, 0xA631, 0x9DF0, 0x95EF, 0x8DAF, 0x8D6F, 0x8D70, 0x95B1, 0x95B2, 0x9DF3,   // 0x0240 (576)
0xA5F5, 0xB637, 0xCEFA, 0xE77D, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xB63A, 0x6C36,   // 0x0250 (592)
0x6C36, 0xB5FB, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0260 (608)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE,   // 0x0270 (624)
0xDF79, 0xBEB3, 0xAE71, 0xBEF4, 0xCEF7, 0xB674, 0x95AF, 0x8DB0, 0xB675, 0xC6B7, 0xAE35, 0x9DF3, 0x8D72, 0x7D31, 0x8D33, 0x9574,   // 0x0280 (640)
0xADF7, 0xD6FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xBE3A, 0x7476, 0x5354, 0x4B54, 0x63D6, 0xA5BA, 0xE73E,   // 0x0290 (656)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02A0 (672)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xDF7A, 0xDF79, 0xE7BB,   // 0x02B0 (688)
0xF7BD, 0xCEF8, 0x95F0, 0x95B0, 0xD6F9, 0xEFBD, 0xE77C, 0xD6FA, 0xBEB8, 0xB637, 0x9DB5, 0x84F2, 0x7CB2, 0x9575, 0xB638, 0xD6FB,   // 0x02C0 (704)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEFC, 0x7CB6, 0x5394, 0x5354, 0x4B54, 0x4B54, 0x5BD6, 0xADFB, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02D0 (720)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02E0 (736)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xBEB6, 0xBEB6,   // 0x02F0 (752)
0xE77C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77D, 0xC6B9, 0x9DB5, 0x74B2, 0x6C72, 0x8535, 0xBE79, 0xEF7D, 0xFFFF, 0xFFFF,   // 0x0300 (768)
0xCEBC, 0x6434, 0x5393, 0x5354, 0x4B54, 0x4B54, 0x6C36, 0xADFB, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0310 (784)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0320 (800)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xF7BD, 0xF7BD, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0330 (816)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xCEFA, 0x84F3, 0x6C71, 0x6C73, 0x74B4, 0x9DB7, 0xE77D, 0xFFFF, 0xD6FC, 0x6C35, 0x5393, 0x5394,   // 0x0340 (832)
0x4B54, 0x6C36, 0xB63B, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0350 (848)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB, 0xDF79, 0xDF79, 0xDF79, 0xDF79, 0xDF79, 0xEFBB,   // 0x0360 (864)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0370 (880)
0xBEB9, 0x7CF2, 0xA5B7, 0xBE7A, 0x9576, 0x6C73, 0x9536, 0xADF9, 0x9578, 0x6C35, 0x74B6, 0x7CB6, 0x7CB7, 0xBE7B, 0xEFBE, 0xFFFF,   // 0x0380 (896)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0390 (912)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xE77A, 0xC6F4, 0xB6B2, 0xB6B2, 0xB6B2, 0xB6B2, 0xB6B2, 0xC6F4, 0xE77A, 0xF7FE, 0xFFFF, 0xFFFF,   // 0x03A0 (928)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6BA, 0xAE38, 0xDF3C, 0xFFFF,   // 0x03B0 (944)
0xD6FC, 0x9576, 0x5BD2, 0x63D3, 0x7475, 0x9D78, 0xC67B, 0xC67B, 0xC6BC, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03C0 (960)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD,   // 0x03D0 (976)
0xCEF6, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F, 0xCF36, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03E0 (992)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0x8D36, 0x6433,   // 0x03F0 (1008)
0x6C75, 0xC6BB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0400 (1024)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xD738, 0xAE70, 0x9E2D, 0x9E2D,   // 0x0410 (1040)
0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xDF78, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0420 (1056)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77D, 0x9D78, 0x6434, 0x8D37, 0xE73D, 0xFFFF,   // 0x0430 (1072)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0440 (1088)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBD, 0xDF79, 0xD737, 0xD737, 0xD737, 0xD737, 0xD737, 0xDF79,   // 0x0450 (1104)
0xEFBD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0460 (1120)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x9537, 0x63D4, 0x9578, 0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0470 (1136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0480 (1152)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0490 (1168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04A0 (1184)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFC, 0x7CB6, 0x6435, 0xADFA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04B0 (1200)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xF7BD, 0xF7BD,   // 0x04C0 (1216)
0xF7BE, 0xFFFE, 0xFFFE, 0xFFFE, 0xFFFE, 0xFFFE, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04D0 (1232)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04E0 (1248)
0xEF7E, 0xA5B9, 0x5BD4, 0x7CB7, 0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04F0 (1264)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xDF38, 0xC6F4, 0xBEB3, 0xBEB3, 0xBEB3, 0xBEB3, 0xBEF3, 0xBEF3,   // 0x0500 (1280)
0xBEB3, 0xBEB3, 0xBEB3, 0xCEF6, 0xEFBB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0510 (1296)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xBE3A, 0x7CB6, 0x5393, 0x5394,   // 0x0520 (1312)
0x9D79, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0530 (1328)
0xFFFF, 0xFFFF, 0xFFFF, 0xDF7A, 0xB6B1, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F,   // 0x0540 (1344)
0xD737, 0xF7FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0550 (1360)
0xF7BE, 0xD6BA, 0xD6BA, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFC, 0x9D78, 0x7475, 0x84F7, 0x6C35, 0x6C36, 0xD6FC, 0xFFFF, 0xFFFF,   // 0x0560 (1376)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF79,   // 0x0570 (1392)
0xB6B1, 0xA66E, 0xA62E, 0xA62E, 0xA62E, 0xA62E, 0xA62E, 0xA62E, 0xA62E, 0xA62E, 0xA62E, 0xAE70, 0xD737, 0xF7BD, 0xFFFF, 0xFFFF,   // 0x0580 (1408)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xADB6, 0x7BCF, 0x94B2, 0xDEFB,   // 0x0590 (1424)
0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0xBE3A, 0xBE3A, 0xD6FC, 0x9D79, 0x5B95, 0x9D79, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05A0 (1440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xDF79, 0xC6F5, 0xBEB3, 0xBEF3,   // 0x05B0 (1456)
0xBEF4, 0xBEF4, 0xBEF4, 0xBEF4, 0xBEF3, 0xBEF3, 0xBEF4, 0xCF36, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05C0 (1472)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0x94B2, 0x4A8A, 0x4208, 0x8430, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF,   // 0x05D0 (1488)
0xF7BE, 0xF7BE, 0xFFFF, 0xB63B, 0x5394, 0x63D5, 0xC67B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05E0 (1504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBD, 0xEFBC, 0xEFBC, 0xEFBC, 0xEFBC, 0xEFBC, 0xEFBC,   // 0x05F0 (1520)
0xEFBC, 0xEFBC, 0xEFBC, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0600 (1536)
0xF7BE, 0xBDF7, 0x7BCF, 0x4A49, 0x3186, 0x630C, 0xC638, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FC,   // 0x0610 (1552)
0x7CB7, 0x4B54, 0xADBA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0620 (1568)
0xFFFE, 0xF7BD, 0xF7BD, 0xF7BD, 0xF7BD, 0xF7FE, 0xF7FE, 0xF7FE, 0xF7FE, 0xF7FE, 0xF7FE, 0xF7FE, 0xF7FE, 0xF7FE, 0xF7FE, 0xFFFF,   // 0x0630 (1584)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0xAD75, 0x6B4D, 0x3186, 0x31C7,   // 0x0640 (1600)
0x5ACB, 0xBDF6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xADFA, 0x5B95, 0x9539, 0xF7BE,   // 0x0650 (1616)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xDF79, 0xCF37, 0xCEF6, 0xCF36,   // 0x0660 (1632)
0xCF36, 0xCF37, 0xCF37, 0xCF37, 0xCF37, 0xCF37, 0xCF37, 0xCF37, 0xCF37, 0xCF37, 0xD737, 0xDF79, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0670 (1648)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xD6BA, 0x8C71, 0x4A49, 0x3186, 0x3186, 0x4208, 0xA534, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0680 (1664)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x63D6, 0x7477, 0xD6FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0690 (1680)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xD736, 0xAE70, 0xAE6F, 0xA66F, 0xA66F, 0xA66F, 0xAE6F, 0xAE6F, 0xAE6F,   // 0x06A0 (1696)
0xAE6F, 0xAE6F, 0xAE6F, 0xAE6F, 0xA66F, 0xAE6F, 0xA66F, 0xB6B1, 0xDF78, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06B0 (1712)
0xEF7D, 0xB5B6, 0x6B4D, 0x4208, 0x3186, 0x31C7, 0x39C7, 0x8C71, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06C0 (1728)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x6436, 0x5B96, 0xB63B, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06D0 (1744)
0xFFFF, 0xFFFF, 0xFFFE, 0xC733, 0xA66D, 0x9E2D, 0xA62D, 0xA66D, 0xA66D, 0xA66D, 0xA66D, 0xA66D, 0xA66D, 0xA66D, 0xA66D, 0xA66D,   // 0x06E0 (1760)
0xA66D, 0xA66D, 0x9E2D, 0xA66D, 0xD776, 0xF7FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6BA, 0x94B2, 0x5ACB, 0x3186, 0x3186,   // 0x06F0 (1776)
0x3186, 0x39C7, 0x6B4D, 0xD679, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0700 (1792)
0xE77E, 0x7477, 0x5355, 0x9D7A, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7B8,   // 0x0710 (1808)
0xC732, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xBEF0, 0xC732,   // 0x0720 (1824)
0xEFBA, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xCE79, 0x8430, 0x4208, 0x3186, 0x3186, 0x3186, 0x31C7, 0x5ACB, 0xB5B6, 0xF7BE,   // 0x0730 (1840)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0x84B7, 0x4B55, 0x9539,   // 0x0740 (1856)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xEFBC, 0xEFBB, 0xEFBC,   // 0x0750 (1872)
0xEFBC, 0xEFBC, 0xEFBC, 0xEFBC, 0xEFBC, 0xEFBC, 0xEFBC, 0xEFBC, 0xEFBC, 0xEFBB, 0xEFBC, 0xF7BD, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0760 (1888)
0xF7BE, 0xB5B6, 0x630C, 0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x4A49, 0x94B2, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0770 (1904)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xE73D, 0xBE3B, 0x7477, 0x5355, 0x9539, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0780 (1920)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0790 (1936)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xB5B6, 0x630C, 0x3186, 0x3186,   // 0x07A0 (1952)
0x3186, 0x3186, 0x3186, 0x31C6, 0x8430, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07B0 (1968)
0xFFFF, 0xFFFF, 0xD6FC, 0x9D78, 0x6C36, 0x5395, 0x5B96, 0x9D7A, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E,   // 0x07C0 (1984)
0xDEFD, 0xC67C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C, 0xBE3C,   // 0x07D0 (2000)
0xBE3C, 0xBE3C, 0xBE3C, 0xCEBD, 0xEF7F, 0xFFFF, 0xFFFF, 0xEF7D, 0x738D, 0x3186, 0x3186, 0x3186, 0x3186, 0x31C6, 0x39C7, 0x630C,   // 0x07E0 (2016)
0xCE38, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFD, 0xA5B9,   // 0x07F0 (2032)
0x7476, 0x5B95, 0x5B95, 0x9D7A, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5FB, 0x8CF9, 0x5B96, 0x5356, 0x5356,   // 0x0800 (2048)
0x5356, 0x4B56, 0x4B56, 0x4B56, 0x4B56, 0x4B56, 0x4B56, 0x4B56, 0x4B56, 0x4B56, 0x4B56, 0x4B56, 0x4B16, 0x4B16, 0x4B16, 0x6C38,   // 0x0810 (2064)
0xBE7D, 0xF7BF, 0xFFFF, 0xEF7D, 0x6B4C, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x528A, 0xB5B6, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0820 (2080)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xF7BE, 0xD6FC, 0x7CB7, 0x5355, 0x9539,   // 0x0830 (2096)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7CB8, 0x5396, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0840 (2112)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x42D5, 0x4B56, 0x9D7A, 0xE77E, 0xFFFF, 0xF7BE,   // 0x0850 (2128)
0x94B1, 0x4A49, 0x39C6, 0x3186, 0x39C7, 0x4A49, 0x9472, 0xEF3C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0860 (2144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77E, 0x7CB7, 0x4B55, 0x9539, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0870 (2160)
0xFFFF, 0xFFFF, 0xFFFF, 0xA5BA, 0x7CB8, 0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x5B96,   // 0x0880 (2176)
0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x5B96, 0x7477, 0xB63B, 0xEFBE, 0xFFFF, 0xFFFF, 0xDEFB, 0x94B2, 0x5ACB, 0x528A,   // 0x0890 (2192)
0x5ACC, 0x94B2, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08A0 (2208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0x7476, 0x5355, 0xA5BA, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77E,   // 0x08B0 (2224)
0xCEBC, 0xADBA, 0xA5BA, 0xA5BA, 0xA5BA, 0xA5BA, 0xADBA, 0xADBA, 0xADBA, 0xADBA, 0xADBA, 0xADBA, 0xADBA, 0xADBA, 0xADBA, 0xADBA,   // 0x08C0 (2240)
0xADBA, 0xADBA, 0xADBA, 0xC67C, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0xAD76, 0xA534, 0xB5B6, 0xDEFB, 0xFFFF, 0xFFFF,   // 0x08D0 (2256)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08E0 (2272)
0xDF3D, 0x6436, 0x63D6, 0xC67C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xF7BF, 0xF7BF,   // 0x08F0 (2288)
0xF7FF, 0xFFFF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xFFFF,   // 0x0900 (2304)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0910 (2320)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x63D6, 0x7CB7, 0xDF3D,   // 0x0920 (2336)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xDF3D,   // 0x0930 (2352)
0xDF3D, 0xDF3D, 0xDF3D, 0xDF3D, 0xDF3D, 0xDF3D, 0xDF3D, 0xDF3D, 0xDF3D, 0xDF3D, 0xDF3D, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0940 (2368)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0950 (2384)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xA5B9, 0x5B95, 0x9D79, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0960 (2400)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xBE7C, 0x9539, 0x84B8, 0x84B8, 0x84B8, 0x84B8, 0x84B8,   // 0x0970 (2416)
0x84B8, 0x84B8, 0x84B8, 0x84B8, 0x84B8, 0x84B8, 0x84B8, 0x9539, 0xCEBD, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0980 (2432)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0990 (2448)
0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x6C36, 0x4B54, 0xADFA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09A0 (2464)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x7C78, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x09B0 (2480)
0x4315, 0x4315, 0x4315, 0x4B15, 0x9D7A, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09C0 (2496)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xEF7E, 0xEF7E, 0xF7BF, 0xB5FA,   // 0x09D0 (2512)
0x5354, 0x6C36, 0xCEBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09E0 (2528)
0xDEFD, 0x84B8, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5356,   // 0x09F0 (2544)
0xA5BA, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A00 (2560)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0xADF9, 0xA5B9, 0xBE7B, 0x9538, 0x5B95, 0xA5BA, 0xEFBE, 0xFFFF,   // 0x0A10 (2576)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xCEBC, 0xA5BA, 0x9539,   // 0x0A20 (2592)
0x9539, 0x9539, 0x9539, 0x9539, 0x9539, 0x9D79, 0x9D79, 0x9D79, 0x9D79, 0x9D79, 0x9D7A, 0xADBB, 0xDEFD, 0xF7BF, 0xFFFF, 0xFFFF,   // 0x0A30 (2608)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A40 (2624)
0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x9D78, 0x6C75, 0x7476, 0x63D5, 0x7476, 0xD6FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A50 (2640)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xE77E, 0xE77E, 0xE77E, 0xE77E, 0xEF7E,   // 0x0A60 (2656)
0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A70 (2672)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE,   // 0x0A80 (2688)
0xCEBB, 0x84F6, 0x5393, 0x5B94, 0xA5BA, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A90 (2704)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xEFBE, 0xEFBE, 0xEFBE, 0xEFBE,   // 0x0AA0 (2720)
0xEFBE, 0xEFBE, 0xEFBE, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AB0 (2736)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xA5B9, 0x63D4, 0x84F7,   // 0x0AC0 (2752)
0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AD0 (2768)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77E, 0xBE3B, 0x9539, 0x8CF9, 0x9539, 0x9539, 0x9539, 0x9539, 0x9539, 0xADBA,   // 0x0AE0 (2784)
0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AF0 (2800)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBB, 0x7CB6, 0x6C75, 0xB63B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B00 (2816)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B10 (2832)
0xFFFF, 0xFFFF, 0xADFB, 0x63D6, 0x4B55, 0x4B15, 0x4B15, 0x4B15, 0x4B15, 0x4B15, 0x4B15, 0x63D6, 0xA5BB, 0xEF7E, 0xFFFF, 0xFFFF,   // 0x0B20 (2848)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B30 (2864)
0xFFFF, 0xFFFF, 0xFFFF, 0xCEBB, 0x7CB6, 0x5BD4, 0xA5B9, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B40 (2880)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9D7A, 0x5355,   // 0x0B50 (2896)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5356, 0x9D3A, 0xE73E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B60 (2912)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FC, 0x84F6,   // 0x0B70 (2928)
0x5BD3, 0x9538, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B80 (2944)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x9D79, 0x7477, 0x6C37, 0x6C37, 0x6C37,   // 0x0B90 (2960)
0x6C37, 0x6C37, 0x6C37, 0x8CF9, 0xCEBD, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BA0 (2976)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77D, 0xE73C, 0xF7FF, 0xFFFF, 0xFFFF, 0xD6FB, 0x84F5, 0x6433, 0x8D37, 0xE77D, 0xFFFF, 0xFFFF,   // 0x0BB0 (2992)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BC0 (3008)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xE77E, 0xD6FD, 0xCEBC, 0xD6BD, 0xD6BD, 0xD6BD, 0xD6BC, 0xD6FD, 0xDF3D,   // 0x0BD0 (3024)
0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BE0 (3040)
0xBE79, 0x9DB6, 0xD6FB, 0xEFBE, 0xCEBB, 0x84F5, 0x6432, 0x9577, 0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BF0 (3056)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C00 (3072)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C10 (3088)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE79, 0x7CF3, 0x9575, 0xA5F7,   // 0x0C20 (3104)
0x84F5, 0x6C73, 0x9DB7, 0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C30 (3120)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C40 (3136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xE77B, 0xE77B,   // 0x0C50 (3152)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xC6B9, 0x8533, 0x6C72, 0x6C73, 0x84F5, 0xB639, 0xEF7E, 0xFFFF,   // 0x0C60 (3168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C70 (3184)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C80 (3200)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3A, 0xAE33, 0xAE33, 0xDF7B, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C90 (3216)
0xFFFF, 0xF7BE, 0xD6FB, 0xB637, 0x8D34, 0x74B2, 0x74B3, 0x9DB7, 0xCEBB, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CA0 (3232)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CB0 (3248)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CC0 (3264)
0xEFBB, 0xD738, 0xCEF7, 0xDF39, 0xE77B, 0xC6B6, 0x95AF, 0x8DAF, 0xC6B7, 0xDF3B, 0xD6FA, 0xC6B8, 0xB637, 0xA5F5, 0x9573, 0x7CF2,   // 0x0CD0 (3280)
0x7CF2, 0xA5B6, 0xC6BA, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CE0 (3296)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CF0 (3312)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF79, 0xB6B2, 0x9E2F, 0xA631,   // 0x0D00 (3328)
0xAE73, 0xA5F1, 0x8DAF, 0x8D6F, 0x9DF2, 0xA5F3, 0x95B2, 0x8D71, 0x8531, 0x8532, 0x9574, 0x9DB5, 0xBE78, 0xE77D, 0xFFFF, 0xFFFF,   // 0x0D10 (3344)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D20 (3360)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D30 (3376)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xC6F4, 0xA670, 0x9E30, 0x9DF0, 0x9DF0, 0x9DF1, 0x95B1,   // 0x0D40 (3392)
0x95B1, 0x95B1, 0x95B1, 0x9DB3, 0xAE36, 0xC6B9, 0xDF3C, 0xEFBE, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D50 (3408)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D60 (3424)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D70 (3440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D80 (3456)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D90 (3472)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DA0 (3488)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DB0 (3504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DC0 (3520)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DD0 (3536)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DE0 (3552)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DF0 (3568)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E00 (3584)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E10 (3600)
};


const unsigned short elrs_switch[0xE10] PROGMEM = {
    0x0000, 0x0000, 0x3271, 0x3A93, 0x42F4, 0x3AF5, 0x42F4, 0x42F5, 0x42F4, 0x3AF5, 0x4315, 0x42F5, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5,
    0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x42F5, 0x42F4, 0x42F5, 0x4314, 0x3AF4, 0x42F4, 0x3AD4, 0x3AB2, 0x3251, 0x0000, 0x0000, 0x0004, 0x4A8D, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x42F4, 0x426E, 0x0003, 0x3272, 0x42F4, 0x4315, 0x42F5, 0x7C56, 0x955B, 0xA59A, 0xADDC,
    0xB61B, 0xADDB, 0xADDB, 0xADBB, 0xB5FB, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC,
    0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC,
    0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xB5FC, 0xB61B, 0xADFB, 0xADDB, 0xADBB, 0xADDB, 0xADBB, 0xADDB, 0xADBB, 0xA59B, 0x7C57,
    0x4315, 0x4315, 0x42F4, 0x3273, 0x3AB3, 0x4B14, 0x52F0, 0xEF9F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE79F, 0x52F0, 0x4315, 0x3AB3,
    0x42F4, 0x4315, 0x8474, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DF, 0x9D59, 0x7C56, 0x7436, 0x6C16, 0x6C16, 0x6BF6, 0x6C16, 0x63F6,
    0x63F5, 0x63F6, 0x63F6, 0x5BD6, 0x63D6, 0x5BB6, 0x63B5, 0x5BB6, 0x63D5, 0x5BD6, 0x63D5, 0x5BB6, 0x63D5, 0x5BB6, 0x63D5, 0x5BB6,
    0x63D5, 0x5BD6, 0x63D6, 0x5BB6, 0x63B5, 0x5BB6, 0x63D5, 0x5BD6, 0x63D5, 0x63D6, 0x6BF6, 0x63D6, 0x6BF6, 0x6BF6, 0x6C16, 0x6C16,
    0x7416, 0x7437, 0x7C76, 0x953A, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7413, 0x4315, 0x3AD5, 0x42D5, 0x4B15, 0xAD78, 0xFFFF,
    0xFFFF, 0xFFFF, 0x9D5B, 0x5311, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x4AF2, 0x9D7B, 0xFFFF, 0xFFFF, 0xFFFF, 0x9CF6, 0x42F5, 0x42F4, 0x42F4, 0x4315, 0xB5B9, 0xFFFF, 0xFFFF, 0xA557, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4316, 0xAD76, 0xFFFF,
    0xFFFF, 0x9D17, 0x4315, 0x42F5, 0x42D5, 0x4B15, 0xB5D9, 0xFFFF, 0xEFDF, 0x5310, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x5311, 0xEFDF, 0xFFFF, 0xAD57, 0x42F5, 0x42F4,
    0x42F4, 0x4315, 0xB5F9, 0xFFFF, 0x9D7C, 0x4B13, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B12, 0x955C, 0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5D9, 0xFFFF,
    0x7459, 0x4B13, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4AF3, 0x7C78, 0xFFFF, 0xAD77, 0x42F5, 0x42F4, 0x42F5, 0x4315, 0xB5F9, 0xFFFF, 0x6C38, 0x4314, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B13, 0x6C38,
    0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5D9, 0xFFFF, 0x6C18, 0x4B13, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4B14, 0x7437, 0xFFFF, 0xAD77, 0x42F5, 0x42F4,
    0x4315, 0x4315, 0xB5F9, 0xFFFF, 0x6C17, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B13, 0x6C18, 0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5D9, 0xFFFF,
    0x63F7, 0x4B13, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x5BB3, 0x6414, 0x63F4, 0x6C14, 0x6414, 0x6C34, 0x6C54, 0x7453, 0x6C54, 0x7453,
    0x6C34, 0x6C33, 0x6414, 0x6C33, 0x6416, 0x5373, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4315, 0x5355, 0x63F4, 0x6414, 0x63F4, 0x6C14,
    0x6414, 0x6C34, 0x6C54, 0x7453, 0x6C34, 0x7453, 0x6C54, 0x6C53, 0x6C34, 0x6C33, 0x6414, 0x6C33, 0x5BD7, 0x4B13, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4AF4, 0x6BF7, 0xFFFF, 0xAD77, 0x42F5, 0x42F4, 0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63F7, 0x4314, 0x4315, 0x4315,
    0x4315, 0x4315, 0x9E0D, 0x9E2F, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
    0x9E12, 0x7CEE, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BB7, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
    0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0x95D6, 0x4B6D, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x63F8,
    0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5D9, 0xFFFF, 0x63F7, 0x4B13, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DEE, 0xA62E,
    0xC675, 0xFFFB, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xBEF3, 0xA60F, 0x4B36, 0x4B14,
    0x42F5, 0x4B15, 0x4314, 0x63D6, 0xA5F1, 0xE7B7, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE,
    0xFFDF, 0xFFDE, 0xF7FC, 0xC675, 0x95B6, 0x4B6D, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4AF4, 0x6BF7, 0xFFFF, 0xAD78, 0x42F5, 0x42F4,
    0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63F7, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x9E0E, 0x9E2F, 0xAE10, 0xDFB8, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDFB7, 0xA5F1, 0x63F6, 0x4314, 0x4315, 0x4315, 0x4B14, 0x5BB7,
    0xAE10, 0xDFB8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xBE76,
    0x95D6, 0x4B6D, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x63D8, 0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xBDF9, 0xFFFF,
    0x63D7, 0x4B14, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCE, 0xB690, 0xA60E, 0xB690, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FB, 0xB612, 0x8516, 0x4B4F, 0x42F5, 0x4B15, 0x4314, 0x63D6, 0xA5F1, 0xE7D7, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC675, 0x95B6, 0x4B6D, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4B14, 0x63F7, 0xFFFF, 0xAD98, 0x42F5, 0x42F4, 0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63D7, 0x4314, 0x4315, 0x4315,
    0x4315, 0x4315, 0x9DEE, 0xB6B2, 0xBED1, 0xA60F, 0xE73A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFD, 0xC6B7, 0x9DF6, 0x53AD, 0x4315, 0x4315, 0x4B14, 0x5BB7, 0xAE10, 0xDFB8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xEF9C, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xBE76, 0x9596, 0x434E, 0x4315, 0x4315, 0x4315, 0x42F5, 0x4B14, 0x5BD7,
    0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xBDF9, 0xFFFF, 0x5BD7, 0x4B14, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1,
    0xE7F9, 0xADF1, 0xB613, 0xF7FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF5B, 0x9DF4, 0x6C4E,
    0x42F5, 0x4B15, 0x4314, 0x63D6, 0xA5F1, 0xE7D7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFFB, 0xBE53, 0xA62E, 0xA64D, 0xDF1A, 0xFFFE,
    0xFFFF, 0xFFFF, 0xFFFC, 0xBE54, 0x8D56, 0x4B4E, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4314, 0x63D7, 0xFFFF, 0xAD98, 0x42F5, 0x42F4,
    0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63D7, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x9DEE, 0xB6B2, 0xFFFD, 0xCEB7, 0xA60F, 0xCF35,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA651, 0x8D8E, 0x4315, 0x4315, 0x4B14, 0x5BB7,
    0xAE10, 0xDFB8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC713, 0xA610, 0xBED1, 0xA630, 0xAE11, 0xEFFA, 0xFFFF, 0xFFFF, 0xFFFC, 0xB614,
    0x8536, 0x434F, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BD7, 0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xBDF9, 0xFFFF,
    0x5BD7, 0x4B14, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1, 0xFFFF, 0xFFDE, 0xA64F, 0xA64E, 0xCEF6, 0xD716, 0xCEF6, 0xD716,
    0xCEF6, 0xD716, 0xCEF6, 0xD716, 0xCEF6, 0xD716, 0xAE90, 0xA62E, 0x4B56, 0x4B14, 0x4314, 0x63D6, 0xA5F1, 0xE7D7, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xA64F, 0xAE4E, 0xFFFD, 0xDEF8, 0xA5F0, 0xD775, 0xFFFF, 0xFFFF, 0xFFFC, 0xB612, 0x7CB6, 0x4B50, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4314, 0x63D6, 0xFFFF, 0xAD98, 0x42F5, 0x42F4, 0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63D7, 0x4314, 0x4315, 0x4315,
    0x4315, 0x4315, 0x9DEE, 0xB6B2, 0xFFFF, 0xFFFF, 0xB6D1, 0x9E2F, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
    0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0x5BD6, 0x4314, 0x4B14, 0x5BB7, 0xAE10, 0xDFB8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA62E, 0xAE90,
    0xFFFE, 0xDF3B, 0xA610, 0xCF55, 0xFFFF, 0xFFFF, 0xF7FB, 0xA5D2, 0x7497, 0x4332, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BD7,
    0xFFFF, 0xAD99, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xBDFA, 0xFFFF, 0x5BB7, 0x4B14, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1,
    0xFFFF, 0xFFDE, 0xA64F, 0xA64E, 0xDF39, 0xE779, 0xE75A, 0xE779, 0xE75A, 0xE779, 0xE75A, 0xE779, 0xE75A, 0xE779, 0xBED2, 0xA60F,
    0x5376, 0x4B14, 0x4314, 0x63D6, 0xA5F1, 0xE7D7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE6F, 0xA64E, 0xF7FC, 0xD6D7, 0xA5F0, 0xDF96,
    0xFFFF, 0xFFFF, 0xEFFA, 0xADF1, 0x6C57, 0x4B32, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4314, 0x63D6, 0xFFFF, 0xAD98, 0x42F5, 0x42F4,
    0x4315, 0x4315, 0xBE19, 0xFFFF, 0x5BD7, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x9DEE, 0xB6B2, 0xFFFD, 0xD6D9, 0xA60F, 0xC734,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBEF2, 0xA60F, 0x4B35, 0x4315, 0x4B14, 0x5BB7,
    0xAE10, 0xDFB8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF54, 0xA610, 0xAE6F, 0xA62F, 0xB612, 0xF7FB, 0xFFFF, 0xFFFF, 0xEFF9, 0xA5F2,
    0x6C57, 0x4313, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BD7, 0xFFFF, 0xAD99, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xBDFA, 0xFFFF,
    0x5BB7, 0x4B14, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1, 0xF7FB, 0xB612, 0xADF2, 0xF7FA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE71, 0x9DEE, 0x4315, 0x4B15, 0x4314, 0x63D6, 0xA5F1, 0xE7D7, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xF7FD, 0xCEB6, 0xA62E, 0xAE6F, 0xEF7C, 0xFFFE, 0xFFFF, 0xFFFF, 0xEFFA, 0xADF1, 0x6C37, 0x4B32, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4314, 0x63D6, 0xFFFF, 0xAD98, 0x42F5, 0x42F4, 0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x5BD7, 0x4314, 0x4315, 0x4315,
    0x4315, 0x4315, 0x9DEE, 0xB6B2, 0xCF55, 0xA610, 0xCED7, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xA612, 0x7D0F, 0x4315, 0x4315, 0x4B14, 0x5BB7, 0xAE10, 0xDFB8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFFA, 0xA5F2, 0x6C37, 0x4313, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BD7,
    0xFFFF, 0xAD99, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xBDFA, 0xFFFF, 0x5BB7, 0x4B13, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1,
    0xA64F, 0xA64E, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xD756, 0xB651, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0x9DF4, 0x746E,
    0x42F5, 0x4B15, 0x4314, 0x5BB6, 0xA5F0, 0xDF96, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xEFFA, 0xADF1, 0x6C37, 0x4B32, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4314, 0x63D6, 0xFFFF, 0xB598, 0x42F5, 0x42F4,
    0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x5BD6, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x9DEE, 0xA670, 0xA62F, 0xC713, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xBEF2, 0xA60F, 0xF79D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE75C, 0x9DF5, 0x642E, 0x4315, 0x4315, 0x4B33, 0x53B6,
    0xA62F, 0xB6D2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD718, 0xE7BB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FA, 0xA5D2,
    0x7477, 0x4332, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BB7, 0xFFFF, 0xAD99, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xBDFA, 0xFFFF,
    0x5BB7, 0x4B13, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DEE, 0xA64E, 0xADF2, 0xEFF9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBED2, 0xA60F,
    0xEF9D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE73A, 0x9DD6, 0x63ED, 0x42F5, 0x4B15, 0x4314, 0x63D6, 0x9E2E, 0xA64E, 0xF79E, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FB, 0xADF1, 0x7477, 0x4B31, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4314, 0x63B6, 0xFFFF, 0xB598, 0x4315, 0x4315, 0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63D7, 0x4314, 0x4315, 0x4315,
    0x4315, 0x4315, 0x9E0D, 0x9E2F, 0xCEB7, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F2, 0xA60F, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFE, 0xD6F9, 0x9DF6, 0x53CE, 0x4315, 0x4315, 0x4B14, 0x5BB7, 0xA64D, 0x9E2E, 0xCEB7, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF9D,
    0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xAE13, 0x8D57, 0x434F, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BB7,
    0xFFFF, 0xAD99, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xBDFA, 0xFFFF, 0x5BB7, 0x4B14, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DEE, 0xA62E,
    0xF79D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB, 0xDF18, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xD6D8, 0x9DD6, 0x5BCD,
    0x42F5, 0x4B15, 0x4314, 0x63D6, 0xA62E, 0xAE6E, 0xADF2, 0xF7FA, 0xFFFF, 0xFFFF, 0xFFFF, 0xF79D, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFD, 0xBE54, 0x9DD6, 0x4B6C, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4314, 0x63B6, 0xFFFF, 0xB598, 0x42F5, 0x42F4,
    0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63D7, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x9DEE, 0xA650, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCEB8, 0x9DF6, 0x53AD, 0x4315, 0x4315, 0x4B14, 0x5BB7,
    0xA62F, 0xBF13, 0xA62F, 0xC714, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xB652, 0xD757, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC697,
    0x9DF6, 0x4B8D, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BB7, 0xFFFF, 0xAD99, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xBDFA, 0xFFFF,
    0x5BB7, 0x4B13, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xD6B7, 0x9DD6, 0x5BAD, 0x42F5, 0x4B15, 0x4314, 0x63D6, 0xA5F1, 0xDFB7, 0xA64F, 0xA64E,
    0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xD6D8, 0x9DD6, 0x5BCD, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4314, 0x63D6, 0xFFFF, 0xAD98, 0x42F5, 0x42F4, 0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x5BD7, 0x4314, 0x4315, 0x4315,
    0x4315, 0x4315, 0x9DEE, 0xB6B2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE77C, 0xAE6F, 0xA62F, 0xCEB6, 0xF7FD, 0xFFFF, 0xFFFF,
    0xFFFD, 0xCEB8, 0x9DF6, 0x53AD, 0x4315, 0x4315, 0x4B14, 0x5BB7, 0xAE10, 0xDFB8, 0xCF54, 0xA610, 0xD6D8, 0xFFFE, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF3B, 0x9DF5, 0x5C0E, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BB7,
    0xFFFF, 0xAD99, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xBDF9, 0xFFFF, 0x5BB7, 0x4B14, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FB, 0xB612, 0xA62E, 0xAE6F, 0xA610, 0xCF54, 0xFFFF, 0xFFFF, 0xFFFD, 0xCEB7, 0x9DD6, 0x5BAD,
    0x42F5, 0x4B15, 0x4314, 0x63D6, 0xA5F1, 0xE7D7, 0xF7FB, 0xB612, 0xADF2, 0xF7FA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xF79D, 0x9DF4, 0x6C6E, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4314, 0x63D6, 0xFFFF, 0xAD98, 0x42F5, 0x42F4,
    0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63D7, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x9DEE, 0xB6B2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xD796, 0xA5F1, 0xD6F7, 0xF7FD, 0xA64E, 0xA670, 0xFFFF, 0xFFFF, 0xFFFD, 0xCED8, 0x9DF6, 0x53AE, 0x4315, 0x4315, 0x4B14, 0x5BB7,
    0xAE10, 0xDFB8, 0xFFFD, 0xCED8, 0xA62F, 0xBEF3, 0xE77A, 0xDF7A, 0xE77A, 0xDF7A, 0xE77A, 0xDF7A, 0xE77A, 0xDF7A, 0xE779, 0xDF5A,
    0xA632, 0x7D0E, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BD7, 0xFFFF, 0xAD99, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5D9, 0xFFFF,
    0x63D7, 0x4B14, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF35, 0xAE0F, 0xE73B, 0xFFFE,
    0xAE90, 0xA62E, 0xFFFF, 0xFFFF, 0xFFFE, 0xDEF8, 0x9DD6, 0x5BCD, 0x42F5, 0x4B15, 0x4314, 0x63D6, 0xA5F1, 0xE7D7, 0xFFFF, 0xF7BD,
    0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E10, 0x958D, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4314, 0x63D6, 0xFFFF, 0xAD98, 0x42F5, 0x42F4, 0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63D7, 0x4314, 0x4315, 0x4315,
    0x4315, 0x4315, 0x9DEE, 0xB6B2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD796, 0xA610, 0xD719, 0xF7FE, 0xA64F, 0xA64F, 0xFFFF, 0xFFFF,
    0xFFFE, 0xDF1A, 0x9DF6, 0x53CE, 0x4315, 0x4315, 0x4B14, 0x5BB7, 0xAE10, 0xDFB8, 0xFFFD, 0xCEB8, 0xA62E, 0xAEB1, 0xCF16, 0xCF16,
    0xCF16, 0xCF16, 0xCF16, 0xCF16, 0xCF16, 0xCF16, 0xCF36, 0xC6F5, 0x9E13, 0x6CAE, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BD7,
    0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5D9, 0xFFFF, 0x63D7, 0x4B14, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFFA, 0xADF1, 0xA630, 0xBEB1, 0xA60F, 0xC713, 0xFFFF, 0xFFFF, 0xFFFE, 0xE73A, 0x9DD6, 0x5BED,
    0x42F5, 0x4B15, 0x4314, 0x63D6, 0xA5F1, 0xE7D7, 0xEFFA, 0xAE11, 0xAE13, 0xF7FA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFE, 0xDEF8, 0x9DD6, 0x63ED, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4314, 0x63D6, 0xFFFF, 0xAD98, 0x42F5, 0x42F4,
    0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63F7, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x9DEE, 0xB6B2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFE, 0xDF3B, 0xA64E, 0x9E2E, 0xBE54, 0xEFFB, 0xFFFF, 0xFFFF, 0xFFFE, 0xE75C, 0x9DF5, 0x5BEE, 0x4315, 0x4315, 0x4B14, 0x5BB7,
    0xAE10, 0xDFB8, 0xBEF2, 0xA62F, 0xDF3A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xB634,
    0x9DD6, 0x4B6D, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BD7, 0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5D9, 0xFFFF,
    0x63D7, 0x4B13, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xF79C,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF9C, 0x9DF5, 0x640D, 0x42F5, 0x4B15, 0x4314, 0x63D6, 0xA5F0, 0xD775, 0xA60E, 0xAE8F,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFF9, 0xADF1, 0x7477, 0x4B31, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4B14, 0x63F7, 0xFFFF, 0xAD98, 0x42F5, 0x42F4, 0x4315, 0x4315, 0xBDF9, 0xFFFF, 0x63F7, 0x4314, 0x4315, 0x4315,
    0x4315, 0x4315, 0x9DEE, 0xB6B2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xEF9D, 0x9DF5, 0x640E, 0x4315, 0x4315, 0x4B14, 0x5BB7, 0xA62E, 0xAE70, 0xAE10, 0xDFB8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF55, 0xA610, 0x5396, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x63D7,
    0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5D9, 0xFFFF, 0x63F7, 0x4B13, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DCF, 0xB6B1,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF79D, 0x9DF5, 0x6C4D,
    0x42F5, 0x4B15, 0x4314, 0x63D6, 0x9E2E, 0xA62D, 0xBE55, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xAE71, 0x9DEE, 0x4315, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4AF4, 0x63F7, 0xFFFF, 0xAD78, 0x42F5, 0x42F4,
    0x4315, 0x4315, 0xB5F9, 0xFFFF, 0x6C17, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x9DEE, 0xAE92, 0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF,
    0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFD, 0xEF7C, 0x9DF4, 0x644E, 0x4315, 0x4315, 0x4B14, 0x5BB7,
    0xA64E, 0x9E2E, 0xEF9C, 0xF7FE, 0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFE, 0xEF9D, 0x9E14, 0x6C6E,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x63F8, 0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5D9, 0xFFFF,
    0x6BF8, 0x4B13, 0x42F5, 0x4B15, 0x4315, 0x4B15, 0x9DEE, 0xA62E, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
    0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9DF5, 0x6C4E, 0x42F5, 0x4B15, 0x4314, 0x5BB6, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
    0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9DD6, 0x5BAD, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4AF4, 0x6BF7, 0xFFFF, 0xAD77, 0x42F5, 0x42F4, 0x4315, 0x4315, 0xB5D9, 0xFFFF, 0x6C17, 0x4314, 0x4315, 0x4315,
    0x4315, 0x4315, 0x5BF4, 0x6415, 0x6414, 0x6414, 0x6414, 0x6414, 0x6414, 0x6414, 0x6C34, 0x6414, 0x6C34, 0x6414, 0x6C34, 0x6414,
    0x6C34, 0x6414, 0x6416, 0x4B74, 0x4315, 0x4315, 0x4315, 0x4B36, 0x63F3, 0x6414, 0x6414, 0x6414, 0x6414, 0x6414, 0x6434, 0x6414,
    0x6414, 0x6414, 0x6414, 0x6414, 0x6414, 0x63F4, 0x5BF6, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x63F8,
    0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5D9, 0xFFFF, 0x6C18, 0x4B13, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4B14, 0x6C17, 0xFFFF, 0xAD77, 0x42F5, 0x42F4,
    0x42F5, 0x4315, 0xB5D9, 0xFFFF, 0x6C38, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B13, 0x6C18, 0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xB5B9, 0xFFFF,
    0x7438, 0x4B13, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x4AF3, 0x7438, 0xFFFF, 0xAD77, 0x42F5, 0x42F4, 0x42F5, 0x4315, 0xB5D9, 0xFFFF, 0x7C99, 0x4B13, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B13, 0x7479,
    0xFFFF, 0xA578, 0x4315, 0x42F5, 0x42D5, 0x4B15, 0xB5B9, 0xFFFF, 0x9D7C, 0x5312, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4B13, 0x9D7C, 0xFFFF, 0xAD77, 0x42F5, 0x42F4,
    0x42F4, 0x4315, 0xB5B9, 0xFFFF, 0xF7FF, 0x5311, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5310, 0xEFDF, 0xFFFF, 0xA558, 0x4315, 0x42F5, 0x42F5, 0x4B15, 0xAD78, 0xFFFF,
    0xFFFF, 0xB597, 0x4315, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
    0x42F5, 0x4B15, 0xAD77, 0xFFFF, 0xFFFF, 0xA516, 0x42F5, 0x42F4, 0x42F4, 0x4315, 0xA557, 0xF7FF, 0xFFFF, 0xFFFF, 0x9D9B, 0x4B12,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
    0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5331, 0xA5BC, 0xFFFF, 0xFFFF,
    0xFFFF, 0x94D6, 0x4315, 0x3AF5, 0x3AD4, 0x4B14, 0x7C34, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5FC, 0x8C96, 0x7C57, 0x7C56,
    0x7437, 0x7436, 0x6C16, 0x7436, 0x6C16, 0x7416, 0x6BF6, 0x7416, 0x6C16, 0x6BF5, 0x63F6, 0x6BF5, 0x63D6, 0x6BF6, 0x63D6, 0x6BF5,
    0x6BF6, 0x6C15, 0x6BF6, 0x6C15, 0x63D6, 0x63D5, 0x63D6, 0x6BD5, 0x63D6, 0x6BD5, 0x63D6, 0x6BF6, 0x63D6, 0x6BF5, 0x63D6, 0x63D5,
    0x63D6, 0x6BF6, 0x6BF6, 0x6C16, 0x6C16, 0x7C56, 0x7C76, 0x9D59, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0x7C13, 0x4315, 0x42F4,
    0x3AB3, 0x4315, 0x5310, 0xDF7F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE79F, 0x4AF1, 0x4B14, 0x3AB4, 0x3A72, 0x4AF3, 0x4315, 0x4314,
    0x7416, 0x9519, 0x9D3A, 0xA59A, 0xA57B, 0xA59A, 0xA57B, 0xA59A, 0xA59A, 0xADDB, 0xADBB, 0xB5DB, 0xADBB, 0xB5FB, 0xB5FC, 0xBE1B,
    0xB5FC, 0xB5DA, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB,
    0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB, 0xADBB, 0xB5DB,
    0xADBB, 0xAD9A, 0x953A, 0x7C36, 0x42F5, 0x4B14, 0x42F4, 0x3AB2, 0x0003, 0x4AAE, 0x42F4, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335,
    0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335,
    0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335,
    0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335, 0x4B15, 0x4335,
    0x4B15, 0x4315, 0x4A6D, 0x0004, 0x0000, 0x0000, 0x3292, 0x42B2, 0x3AF5, 0x42F4, 0x3B15, 0x42F4, 0x3B15, 0x42F5, 0x3B15, 0x42F4,
    0x3B15, 0x42F5, 0x3B15, 0x42F5, 0x3B15, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5,
    0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5, 0x4315, 0x42F5,
    0x4315, 0x42F5, 0x3B15, 0x42F4, 0x3AF5, 0x42D4, 0x3AF5, 0x42D4, 0x3AF5, 0x42D4, 0x3AF5, 0x42D4, 0x3AB2, 0x3A70, 0x0000, 0x0000,
};

const unsigned short elrs_antenna[0xE10] PROGMEM = {
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFBD, 0xEF7C, 0xFFFE, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF96, 0xA611, 0xA62E, 0xAE91, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC712, 0xA60F, 0xAE10, 0xD777, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xEF7B, 0xA62E, 0xA62D, 0x9E2E, 0xA62D, 0xE75B, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD,
    0x9E2E, 0xA62D, 0xA62E, 0xA64D, 0xE75B, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFFA, 0xAE13, 0xA64D, 0x9E2E,
    0xA64E, 0x9E2E, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DF, 0xA64E, 0x9E2E, 0xA64D, 0x9E2E,
    0xAE32, 0xEFFA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB6B1, 0xA62E, 0x9E2E, 0xA62D, 0xA610, 0xCF33, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF55, 0xAE0F, 0x9E2E, 0xA62D, 0xA60F, 0xB6B0, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFE, 0xE75B, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xC675, 0xF7FD, 0xFFFF, 0xFFFF, 0xCE9D, 0xBE3C, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xC65C, 0xBE3C, 0xFFFF, 0xFFFF, 0xFFFD, 0xC6B7, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xDF3A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FC, 0xBE54,
    0x9E2E, 0xA62D, 0xA62E, 0xAE6F, 0xFFDF, 0xFFFF, 0xF7FF, 0x94D6, 0x42F5, 0x4B15, 0x7C34, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xD69A, 0xA513, 0x8431, 0x8C30, 0x9CD3, 0xD679, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0x94D5, 0x42F5, 0x4B15, 0x7C35, 0xF7FF,
    0xFFFF, 0xFFFF, 0xAE90, 0xA62E, 0x9E2E, 0xA62D, 0xB613, 0xFFFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D7, 0xA5F1, 0xA64D, 0x9E2E, 0xA610, 0xD777,
    0xFFFF, 0xFFFF, 0xA5BD, 0x4B13, 0x4315, 0x4315, 0x5311, 0xDF7F, 0xFFFF, 0xFFFF, 0xD69A, 0x5AEC, 0x31A6, 0x31A7, 0x31A6, 0x31A7,
    0x31A6, 0x31A7, 0x528A, 0xC618, 0xFFFF, 0xFFFF, 0xEFDF, 0x5311, 0x4315, 0x4315, 0x4B13, 0x84FB, 0xFFFF, 0xFFFF, 0xE7D7, 0xA5F1,
    0xA64D, 0x9E2E, 0xA610, 0xD797, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBEF3, 0xA60F, 0x9E2E, 0xA62D, 0xB634, 0xFFFC, 0xFFFF, 0xEF3D, 0x4315, 0x4B15,
    0x42F5, 0x4B15, 0x6392, 0xF7FF, 0xFFFF, 0xB575, 0x39A7, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0x9492, 0xFFFF, 0xF7FF, 0x73F3, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0xD69B, 0xFFFF, 0xFFFD, 0xC675, 0x9E2E, 0xA62D, 0xA60F, 0xBED1,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xA66E, 0x9E2E, 0xA64D, 0x9E2E, 0xDF19, 0xFFFE, 0xFFFF, 0x8CB6, 0x4315, 0x4315, 0x4315, 0x4316, 0xDEDC, 0xFFFF,
    0xC617, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x9CF4, 0xFFFF, 0xDF1D,
    0x4B56, 0x4315, 0x4315, 0x4315, 0x7413, 0xF7FF, 0xFFFE, 0xE75B, 0xA64D, 0x9E2E, 0xA64E, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF79C, 0x9E2E, 0xA62D,
    0x9E2E, 0xA62D, 0xF79E, 0xFFFF, 0xDF7F, 0x5311, 0x42F5, 0x4B15, 0x4B13, 0xA5BD, 0xFFFF, 0xF79E, 0x4208, 0x39A6, 0x3186, 0x39A6,
    0x3186, 0x528A, 0x8C31, 0x8C50, 0x5ACB, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39C6, 0xDEFC, 0xFFFF, 0xAE1E, 0x5312, 0x42F5, 0x4B15,
    0x4B12, 0xCEDF, 0xFFFF, 0xFFFF, 0xA64E, 0xA62D, 0x9E2E, 0xA62D, 0xE75B, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF1A, 0xA64D, 0x9E2E, 0xA62E, 0xAE90, 0xFFFF, 0xFFFF,
    0xAE1E, 0x4B13, 0x4315, 0x4315, 0x6392, 0xEFDF, 0xFFFF, 0x9CF4, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x94B2, 0xFFDF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xAD76, 0x39C7, 0x31A7, 0x31A6, 0x31A7, 0x8410, 0xFFFF, 0xF7FF, 0x6BD3, 0x4315, 0x4315, 0x4B13, 0x955C, 0xFFFF, 0xFFFF,
    0xBED1, 0xA60F, 0xA64D, 0x9E2E, 0xD6F8, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xD6D8, 0x9E2E, 0xA62D, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF, 0x84DB, 0x4B13, 0x42F5, 0x4B15,
    0x94F6, 0xFFFF, 0xFFFF, 0x5ACB, 0x3186, 0x39A6, 0x3186, 0x738E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9492, 0x39A6,
    0x3186, 0x39A6, 0x41E8, 0xFFFF, 0xFFFF, 0xAD78, 0x42F5, 0x4B15, 0x4314, 0x6C18, 0xFFFF, 0xFFFF, 0xC714, 0xA60F, 0x9E2E, 0xA62D,
    0xC697, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFD, 0xCEB8, 0xA64D, 0x9E2E, 0xA62F, 0xC714, 0xFFFF, 0xFFFF, 0x7459, 0x4314, 0x4315, 0x4315, 0xBE19, 0xFFFF, 0xFFFF, 0x31C7,
    0x31A6, 0x31A7, 0x31A6, 0xD69A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF5D, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0xE71C,
    0xFFFF, 0xCE9C, 0x4315, 0x4315, 0x4B14, 0x5397, 0xFFFF, 0xFFFF, 0xCF55, 0xA610, 0xA64D, 0x9E2E, 0xC675, 0xFFFD, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCEB6, 0x9E2E, 0xA62D,
    0xA610, 0xCF34, 0xFFFF, 0xFFFF, 0x63F8, 0x4B14, 0x42F5, 0x4B15, 0xC63A, 0xFFFF, 0xF79E, 0x39A6, 0x3186, 0x39A6, 0x3186, 0xEF5D,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x39C7, 0x3186, 0x39A6, 0x3186, 0xDEBA, 0xFFFF, 0xDEFC, 0x42F5, 0x4B15,
    0x42F5, 0x4B35, 0xFFFF, 0xFFFF, 0xD776, 0xAE10, 0x9E2E, 0xA62D, 0xBE55, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCEB8, 0xA64D, 0x9E2E, 0xA62F, 0xC714, 0xFFFF, 0xFFFF,
    0x7459, 0x4314, 0x4315, 0x4315, 0xBDF9, 0xFFFF, 0xFFFF, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0xD69A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xEF7D, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0xE71C, 0xFFFF, 0xCE9C, 0x4315, 0x4315, 0x4B15, 0x5397, 0xFFFF, 0xFFFF,
    0xD775, 0xA610, 0xA64D, 0x9E2E, 0xC675, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xD6D8, 0x9E2E, 0xA62D, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF, 0x84DB, 0x4B13, 0x42F5, 0x4B15,
    0x94D6, 0xFFFF, 0xFFFF, 0x5ACA, 0x3186, 0x39A6, 0x3186, 0x840F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9CB3, 0x39A6,
    0x3186, 0x39A6, 0x41E8, 0xFFFF, 0xFFFF, 0xB598, 0x42F5, 0x4B15, 0x4314, 0x6C18, 0xFFFF, 0xFFFF, 0xC714, 0xA60F, 0x9E2E, 0xA62D,
    0xC696, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFE, 0xDF3B, 0xA64D, 0x9E2E, 0xA62E, 0xAE90, 0xFFFF, 0xFFFF, 0xAE1E, 0x4B13, 0x4315, 0x4315, 0x6372, 0xEFDF, 0xFFFF, 0x9CD3,
    0x31A6, 0x31A7, 0x31A6, 0x31A7, 0xA514, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB597, 0x39C7, 0x31A7, 0x31A6, 0x31A7, 0x840F, 0xFFFF,
    0xF7FF, 0x73F4, 0x4315, 0x4315, 0x4B13, 0x8D3C, 0xFFFF, 0xFFFF, 0xBED2, 0xA60F, 0xA64D, 0x9E2E, 0xD6F8, 0xFFFE, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF79D, 0x9E2E, 0xA62D,
    0x9E2E, 0xA62D, 0xF79E, 0xFFFF, 0xDF7F, 0x5311, 0x42F5, 0x4B15, 0x4B13, 0xA59C, 0xFFFF, 0xF79D, 0x41E8, 0x39A6, 0x3186, 0x39A6,
    0x3186, 0x5AAA, 0x8C51, 0x9491, 0x5AEC, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39C6, 0xDEFC, 0xFFFF, 0xBE5F, 0x5312, 0x42F5, 0x4B15,
    0x4AF2, 0xC6BF, 0xFFFF, 0xFFFF, 0xA64E, 0xA62D, 0x9E2E, 0xA62D, 0xE73B, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA66E, 0x9E2E, 0xA64D, 0x9E2E, 0xDF19, 0xFFFE,
    0xFFFF, 0x8496, 0x4315, 0x4315, 0x4315, 0x4316, 0xD6DC, 0xFFFF, 0xBDD7, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7,
    0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x9CF4, 0xFFFF, 0xE75E, 0x5376, 0x4315, 0x4315, 0x4315, 0x6BD3, 0xEFFF, 0xFFFE, 0xE75C,
    0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xFFDE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0x9E2E, 0xA62D, 0xB634, 0xFFFC, 0xFFFF, 0xE73D, 0x4315, 0x4B15,
    0x42F5, 0x4B15, 0x6392, 0xF7FF, 0xFFFF, 0xAD34, 0x39A7, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0x8C51, 0xFFFF, 0xF7FF, 0x7C34, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0xC65B, 0xFFFF, 0xFFFD, 0xC696, 0x9E2E, 0xA62D, 0xA60F, 0xB6B0,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xA610, 0xD777, 0xFFFF, 0xFFFF, 0x9D9D, 0x4B13, 0x4315, 0x4315, 0x5311, 0xDF5F,
    0xFFFF, 0xFFFF, 0xCE79, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0xB597, 0xFFFF, 0xFFFF, 0xF7FF, 0x5311,
    0x4315, 0x4315, 0x4B13, 0x7C9A, 0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xA610, 0xCF76, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xBE54,
    0x9E2E, 0xA62D, 0xA62E, 0xAE6F, 0xFFDF, 0xFFFF, 0xF7FF, 0x94B5, 0x42F5, 0x4B15, 0x7C14, 0xF7FF, 0xFFFF, 0xFFFF, 0xD67A, 0x39A6,
    0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0xB596, 0xFFFF, 0xFFFF, 0xF7FF, 0x8C95, 0x42F5, 0x4B15, 0x73D3, 0xF7DF,
    0xFFFF, 0xFFFF, 0xB6B1, 0xA62E, 0x9E2E, 0xA62D, 0xAE13, 0xF7FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE75C, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
    0xC695, 0xF7FD, 0xFFFF, 0xFFFF, 0xC65C, 0xB61C, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0x8430, 0x31A7, 0x31A6, 0x31A7, 0x5ACA, 0x738E,
    0x31A6, 0x31A7, 0x31A6, 0x632D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xB5FB, 0xADDB, 0xFFDF, 0xFFFF, 0xFFFD, 0xCED8, 0xA64D, 0x9E2E,
    0xA64D, 0x9E2E, 0xD719, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBED2, 0xA60E, 0x9E2E, 0xA62D, 0xA610, 0xCF33, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFBE, 0x41E8, 0x39A6, 0x3186, 0x39A6, 0xA514, 0xC617, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0xE71C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD776, 0xAE10, 0x9E2E, 0xA62D, 0xA60E, 0xB68F, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FA, 0xAE13, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xB597, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0xEF7D, 0xFFDF, 0x4A28, 0x31A7, 0x31A6, 0x31A7, 0x9CD3, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xA66E, 0x9E2E, 0xA64D, 0x9E2E, 0xAE11, 0xE7D9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xEF7C, 0xA64E, 0xA62D, 0x9E2E, 0xA62D, 0xE75C, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x6B2C, 0x3186, 0x39A6,
    0x3186, 0x738E, 0xFFFF, 0xFFFF, 0x8C51, 0x39A6, 0x3186, 0x39A6, 0x4A49, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD,
    0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xDF1A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDFB7, 0xA611,
    0xA62E, 0xAE91, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF3C, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0xBDF8, 0xFFFF, 0xFFFF,
    0xDEFB, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0xCE59, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBED1, 0xA60F, 0xA60F, 0xC734,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDE, 0xEF7C, 0xFFFE, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9CB3, 0x39A6, 0x3186, 0x39A6, 0x4208, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0x62EB, 0x3186, 0x39A6,
    0x3186, 0x83EF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xEF7B, 0xEF9D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0x5289, 0x31A7, 0x31A6, 0x31A7, 0x9471, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA555, 0x31A6, 0x31A7, 0x31A6, 0x39C7, 0xF7BE, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCE59, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0xC618, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE6FB, 0x3186, 0x39A6, 0x3186, 0x39A6, 0xAD76, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7BF0, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x4A49, 0xE73D, 0xFFFF, 0xFFFF,
    0xF7BE, 0x5ACC, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x630C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xF79E, 0x41E7, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x5AAA, 0xF79E, 0xFFFF, 0x6B4D, 0x39A6, 0x3186, 0x39A6,
    0x3186, 0x39A6, 0x3186, 0xE6FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB595, 0x31A7,
    0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x6B2C, 0x7BCF, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x9492,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x630C, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x4A48, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE71C, 0x39A6, 0x31A7, 0x31A6, 0x31A7, 0xB596, 0x4A6A, 0x31A6, 0x31A7, 0x31A6, 0x31A7,
    0x31A6, 0x31A7, 0x4208, 0xBDD7, 0x39A6, 0x31A7, 0x31A6, 0x31A7, 0xC638, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0x9CB2, 0x3186, 0x39A6, 0x3186, 0x4A49, 0xFFFF, 0xE6FB, 0x4208, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39C6, 0xCE59, 0xFFFF,
    0x630C, 0x39A6, 0x3186, 0x39A6, 0x7BAF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x4A49, 0x31A6, 0x31A7,
    0x31A6, 0x94B3, 0xFFFF, 0xCE59, 0x39E7, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0xB596, 0xFFFF, 0xB595, 0x31A7, 0x31A6, 0x31A7,
    0x39C7, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC618, 0x39A6, 0x3186, 0x39A6, 0x3186, 0xE71B, 0xD6BB, 0x4207,
    0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x39A7, 0xC617, 0xF79E, 0x41E7, 0x3186, 0x39A6, 0x3186, 0xAD55, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7BCF, 0x31A7, 0x31A6, 0x31A7, 0x630C, 0xE73D, 0x4A49, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7,
    0x31A6, 0x31A7, 0x31A6, 0x39E8, 0xD6BA, 0x7BD0, 0x31A6, 0x31A7, 0x31A6, 0x5AEC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF79D,
    0x39C7, 0x39A6, 0x3186, 0x39A6, 0x9CD3, 0x528A, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x7BCF, 0x9CB2, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0x4229, 0xB575, 0x3186, 0x39A6, 0x3186, 0x39A6, 0xDEDB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA555, 0x31A6, 0x31A7, 0x31A6, 0x31A7,
    0x528A, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x6B4E, 0xFFFF, 0xFFFF, 0x8410, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x4A6A, 0x39E7, 0x31A7,
    0x31A6, 0x31A7, 0x9471, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x62EB, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0x5ACB, 0xFFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0x7BAE, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x4208, 0xFFDF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xDEFB, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x4A6A, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xF7DF, 0x632C, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0xBDF8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x8C51, 0x39A6,
    0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x4208, 0xE71C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF77E, 0x5AAA,
    0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x738E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0x4A28, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7,
    0x31A6, 0x39E8, 0xD6BA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0x4A49, 0x31A7, 0x31A6, 0x31A7,
    0x31A6, 0x31A7, 0x31A6, 0x31A7, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC5F7, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x39A7, 0xCE38, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEDB, 0x4208, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0xA514, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0x738E, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0xB5B6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCE5A, 0x39E7, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x5ACA, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x39C6, 0x3186, 0x39A6,
    0x3186, 0x39A6, 0x3186, 0xA513, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xBDD7, 0x39C6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0xDEBA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA534, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x4208,
    0x4228, 0x4208, 0x4228, 0x4208, 0x4228, 0x4208, 0x4228, 0x4208, 0x4228, 0x4208, 0x4228, 0x4208, 0x4228, 0x4208, 0x4228, 0x31A7,
    0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x8451, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x52AB, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0x3186, 0x4208, 0xFFBF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6BB,
    0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7,
    0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0x31A6, 0x31A7, 0xBDD7, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x8C50, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6,
    0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x3186, 0x39A6, 0x6B6E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0x4208, 0x31A6, 0x31A7, 0x31A6, 0x8451, 0xCE79, 0xCE59, 0xCE79, 0xCE59,
    0xCE79, 0xCE59, 0xCE79, 0xCE59, 0xCE79, 0xCE59, 0xCE79, 0xCE59, 0xCE79, 0xCE59, 0xCE79, 0xCE59, 0xCE79, 0xCE59, 0xCE79, 0xCE59,
    0xCE79, 0xCE59, 0x9CF3, 0x31A7, 0x31A6, 0x31A7, 0x39C7, 0xE75D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xBDB7, 0x39A6, 0x3186, 0x39A6, 0x39A7, 0xEF5C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0x4208,
    0x3186, 0x39A6, 0x3186, 0xA4F3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x736D, 0x31A7,
    0x31A6, 0x31A7, 0x6B6D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x8431, 0x31A6, 0x31A7, 0x31A6, 0x528A,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x4A49, 0x39A6, 0x3186, 0x39A6, 0xBDB7, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEBA, 0x3186, 0x39A6, 0x3186, 0x39A6, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x8C10, 0x31A6, 0x39A6, 0x528A, 0xFFDE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x6B2C, 0x31A6, 0x39A6, 0x632C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xB596, 0x9D14, 0xF77D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xF79E, 0xA514, 0xA535, 0xF79E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
};

const unsigned short elrs_power[0xE10] PROGMEM ={
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0010 (16)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0020 (32)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0030 (48)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0040 (64)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0050 (80)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0060 (96)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0070 (112)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0080 (128)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0090 (144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00A0 (160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00B0 (176)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x00C0 (192)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00D0 (208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xC67C, 0x7478,   // 0x00E0 (224)
0x5355, 0x63D7, 0x84B8, 0x9D7A, 0xC67C, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00F0 (240)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0100 (256)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0110 (272)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xB63C, 0x5B96, 0x4315, 0x4315, 0x5396, 0x6C37,   // 0x0120 (288)
0x8D39, 0xB63B, 0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0130 (304)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0140 (320)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0150 (336)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xC67C, 0x7C78, 0x5355, 0x4B55, 0x4B15, 0x4315, 0x4315, 0x5396, 0x8CF9, 0xCEBC,   // 0x0160 (352)
0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0170 (368)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0180 (384)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0190 (400)
0xFFFF, 0xFFFF, 0xE77E, 0xC67C, 0xA5BA, 0x9539, 0x7478, 0x5356, 0x4315, 0x4315, 0x4B55, 0x7477, 0xB63B, 0xF7BF, 0xFFFF, 0xFFFF,   // 0x01A0 (416)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01B0 (432)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01C0 (448)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01D0 (464)
0xF7BE, 0xE77E, 0xD6FD, 0xB63B, 0x8CF9, 0x5356, 0x4315, 0x4315, 0x6C37, 0xC67C, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01E0 (480)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01F0 (496)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0200 (512)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xE77E, 0xF7BF, 0xFFFF, 0xFFFF,   // 0x0210 (528)
0xDF3D, 0xA57A, 0x63D6, 0x4315, 0x4315, 0x7C78, 0xD6BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0220 (544)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0230 (560)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0240 (576)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xB5FB, 0x9539, 0xB5FB, 0xDF3D, 0xF7FF, 0xFFFF, 0xF7BF, 0xA5BA, 0x5B96,   // 0x0250 (592)
0x4315, 0x4315, 0x9D7A, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0260 (608)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0270 (624)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0280 (640)
0xFFFF, 0xFFFF, 0xE73E, 0x84F9, 0x5355, 0x5B96, 0x7437, 0xADFB, 0xEF7E, 0xFFFF, 0xEF7E, 0x9D7A, 0x4B15, 0x4315, 0x6C37, 0xBE3C,   // 0x0290 (656)
0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02A0 (672)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02B0 (688)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9,   // 0x02C0 (704)
0x4314, 0x4315, 0x4315, 0x63D6, 0xA5BA, 0xEFBE, 0xFFFF, 0xD6FD, 0x7437, 0x4B15, 0x4B55, 0x8CF9, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02D0 (720)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02E0 (736)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02F0 (752)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xBE7D, 0x84B9, 0x63D6, 0x4B15, 0x4315,   // 0x0300 (768)
0x5B96, 0xC67C, 0xF7BF, 0xEFBE, 0xADFB, 0x5356, 0x4315, 0x7477, 0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0310 (784)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0320 (800)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0330 (816)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xEF7E, 0xC67C, 0x6C37, 0x4314, 0x4315, 0x7CB8, 0xDEFD, 0xFFFF,   // 0x0340 (832)
0xD6FD, 0x5BD6, 0x4315, 0x63D7, 0xBE3C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0350 (848)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0360 (864)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0370 (880)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xA5BB, 0x5B96, 0x4315, 0x4B55, 0xC67C, 0xFFFF, 0xE73E, 0x63D6, 0x4315, 0x5356,   // 0x0380 (896)
0x9D7A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0390 (912)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0xBE3B, 0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03A0 (928)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73C, 0xBDF7, 0xA534, 0xAD75, 0xCE38, 0xEF7D, 0xFFFF,   // 0x03B0 (944)
0xFFFF, 0xFFFF, 0xDEFE, 0x9539, 0x63D6, 0x63D6, 0xC6BC, 0xFFFF, 0xE77E, 0x7C78, 0x5355, 0x5B96, 0x9D3A, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03C0 (960)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03D0 (976)
0xFFFF, 0xFFFF, 0xF7BF, 0xCEBC, 0x7477, 0x5355, 0x9539, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03E0 (992)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0x94B2, 0x528A, 0x39C7, 0x39C7, 0x630C, 0xAD75, 0xEF7C, 0xFFFF, 0xFFFF, 0xF7BF, 0xD6FD,   // 0x03F0 (1008)
0xADFB, 0xADFB, 0xE73E, 0xFFFF, 0xF7BE, 0xB63B, 0x8CF9, 0x9539, 0xBE3C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0400 (1024)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xBE3C, 0x7C78,   // 0x0410 (1040)
0x4B15, 0x4315, 0x5355, 0x9539, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D,   // 0x0420 (1056)
0x94B2, 0x4208, 0x3186, 0x3186, 0x3186, 0x39C7, 0x4A49, 0xB5B5, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xF7BE, 0xFFFF, 0xFFFF,   // 0x0430 (1072)
0xFFFF, 0xF7BE, 0xE77E, 0xE77E, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0440 (1088)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9, 0x4315, 0x4315, 0x4315, 0x4315, 0x5356,   // 0x0450 (1104)
0x9D79, 0xE73E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6BA, 0x6B4D, 0x3186, 0x3186, 0x3186,   // 0x0460 (1120)
0x3186, 0x3186, 0x3186, 0x8430, 0xE73B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0470 (1136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0480 (1152)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0x9D7B, 0x5B97, 0x4315, 0x4315, 0x4315, 0x4315, 0x5355, 0x9D7A, 0xE73E, 0xFFFF,   // 0x0490 (1168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCE79, 0x6B4D, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x7BCE,   // 0x04A0 (1184)
0xD6B9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04B0 (1200)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE,   // 0x04C0 (1216)
0xFFFF, 0xFFFF, 0xF7BF, 0xB5FC, 0x6397, 0x4315, 0x4315, 0x4315, 0x4315, 0x5355, 0x9539, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04D0 (1232)
0xFFFF, 0xFFFF, 0xF7BE, 0xC638, 0x630C, 0x3186, 0x3186, 0x3186, 0x3186, 0x3187, 0x39C7, 0x8C71, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04E0 (1248)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04F0 (1264)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xD736, 0xDF78, 0xF7FE, 0xFFFF, 0xF7BF,   // 0x0500 (1280)
0xB5FC, 0x63D7, 0x4315, 0x4315, 0x4315, 0x4315, 0x5356, 0x9539, 0xE73E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xCE79, 0x7BCF,   // 0x0510 (1296)
0x4208, 0x3186, 0x3186, 0x3186, 0x3187, 0x3187, 0x5ACA, 0xBDF6, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0520 (1312)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0530 (1328)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xC6F5, 0xAE6F, 0xBEF1, 0xE7B9, 0xFFFF, 0xFFFF, 0xF7FF, 0xB5FC, 0x63D7, 0x4315,   // 0x0540 (1344)
0x4315, 0x4315, 0x4315, 0x5355, 0x9539, 0xDF3E, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0x8430, 0x4208, 0x3187, 0x39C7, 0x528A, 0x5ACA,   // 0x0550 (1360)
0x5ACA, 0x738D, 0xAD75, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0560 (1376)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0570 (1392)
0xFFFF, 0xE77B, 0xB6B1, 0x9E2E, 0x9E2E, 0xC6F3, 0xE7BA, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5FC, 0x63D7, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0580 (1408)
0x4B55, 0x953A, 0xE77E, 0xFFFF, 0xFFFF, 0xD6BA, 0x842F, 0x4208, 0x4208, 0x630C, 0xA534, 0xAD75, 0xAD75, 0xCE79, 0xF7BE, 0xFFFF,   // 0x0590 (1424)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05A0 (1440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xC6F4, 0xA66E, 0x9E2D,   // 0x05B0 (1456)
0x9E2D, 0xA62E, 0xC6F3, 0xE7BA, 0xFFFF, 0xFFFF, 0xF7BF, 0xBE3C, 0x63D7, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B55, 0x9539, 0xE77E,   // 0x05C0 (1472)
0xFFFF, 0xF7BE, 0xD6B9, 0x842F, 0x83CF, 0xBDF6, 0xEF7D, 0xF7BE, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05D0 (1488)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05E0 (1504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xA62E, 0xC6F2,   // 0x05F0 (1520)
0xE7BA, 0xFFFF, 0xFFFF, 0xF7BF, 0xBE3C, 0x6C37, 0x4315, 0x4315, 0x4315, 0x4314, 0x5355, 0x953A, 0xDF3E, 0xFFFF, 0xFFFF, 0xDEFB,   // 0x0600 (1536)
0xD6BA, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0610 (1552)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0620 (1568)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xD738, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xA62E, 0xBEF2, 0xE7BA, 0xF7FE, 0xFFFF,   // 0x0630 (1584)
0xFFFF, 0xB5FC, 0x6C37, 0x4315, 0x4315, 0x4315, 0x4314, 0x4B55, 0x953A, 0xDF3E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0640 (1600)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0650 (1616)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC,   // 0x0660 (1632)
0xC6F5, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xA62E, 0xBEF2, 0xE7B9, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE3C, 0x63D7,   // 0x0670 (1648)
0x4B15, 0x4315, 0x4315, 0x4315, 0x4B55, 0x8D39, 0xE73E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0680 (1664)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0690 (1680)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xBEB3, 0xA62E, 0x9E2D, 0x9E2D,   // 0x06A0 (1696)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xA62E, 0xBEF2, 0xE779, 0xFFFF, 0xFFFF, 0xF7BF, 0xBE3C, 0x6C37, 0x4315, 0x4315, 0x4315,   // 0x06B0 (1712)
0x4314, 0x5355, 0x8CF9, 0xDF3E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06C0 (1728)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06D0 (1744)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xB6B2, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x06E0 (1760)
0x9E2D, 0x9E2E, 0xA62E, 0xBEF2, 0xE7B9, 0xF7FE, 0xFFFF, 0xFFFF, 0xBE3C, 0x6C37, 0x4315, 0x4315, 0x4315, 0x4314, 0x4B55, 0x8D39,   // 0x06F0 (1776)
0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0700 (1792)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0710 (1808)
0xFFFF, 0xFFFF, 0xFFFF, 0xDF7A, 0xB6B1, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xA62E,   // 0x0720 (1824)
0xBEF2, 0xE7B9, 0xF7FE, 0xFFFF, 0xFFFF, 0xBE3C, 0x6C37, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B15, 0x8CF9, 0xDF3D, 0xFFFF, 0xFFFF,   // 0x0730 (1840)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0740 (1856)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF7A,   // 0x0750 (1872)
0xB6B1, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xBEF2, 0xE779, 0xFFFF,   // 0x0760 (1888)
0xFFFF, 0xFFFF, 0xC67C, 0x6C38, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B55, 0x8CF8, 0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0770 (1904)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0780 (1920)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xB6B2, 0x9E2E, 0x9E2D, 0x9E2D,   // 0x0790 (1936)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xA62E, 0xBEF2, 0xE779, 0xFFFE, 0xFFFF, 0xF7BF, 0xC67D,   // 0x07A0 (1952)
0x6C38, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B55, 0x84F8, 0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07B0 (1968)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07C0 (1984)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xBEB3, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x07D0 (2000)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0x9E2E, 0xBEF2, 0xE779, 0xF7FE, 0xFFFF, 0xFFFF, 0xBE3C, 0x7438, 0x4315, 0x4315,   // 0x07E0 (2016)
0x4315, 0x4315, 0x4B15, 0x8CF9, 0xD6FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07F0 (2032)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0800 (2048)
0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xC6F5, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0810 (2064)
0x9E2D, 0x9E2D, 0x9E2E, 0xA62E, 0xBEB2, 0xE779, 0xF7FE, 0xFFFF, 0xFFFF, 0xC67D, 0x6C38, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0820 (2080)
0x84F8, 0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0830 (2096)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE,   // 0x0840 (2112)
0xD738, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E,   // 0x0850 (2128)
0xA62E, 0xBEF2, 0xDF79, 0xFFFF, 0xFFFF, 0xF7BF, 0xCE7D, 0x6C38, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B55, 0x84B8, 0xDEFD, 0xFFFF,   // 0x0860 (2144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0870 (2160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xAE70, 0x9E2D, 0x9E2D,   // 0x0880 (2176)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0x9E2E, 0xBEF1, 0xDF79,   // 0x0890 (2192)
0xF7FE, 0xFFFF, 0xFFFF, 0xC67D, 0x7478, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B15, 0x7CB8, 0xC67C, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08A0 (2208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08B0 (2224)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xBEF4, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x08C0 (2240)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0x9E2E, 0xBEB2, 0xE779, 0xF7BE, 0xFFFF, 0xFFFF,   // 0x08D0 (2256)
0xBE3C, 0x7478, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x9539, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08E0 (2272)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08F0 (2288)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF7A, 0xB6B1, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0900 (2304)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xBEB2, 0xDF78, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBD, 0x6C38, 0x4315,   // 0x0910 (2320)
0x4315, 0x4315, 0x5B96, 0xADFB, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0920 (2336)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0930 (2352)
0xFFFF, 0xF7FE, 0xC6F5, 0xA66E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0940 (2368)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0x9E2E, 0xBEF1, 0xDF78, 0xF7FE, 0xFFFF, 0xFFFF, 0xCE7D, 0x6BD8, 0x4315, 0x63D6, 0xBE3B, 0xE77E,   // 0x0950 (2384)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0960 (2400)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xBEB3,   // 0x0970 (2416)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0980 (2432)
0x9E2E, 0x9E2E, 0xBEB1, 0xDF79, 0xF7BE, 0xFFFF, 0xFFFF, 0xBE3B, 0x8CF9, 0xB5FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0990 (2448)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09A0 (2464)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xB6B1, 0x9E2E, 0x9E2D, 0x9E2D,   // 0x09B0 (2480)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0x9E2E, 0xB6B2,   // 0x09C0 (2496)
0xDF78, 0xF7FD, 0xFFFF, 0xFFFF, 0xF7BF, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09D0 (2512)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09E0 (2528)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xE77B, 0xEFBD, 0xFFFF, 0xFFFF, 0xDF7A, 0xB6B2, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x09F0 (2544)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xAE70, 0xDF76, 0xF7FE, 0xFFFF,   // 0x0A00 (2560)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A10 (2576)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF79,   // 0x0A20 (2592)
0xB6B2, 0xC6F4, 0xEFBC, 0xFFFF, 0xFFFF, 0xE77A, 0xC6F4, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0A30 (2608)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0x9E2E, 0xCF34, 0xF7FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A40 (2624)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A50 (2640)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xC6F4, 0x9E2D, 0x9E2D, 0xCEF5, 0xEFBC,   // 0x0A60 (2656)
0xFFFF, 0xFFFF, 0xEFBC, 0xCEF6, 0xB6B1, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0A70 (2672)
0x9E2D, 0x9E2D, 0xA66F, 0xB6B1, 0xC6F4, 0xDF79, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A80 (2688)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A90 (2704)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xD738, 0xAE71, 0x9E2D, 0x9E2D, 0xA66F, 0xC6F5, 0xEFBC, 0xFFFF, 0xFFFF, 0xF7FE,   // 0x0AA0 (2720)
0xE77A, 0xC6F5, 0xB6B1, 0xA66F, 0xA66E, 0xA62E, 0xA62E, 0xA62E, 0xA62E, 0xA62E, 0xA62E, 0xA66E, 0xA66F, 0xAE71, 0xC6F5, 0xDF7A,   // 0x0AB0 (2736)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AC0 (2752)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AD0 (2768)
0xFFFF, 0xEFBD, 0xBEB2, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F, 0xBEB3, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xE77B, 0xDF79,   // 0x0AE0 (2784)
0xCF36, 0xC6F4, 0xBEB3, 0xBEB3, 0xBEB3, 0xBEB3, 0xC6F4, 0xCF36, 0xDF38, 0xE77B, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AF0 (2800)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B00 (2816)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xD737, 0xA66F, 0x9E2D,   // 0x0B10 (2832)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xB6B2, 0xCF36, 0xE77A, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xEFBC, 0xE77B, 0xE77B,   // 0x0B20 (2848)
0xE77B, 0xE77B, 0xEFBC, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B30 (2864)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B40 (2880)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xB6B1, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0B50 (2896)
0x9E2D, 0x9E2D, 0xA66F, 0xBEB2, 0xD737, 0xEFBB, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B60 (2912)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B70 (2928)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B80 (2944)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCEF6, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E,   // 0x0B90 (2960)
0xA66F, 0xBEF4, 0xE77B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BA0 (2976)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BB0 (2992)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF38,   // 0x0BC0 (3008)
0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xDF38, 0xFFFF,   // 0x0BD0 (3024)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BE0 (3040)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BF0 (3056)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xBEF3, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0C00 (3072)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xD738, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C10 (3088)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C20 (3104)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C30 (3120)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCF37, 0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0C40 (3136)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xD737, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C50 (3152)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C60 (3168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB, 0xE77B,   // 0x0C70 (3184)
0xE7BB, 0xD738, 0xAE70, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0C80 (3200)
0x9E2D, 0xA66E, 0xC6F5, 0xE77B, 0xE77B, 0xE77B, 0xEFBC, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C90 (3216)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CA0 (3232)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBEB3, 0xB6B2, 0xBEB2, 0xB6B1, 0x9E2D, 0x9E2D,   // 0x0CB0 (3248)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xAE70, 0xB6B2,   // 0x0CC0 (3264)
0xBEB2, 0xB6B2, 0xCEF5, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CD0 (3280)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CE0 (3296)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0CF0 (3312)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE71, 0xE77B,   // 0x0D00 (3328)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D10 (3344)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D20 (3360)
0xFFFF, 0xFFFF, 0xB6B1, 0xA66E, 0xA66F, 0xA66F, 0xAE70, 0xAE70, 0xAE70, 0xAE70, 0xAE70, 0xAE70, 0xAE70, 0xAE70, 0xAE70, 0xAE70,   // 0x0D30 (3376)
0xAE70, 0xAE70, 0xAE70, 0xAE70, 0xAE70, 0xAE70, 0xAE6F, 0xA66F, 0xA66F, 0xA66E, 0xBEB3, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D40 (3392)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D50 (3408)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D60 (3424)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D70 (3440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D80 (3456)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D90 (3472)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DA0 (3488)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DB0 (3504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DC0 (3520)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DD0 (3536)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DE0 (3552)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DF0 (3568)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E00 (3584)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E10 (3600)
};

const unsigned short elrs_ratio[0xE10] PROGMEM ={
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0010 (16)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0020 (32)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0030 (48)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0040 (64)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0050 (80)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0060 (96)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0070 (112)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0080 (128)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0090 (144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00A0 (160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00B0 (176)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x00C0 (192)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00D0 (208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00E0 (224)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00F0 (240)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0100 (256)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0110 (272)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0120 (288)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0130 (304)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0140 (320)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0150 (336)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0160 (352)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0170 (368)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0180 (384)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0190 (400)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01A0 (416)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01B0 (432)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xF7BE, 0xE73C, 0xCEB9, 0xC638, 0xC638, 0xC679, 0xDEFB, 0xEF7D, 0xF7BE, 0xFFFF,   // 0x01C0 (448)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01D0 (464)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01E0 (480)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01F0 (496)
0xF7BE, 0xDEFB, 0xCE79, 0xB5B6, 0x9D33, 0x94B2, 0x94B2, 0x9CF3, 0xAD75, 0xBDF7, 0xD6BA, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0200 (512)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0210 (528)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0220 (544)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xC638, 0x8C71, 0x4A49, 0x3186,   // 0x0230 (560)
0x39C7, 0x39C7, 0x39C7, 0x39C7, 0x3987, 0x4208, 0x6B4D, 0xA575, 0xDEFB, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0240 (576)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0250 (592)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0260 (608)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6BA, 0x8CB1, 0x528A, 0x3987, 0x3146, 0x3186, 0x4A8A, 0x630C, 0x6B4D, 0x5ACB,   // 0x0270 (624)
0x4208, 0x3186, 0x3145, 0x4A08, 0x6B4D, 0xB5B6, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0280 (640)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0290 (656)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02A0 (672)
0xFFFF, 0xCE79, 0x7C2F, 0x3186, 0x2945, 0x528A, 0x8430, 0xA534, 0xB5F6, 0xC638, 0xCE79, 0xBE38, 0xAD75, 0x94B2, 0x6B4D, 0x39C7,   // 0x02B0 (688)
0x3145, 0x5ACB, 0xA575, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02C0 (704)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02D0 (720)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xD6BA, 0x738E, 0x2945, 0x3987,   // 0x02E0 (736)
0x6B8D, 0xB5B6, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xCE79, 0x94B2, 0x528A, 0x2945, 0x4A08, 0xAD75,   // 0x02F0 (752)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0300 (768)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0310 (784)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0x8C71, 0x3146, 0x2946, 0x8C71, 0xDEFB, 0xF7BE, 0xFFFF, 0xFFFF,   // 0x0320 (800)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xB5B6, 0x5ACB, 0x2945, 0x4A49, 0xBE38, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0330 (816)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0340 (832)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0350 (848)
0xFFFF, 0xFFFF, 0xFFFF, 0xADB5, 0x4208, 0x2905, 0x8470, 0xDF3C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0360 (864)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xAD75, 0x524A, 0x2945, 0x7BCF, 0xD6FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0370 (880)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0380 (896)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0x738E,   // 0x0390 (912)
0x2905, 0x52CA, 0xD6B9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03A0 (928)
0xFFFF, 0xE73C, 0x9CF3, 0x3986, 0x4208, 0x94B2, 0xDEFB, 0xE73C, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03B0 (944)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03C0 (960)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xA534, 0x4208, 0x41C8, 0xA574, 0xEF7D, 0xFFFF,   // 0x03D0 (976)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6BA, 0x5ACB,   // 0x03E0 (992)
0x3186, 0x4A49, 0x8430, 0x94F3, 0xAD75, 0xC638, 0xE73C, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03F0 (1008)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0400 (1024)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0x73CE, 0x3146, 0x5ACB, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0410 (1040)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0x8C71, 0x39C7, 0x2945, 0x39C7, 0x4208,   // 0x0420 (1056)
0x4A08, 0x528A, 0x6B4D, 0xA534, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0430 (1072)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0440 (1088)
0xFFFF, 0xE73B, 0x630C, 0x3146, 0x738E, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0450 (1104)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xCE78, 0x8C71, 0x6B4D, 0x6B4D, 0x630C, 0x4A49, 0x3186, 0x3186, 0x4A49,   // 0x0460 (1120)
0x8430, 0xC638, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0470 (1136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73B, 0x630C, 0x3186,   // 0x0480 (1152)
0x8430, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0490 (1168)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xDEFB, 0xCE79, 0xCEB9, 0xC638, 0xAD75, 0x8C71, 0x630B, 0x39C7, 0x3145, 0x634D, 0xBE38, 0xEF7D,   // 0x04A0 (1184)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04B0 (1200)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73C, 0x630C, 0x3186, 0x8470, 0xEF7D, 0xFFFF, 0xFFFF,   // 0x04C0 (1216)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04D0 (1232)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xF7BE, 0xCEB9, 0x8C71, 0x41C7, 0x3185, 0x5ACB, 0xAD75, 0xD6BA, 0xD6BB, 0xDEFB, 0xEF7D,   // 0x04E0 (1248)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04F0 (1264)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC678, 0x528A, 0x3146, 0x7BCF, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0500 (1280)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0510 (1296)
0xFFFF, 0xFFFF, 0xFFFF, 0xE73C, 0x9CF3, 0x4A49, 0x3145, 0x4A49, 0x630C, 0x630C, 0x630D, 0x738E, 0x8CB2, 0xBDF7, 0xEF7D, 0xFFFF,   // 0x0520 (1312)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3B,   // 0x0530 (1328)
0xAD75, 0x738D, 0x3146, 0x2946, 0x6B4D, 0xE77C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0540 (1344)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0550 (1360)
0xE73C, 0x9CF3, 0x4A48, 0x3185, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x5ACB, 0x8CB2, 0xBE38, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0560 (1376)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFA, 0x94B1, 0x4208, 0x2946, 0x4208, 0x5ACB,   // 0x0570 (1392)
0x9CF2, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0580 (1408)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0x9CF3, 0x7BCF,   // 0x0590 (1424)
0x8430, 0x8430, 0x738E, 0x630C, 0x4A49, 0x39C6, 0x3185, 0x528B, 0xAD76, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05A0 (1440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FA, 0x738E, 0x3187, 0x2905, 0x5A8B, 0x9D33, 0xCEB9, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05B0 (1456)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05C0 (1472)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xE73C, 0xE77D, 0xEF7D, 0xE73C, 0xDEFB,   // 0x05D0 (1488)
0xBDF7, 0x8430, 0x4207, 0x3144, 0x4A49, 0x9CF4, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05E0 (1504)
0xDEFB, 0x7BCF, 0x2905, 0x3187, 0x7BCF, 0xC638, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05F0 (1520)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0600 (1536)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xDEFB, 0xAD76, 0x5ACB,   // 0x0610 (1552)
0x3144, 0x4A49, 0xA576, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBD, 0x9D33, 0x2946, 0x2946, 0x8430,   // 0x0620 (1568)
0xD6BA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0630 (1584)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0640 (1600)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xB5B7, 0x5ACB, 0x3144, 0x630C, 0xBDF8,   // 0x0650 (1616)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE37, 0x5ACB, 0x2905, 0x738D, 0xCEB9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0660 (1632)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0670 (1648)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0680 (1664)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xB5F7, 0x4A49, 0x3144, 0x738F, 0xE77D, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0690 (1680)
0xFFFF, 0xFFFF, 0xF7BE, 0x8470, 0x3146, 0x4A09, 0xC678, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06A0 (1696)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06B0 (1712)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06C0 (1728)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0x8431, 0x3185, 0x5289, 0xBDF8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0x630C,   // 0x06D0 (1744)
0x2946, 0x73CE, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06E0 (1760)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06F0 (1776)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0700 (1792)
0xFFFF, 0xBDF8, 0x5ACB, 0x39C6, 0x8431, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBDF6, 0x524A, 0x39C8, 0xA573, 0xF7BD, 0xFFFF,   // 0x0710 (1808)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xE77B, 0xDF7A, 0xDF7A, 0xEFBC, 0xFFFF,   // 0x0720 (1824)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xE73E, 0xF7BF, 0xFFFF,   // 0x0730 (1840)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0x7C30, 0x3185,   // 0x0740 (1856)
0x5ACB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9CF2, 0x39C8, 0x5ACB, 0xC638, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0750 (1872)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF79, 0xB6B2, 0xA66F, 0xA66F, 0xCEF6, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0760 (1888)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xADFB, 0x8CF8, 0xC67C, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0770 (1904)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x8CB2, 0x3186, 0x39C7, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0780 (1920)
0xFFFF, 0xFFFF, 0x8C71, 0x3987, 0x630C, 0xCEB9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0790 (1936)
0xFFFF, 0xFFFF, 0xD737, 0xA66E, 0x9E2D, 0x9E2D, 0xBEF3, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07A0 (1952)
0xFFFF, 0xFFFF, 0xF7FF, 0xC67C, 0x6C37, 0x4B55, 0x7477, 0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07B0 (1968)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x94F3, 0x3187, 0x39C7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x8C70, 0x3987,   // 0x07C0 (1984)
0x630C, 0xD6B9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD737, 0xA66F,   // 0x07D0 (2000)
0x9E2D, 0x9E2D, 0xC6F4, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x7C78,   // 0x07E0 (2016)
0x4B15, 0x4315, 0x4B15, 0x9D7A, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07F0 (2032)
0xFFFF, 0xFFFF, 0x94F3, 0x3187, 0x39C7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x94F2, 0x41C8, 0x5ACB, 0xC638, 0xF7BE, 0xFFFF,   // 0x0800 (2048)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD738, 0xAE70, 0x9E2D, 0x9E2D, 0xC6F4, 0xF7BE,   // 0x0810 (2064)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x9539, 0x5355, 0x4315, 0x4315, 0x4315, 0x5B96,   // 0x0820 (2080)
0xADBA, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x94B3, 0x3186,   // 0x0830 (2096)
0x4207, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5B5, 0x4A49, 0x4208, 0xA574, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0840 (2112)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD738, 0xAE70, 0x9E2D, 0x9E2D, 0xC6F4, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0850 (2128)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xADBA, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6C37, 0xBE3B, 0xF7BF, 0xFFFF,   // 0x0860 (2144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8431, 0x3186, 0x5ACB, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0870 (2160)
0xFFFF, 0xFFFF, 0xD6FA, 0x5ACB, 0x3146, 0x7BCE, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0880 (2176)
0xFFFF, 0xFFFF, 0xD738, 0xAE70, 0x9E2D, 0x9E2D, 0xC6F4, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0890 (2192)
0xADFB, 0x5B96, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x7477, 0xCEBC, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08A0 (2208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xC679, 0x630C, 0x39C6, 0x7C30, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0x842F,   // 0x08B0 (2224)
0x2946, 0x4A49, 0xCE78, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD738, 0xAE70,   // 0x08C0 (2240)
0x9E2D, 0x9E2D, 0xC6F4, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xBE7C, 0x63D6, 0x4B55, 0x63D6, 0x5B96,   // 0x08D0 (2256)
0x4B15, 0x4315, 0x4B55, 0x5B96, 0x63D6, 0x5355, 0x7CB8, 0xCEBD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08E0 (2272)
0xEF7D, 0x8C72, 0x39C6, 0x4A49, 0xB5B7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xB5F6, 0x524A, 0x2905, 0x7BCF, 0xD6BA,   // 0x08F0 (2288)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD738, 0xAE70, 0x9E2D, 0x9E2D, 0xC6F4, 0xF7BE,   // 0x0900 (2304)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x84B8, 0x63D6, 0x9539, 0xB63B, 0x84F8, 0x5355, 0x4315, 0x5355, 0xA57A,   // 0x0910 (2320)
0xB63B, 0x84B8, 0x63D7, 0xA5BB, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC639, 0x4A49, 0x3185, 0x738F,   // 0x0920 (2336)
0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0x94B2, 0x2905, 0x3987, 0x94B1, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0930 (2352)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD738, 0xAE70, 0x9E2D, 0x9E2D, 0xC6F4, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0940 (2368)
0xFFFF, 0xFFFF, 0xDF3E, 0xB63B, 0xC67C, 0xE73E, 0xE77E, 0x9539, 0x5355, 0x4315, 0x5355, 0xC67C, 0xF7BE, 0xDEFD, 0xB63C, 0xC67D,   // 0x0950 (2384)
0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC679, 0x630D, 0x3144, 0x5A8A, 0xADB7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0960 (2400)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FA, 0x738D, 0x2905, 0x41C8, 0x8C71, 0xCE79, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0970 (2416)
0xFFFF, 0xFFFF, 0xD738, 0xAE70, 0x9E2D, 0x9E2D, 0xC6F4, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xF7BE,   // 0x0980 (2432)
0xFFFF, 0xFFFF, 0xEFBE, 0x9539, 0x4B55, 0x4315, 0x4B56, 0xBE7C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0990 (2448)
0xFFFF, 0xE73C, 0xB5B7, 0x6B4D, 0x3144, 0x4207, 0x9CF4, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09A0 (2464)
0xFFFF, 0xCEB9, 0x6B4C, 0x3146, 0x3146, 0x6B4D, 0xADB5, 0xD6BA, 0xE73C, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD738, 0xAE70,   // 0x09B0 (2480)
0x9E2D, 0x9E2D, 0xC6F4, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9,   // 0x09C0 (2496)
0x4B55, 0x4315, 0x4B56, 0xB5FB, 0xFFFF, 0xFFFF, 0xF7BE, 0xEF7D, 0xEF7D, 0xEF7D, 0xE73C, 0xDEFC, 0xCE7A, 0x94F3, 0x4A48, 0x3144,   // 0x09D0 (2512)
0x4208, 0x8C72, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6B9, 0x842F,   // 0x09E0 (2528)
0x3987, 0x3146, 0x41C8, 0x528A, 0x8C71, 0xD6FA, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xD738, 0xAE70, 0x9E2D, 0x9E2D, 0xC6F5, 0xFFFF,   // 0x09F0 (2544)
0xFFFF, 0xFFFF, 0xFFFE, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9, 0x4B55, 0x4315, 0x4B56, 0xBE3C,   // 0x0A00 (2560)
0xFFFF, 0xE73C, 0xAD74, 0x9CF2, 0x9CF3, 0x94B2, 0x8471, 0x6B4D, 0x4A49, 0x41C7, 0x3185, 0x4A49, 0x94F3, 0xE73C, 0xFFFF, 0xFFFF,   // 0x0A10 (2576)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFA, 0xA534, 0x6B4D, 0x3146, 0x2105,   // 0x0A20 (2592)
0x5ACB, 0xD6F9, 0xDF79, 0xD777, 0xE77B, 0xFFFE, 0xDF79, 0xAE70, 0x9E2D, 0x9E2D, 0xCEF5, 0xFFFE, 0xEFBD, 0xD737, 0xD737, 0xEFBC,   // 0x0A30 (2608)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9, 0x4B55, 0x4315, 0x5356, 0xC6BC, 0xFFFF, 0xCEBA, 0x5ACA, 0x41C6,   // 0x0A40 (2624)
0x4208, 0x4208, 0x3986, 0x3186, 0x3185, 0x4A49, 0x7C30, 0xB5B6, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A50 (2640)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xCE78, 0x94F2, 0x734E, 0x9CF3, 0xEF7C, 0xDF78, 0xBEF2,   // 0x0A60 (2656)
0xBEB2, 0xCF37, 0xC6F5, 0xAE70, 0x9E2E, 0x9E2E, 0xBEB3, 0xD737, 0xC6F5, 0xAE70, 0xC6F3, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A70 (2672)
0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9, 0x4B55, 0x4315, 0x5356, 0xC6BC, 0xFFFF, 0xD6BA, 0x6B4D, 0x528A, 0x528A, 0x5ACB, 0x630C, 0x6B4E,   // 0x0A80 (2688)
0x8430, 0xAD75, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A90 (2704)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xF7BE, 0xFFFF, 0xEFBC, 0xCF35, 0xA66E, 0xA62E, 0xA66E, 0xA66E,   // 0x0AA0 (2720)
0x9E2E, 0xA62E, 0xA66E, 0xA62E, 0xA62E, 0xB6B1, 0xDF78, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9,   // 0x0AB0 (2736)
0x4B55, 0x4315, 0x4B56, 0xBE3C, 0xFFFF, 0xEF7E, 0xC638, 0xBDF7, 0xBDF8, 0xC638, 0xCE79, 0xDEFC, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AC0 (2752)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AD0 (2768)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB, 0xC6F4, 0x9E2E, 0x9E2D, 0x9E2E, 0x9E2E, 0x9E2E, 0x9E2D, 0x9E2D,   // 0x0AE0 (2784)
0xAE70, 0xD738, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9, 0x4B55, 0x4315, 0x4B56, 0xB5FB,   // 0x0AF0 (2800)
0xFFFF, 0xFFFF, 0xF7BE, 0xF7BE, 0xF7BE, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B00 (2816)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B10 (2832)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xB6B2, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F, 0xD737, 0xF7BE, 0xFFFF, 0xFFFF,   // 0x0B20 (2848)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9, 0x4B55, 0x4315, 0x4B55, 0xB5FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B30 (2864)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B40 (2880)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B50 (2896)
0xFFFE, 0xDF38, 0xB6B1, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66E, 0xC6F5, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B60 (2912)
0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9, 0x4B55, 0x4315, 0x4B55, 0xB63B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B70 (2928)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B80 (2944)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xD737, 0xA66F,   // 0x0B90 (2960)
0x9E2D, 0x9E2D, 0xBEF3, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8CF9,   // 0x0BA0 (2976)
0x4B15, 0x4315, 0x4B55, 0xB63B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BB0 (2992)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BC0 (3008)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xC6F5, 0xA62E, 0xB671, 0xDF79, 0xFFFF,   // 0x0BD0 (3024)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77E, 0x7CB8, 0x4315, 0x4315, 0x4315, 0xB5FB,   // 0x0BE0 (3040)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BF0 (3056)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C00 (3072)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB, 0xC6F5, 0xD738, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C10 (3088)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xA5BA, 0x7C78, 0x7477, 0x7CB8, 0xCEBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C20 (3104)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C30 (3120)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C40 (3136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xEFBD, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C50 (3152)
0xFFFF, 0xFFFF, 0xF7FF, 0xDEFD, 0xC67C, 0xBE7C, 0xC6BC, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C60 (3168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C70 (3184)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C80 (3200)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C90 (3216)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CA0 (3232)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CB0 (3248)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CC0 (3264)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CD0 (3280)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CE0 (3296)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CF0 (3312)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D00 (3328)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D10 (3344)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D20 (3360)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D30 (3376)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D40 (3392)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D50 (3408)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D60 (3424)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D70 (3440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D80 (3456)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D90 (3472)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DA0 (3488)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DB0 (3504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DC0 (3520)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DD0 (3536)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DE0 (3552)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DF0 (3568)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E00 (3584)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E10 (3600)
};

const unsigned short elrs_bind[0xE10] PROGMEM ={
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0010 (16)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0020 (32)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0030 (48)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0040 (64)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0050 (80)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0060 (96)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0070 (112)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0080 (128)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0090 (144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00A0 (160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00B0 (176)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x00C0 (192)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00D0 (208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xEFBC, 0xDF38, 0xC6F5, 0xB6B2, 0xAE71,   // 0x00E0 (224)
0xAE70, 0xAE6F, 0xAE70, 0xB6B2, 0xC6F4, 0xD737, 0xE7BB, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00F0 (240)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0100 (256)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0110 (272)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xD737, 0xBEF3, 0xB6B1, 0xA66F, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66E,   // 0x0120 (288)
0xAE70, 0xBEB2, 0xCEF6, 0xDF7A, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0130 (304)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0140 (320)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0150 (336)
0xF7FE, 0xDF79, 0xBEB3, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xB6B1,   // 0x0160 (352)
0xD737, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0170 (368)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0180 (384)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xD737, 0xAE70, 0xA62E, 0x9E2D,   // 0x0190 (400)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xA66F, 0xC6F5, 0xE7BB, 0xFFFF,   // 0x01A0 (416)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01B0 (432)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01C0 (448)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCF37, 0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x01D0 (464)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F, 0xC6F5, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01E0 (480)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01F0 (496)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0200 (512)
0xFFFF, 0xF7BD, 0xCF37, 0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xA66F, 0xA66F, 0xA66E, 0x9E2D,   // 0x0210 (528)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F, 0xCEF6, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0220 (544)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0230 (560)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCF37, 0xAE6F, 0x9E2D,   // 0x0240 (576)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xAE70, 0xBEF3, 0xCEF5, 0xCEF6, 0xC6F4, 0xB6B1, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0250 (592)
0x9E2D, 0x9E2D, 0x9E2D, 0xA66F, 0xDF7A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0260 (608)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0270 (624)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCF36, 0xAE6F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0280 (640)
0xA66E, 0xC6F5, 0xE77A, 0xEFBC, 0xF7BD, 0xF7BD, 0xEFBC, 0xE7BB, 0xD737, 0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0290 (656)
0xC6F5, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02A0 (672)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02B0 (688)
0xFFFF, 0xFFFF, 0xF7BD, 0xCF36, 0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE6F, 0xCEF5, 0xEFBC, 0xFFFF, 0xFFFF,   // 0x02C0 (704)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xD738, 0xB6B1, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xAE70, 0xDF38, 0xFFFE, 0xFFFF,   // 0x02D0 (720)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02E0 (736)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCF36, 0xAE70,   // 0x02F0 (752)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE6F, 0xCF35, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0300 (768)
0xFFFF, 0xFFFE, 0xD737, 0xA66E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xCEF6, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0310 (784)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0320 (800)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBD, 0xCF36, 0xAE6F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0330 (816)
0x9E2D, 0xA66E, 0xD736, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xB6B2,   // 0x0340 (832)
0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xC6F4, 0xEFBB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0350 (848)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0360 (864)
0xFFFF, 0xFFFF, 0xFFFF, 0xEFBD, 0xCEF6, 0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xAE6F, 0xD736, 0xF7BD, 0xFFFF,   // 0x0370 (880)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCF36, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0380 (896)
0x9E2D, 0xBEB3, 0xDF7A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0390 (912)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xE77B, 0xC6F5,   // 0x03A0 (928)
0xA66F, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE6F, 0xD735, 0xF7FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03B0 (944)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xDF38, 0xAE6F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xB6B2, 0xDF79, 0xFFFF,   // 0x03C0 (960)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03D0 (976)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB, 0xC6F4, 0xAEAE, 0xAE6D, 0xA66D, 0xA62D, 0x9E2D,   // 0x03E0 (992)
0x9E2E, 0x9E2D, 0xA66F, 0xD776, 0xF7FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03F0 (1008)
0xFFFF, 0xFFFF, 0xF7FE, 0xDF38, 0xAE6F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xBEB2, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0400 (1024)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0410 (1040)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE7B9, 0xC733, 0xC6F2, 0xC733, 0xC6F2, 0xB6B0, 0xA66E, 0x9E2E, 0xAE6F, 0xD776, 0xF7FD,   // 0x0420 (1056)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCEF6,   // 0x0430 (1072)
0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xBEB3, 0xE77A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0440 (1088)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0450 (1104)
0xFFFF, 0xE77D, 0xD6FB, 0xDF3C, 0xDF3C, 0xDF3B, 0xE779, 0xDF76, 0xD775, 0xDFB6, 0xF7FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0460 (1120)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xB6B1, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0470 (1136)
0x9E2D, 0xC6F4, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0480 (1152)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0490 (1168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04A0 (1184)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCEF6, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xCF36, 0xF7FE, 0xFFFF,   // 0x04B0 (1200)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04C0 (1216)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xE77E, 0xDEFD, 0xCEBD, 0xCEBD, 0xD6FD, 0xE73E, 0xF7BF, 0xFFFF,   // 0x04D0 (1232)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04E0 (1248)
0xF7FE, 0xCEF6, 0xAE6F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xB6B1, 0xDF79, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04F0 (1264)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0500 (1280)
0xFFFF, 0xE77E, 0xBE3B, 0x9539, 0x7C78, 0x6C37, 0x6BD7, 0x63D7, 0x6C37, 0x7438, 0x84B9, 0x9D7B, 0xCEBD, 0xF7BF, 0xFFFF, 0xFFFF,   // 0x0510 (1296)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xCF36, 0xAE70, 0x9E2D, 0x9E2D,   // 0x0520 (1312)
0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xCEF6, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0530 (1328)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xBE7C, 0x9539, 0x6C37, 0x5355,   // 0x0540 (1344)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5B96, 0x7478, 0x9D7A, 0xD6FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0550 (1360)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xD737, 0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xB6B1,   // 0x0560 (1376)
0xE77B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0570 (1392)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0xA5BA, 0x63D6, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0580 (1408)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B15, 0x7CB8, 0xC67C, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0590 (1424)
0xFFFF, 0xF7BD, 0xCF36, 0xAE6F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xD737, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05A0 (1440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05B0 (1456)
0xFFFF, 0xDF3D, 0x9D79, 0x5B96, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x05C0 (1472)
0x4315, 0x4315, 0x4B15, 0x6C37, 0xB63B, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xCEF6, 0xAE6F, 0x9E2D,   // 0x05D0 (1488)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xCF36, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05E0 (1504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFD, 0x9539, 0x5B96, 0x4315,   // 0x05F0 (1520)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0600 (1536)
0x6C37, 0xB63B, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCF36, 0xAE6F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0610 (1552)
0xAE70, 0xCF36, 0xEFBD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0620 (1568)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFD, 0x9539, 0x5396, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0630 (1584)
0x4315, 0x5356, 0x5357, 0x5358, 0x5357, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x6C37, 0xD6FD, 0xFFFF,   // 0x0640 (1600)
0xFFFF, 0xFFFF, 0xF7BD, 0xCF36, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xCEF6, 0xEFBD, 0xFFFF, 0xFFFF,   // 0x0650 (1616)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0660 (1632)
0xFFFF, 0xFFFF, 0xD6FD, 0x8D39, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B55, 0x7438, 0x9D7A, 0xB63B, 0xBE3C,   // 0x0670 (1648)
0xADBB, 0x84B9, 0x5B97, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x7439, 0xD6FD, 0xFFFF, 0xFFFF, 0xF7FD, 0xCF36, 0xA66F,   // 0x0680 (1664)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xCEF6, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0690 (1680)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x8CF9, 0x5355,   // 0x06A0 (1696)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x5B96, 0x9D7B, 0xD6FD, 0xEF7E, 0xF7FE, 0xFFFE, 0xF7BE, 0xDF3E, 0xC67D, 0x84B9,   // 0x06B0 (1712)
0x4B56, 0x4315, 0x4B14, 0x4B15, 0x7479, 0xCEBD, 0xF7BE, 0xFFFF, 0xF7FD, 0xCF35, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x06C0 (1728)
0x9E2D, 0xAE70, 0xCF36, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06D0 (1744)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x8CF9, 0x5356, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x06E0 (1760)
0x4315, 0x63D7, 0xA5BB, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFE, 0xF7FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x953B, 0x6C39, 0x6BD9, 0x8CFB,   // 0x06F0 (1776)
0xD6FD, 0xFFFF, 0xFFFF, 0xF7FC, 0xCF36, 0xA66F, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xCF36, 0xEFBD, 0xFFFF,   // 0x0700 (1792)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0710 (1808)
0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x8CF9, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5B96, 0xA5BB, 0xF7BF, 0xFFFF,   // 0x0720 (1824)
0xF7FE, 0xEFBA, 0xDF76, 0xD775, 0xDF78, 0xF7BC, 0xFFFF, 0xFFFF, 0xF7BF, 0xDEFD, 0xD6FD, 0xF7BE, 0xFFFF, 0xFFFF, 0xF7FC, 0xCF35,   // 0x0730 (1840)
0xA66F, 0x9E2E, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xCF36, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0740 (1856)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x8CF9,   // 0x0750 (1872)
0x5355, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5B96, 0xB5FB, 0xF7BF, 0xFFFF, 0xFFFF, 0xDF78, 0xBEF1, 0xA66E, 0xA66E,   // 0x0760 (1888)
0xAE6F, 0xCF33, 0xEFBB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB, 0xCF34, 0xA66F, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0770 (1904)
0x9E2D, 0x9E2D, 0xAE70, 0xCEF6, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0780 (1920)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x8CF9, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0790 (1936)
0x4315, 0x4315, 0x63D7, 0xB5FB, 0xEFBE, 0xFFFF, 0xF7BE, 0xDF77, 0xBEF1, 0x9E2E, 0x9E2E, 0x9E2E, 0x9E2E, 0xA66E, 0xBEB1, 0xD736,   // 0x07A0 (1952)
0xE7B9, 0xEFBB, 0xEFFB, 0xE7B9, 0xD776, 0xBEF2, 0xA66F, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xCF37, 0xF7BD,   // 0x07B0 (1968)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07C0 (1984)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x8CF9, 0x5356, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x63D6, 0xB5FB, 0xF7BF,   // 0x07D0 (2000)
0xFFFF, 0xFFFF, 0xF7FD, 0xCF34, 0xA62E, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0x9E2E, 0xAE6F, 0xBEF1, 0xC6F3, 0xC733, 0xBEF2,   // 0x07E0 (2016)
0xAE6F, 0x9E2E, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xCF37, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07F0 (2032)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD,   // 0x0800 (2048)
0x8CF9, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x63D6, 0xB63B, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE7B8,   // 0x0810 (2064)
0xBEF1, 0x9E2E, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0820 (2080)
0x9E2D, 0x9E2D, 0x9E2E, 0xAE70, 0xD737, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0830 (2096)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x8CF9, 0x4B55, 0x4315, 0x4315, 0x4315,   // 0x0840 (2112)
0x4315, 0x4315, 0x4315, 0x63D6, 0xBE3B, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xDF78, 0xBEF1, 0x9E2E, 0x9E2E,   // 0x0850 (2128)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xB671, 0xD737,   // 0x0860 (2144)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0870 (2160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x9539, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6C37, 0xBE3B,   // 0x0880 (2176)
0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xE779, 0xBEF2, 0xA62E, 0x9E2E, 0x9E2E, 0x9E2D, 0x9E2D,   // 0x0890 (2192)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xB6B2, 0xD738, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08A0 (2208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08B0 (2224)
0xEFBE, 0xA57A, 0x5B96, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6BD7, 0xB63B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08C0 (2240)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xEFBA, 0xCF34, 0xBEF0, 0xAEAE, 0xA66D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x08D0 (2256)
0x9E2D, 0x9E2D, 0xA66F, 0xAE71, 0xBEF4, 0xDF79, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08E0 (2272)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x63D6, 0x4315, 0x4315,   // 0x08F0 (2288)
0x4315, 0x4315, 0x4315, 0x4315, 0x63D6, 0xBE3B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0900 (2304)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7FD, 0xE77A, 0xCF35, 0xBEF1, 0xAEAF, 0xAE6E, 0xAE6E, 0xAE6E, 0xAE6E, 0xAEAF, 0xB6B1, 0xC6F5, 0xDF79,   // 0x0910 (2320)
0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0920 (2336)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77E, 0x9D7A, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x63D7,   // 0x0930 (2352)
0xBE3B, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0940 (2368)
0xFFFF, 0xEF7D, 0xEFBB, 0xE7B9, 0xDF78, 0xDF77, 0xDF77, 0xDF78, 0xE779, 0xEFBB, 0xEFBC, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0950 (2384)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0960 (2400)
0xFFFF, 0xFFFF, 0xF7BF, 0xCEBC, 0x7437, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x63D6, 0xB5FB, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0970 (2416)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0980 (2432)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0990 (2448)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xB5FB,   // 0x09A0 (2464)
0x5356, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B55, 0x9D7A, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09B0 (2480)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFD, 0xBE7C, 0xC67C, 0xDF3E, 0xE77E, 0xEF7E, 0xEF7E, 0xE77E, 0xEF7E,   // 0x09C0 (2496)
0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09D0 (2512)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3E, 0x9D7A, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x09E0 (2528)
0x4315, 0x6C37, 0xC67C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09F0 (2544)
0xFFFF, 0xFFFF, 0xDF3D, 0x84F8, 0x5B96, 0x5B96, 0x7438, 0x84BA, 0x953B, 0x94FA, 0x84BA, 0xADFC, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A00 (2560)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A10 (2576)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBD, 0x953A, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B15, 0x8CF9, 0xDEFD, 0xFFFF,   // 0x0A20 (2592)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFD, 0x8CF9, 0x4B55,   // 0x0A30 (2608)
0x4315, 0x4315, 0x4315, 0x4B56, 0x5356, 0x4B16, 0x5B97, 0xADBB, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A40 (2624)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A50 (2640)
0xFFFF, 0xFFFF, 0xC67C, 0x8CF9, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B55, 0x9539, 0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A60 (2656)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFD, 0x8CF9, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0A70 (2672)
0x4315, 0x63D6, 0x9D79, 0xD6FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A80 (2688)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBD, 0x953A,   // 0x0A90 (2704)
0x4315, 0x4315, 0x4315, 0x4315, 0x4B15, 0x8CF9, 0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AA0 (2720)
0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x8CF9, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x63D7, 0xADFB, 0xEF7E, 0xFFFF,   // 0x0AB0 (2736)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AC0 (2752)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3E, 0x9D7A, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0AD0 (2768)
0x4315, 0x6C37, 0xBE7C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFD, 0x84B8,   // 0x0AE0 (2784)
0x4B55, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6C37, 0xB5FB, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AF0 (2800)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B00 (2816)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xADFB, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B55, 0x9D79, 0xEFBE,   // 0x0B10 (2832)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x84F8, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0B20 (2848)
0x4315, 0x4315, 0x6C37, 0xB5FB, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B30 (2864)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B40 (2880)
0xFFFF, 0xFFFF, 0xF7BF, 0xCEBC, 0x6C37, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6C37, 0xB5FB, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B50 (2896)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xD6FD, 0x8CF8, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B15, 0x6C37, 0xB5FB, 0xEFBE,   // 0x0B60 (2912)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B70 (2928)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77E,   // 0x0B80 (2944)
0x9D7A, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B15, 0x63D6, 0xADFB, 0xE77E, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xC67C,   // 0x0B90 (2960)
0x7478, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6C37, 0xB5FB, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BA0 (2976)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BB0 (2992)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x63D6, 0x4315, 0x4315,   // 0x0BC0 (3008)
0x4315, 0x4315, 0x4315, 0x4315, 0x63D6, 0x84F8, 0xA5BA, 0xB63B, 0xBE3B, 0xADFB, 0x9539, 0x6C37, 0x4B15, 0x4315, 0x4315, 0x4315,   // 0x0BD0 (3024)
0x4315, 0x4315, 0x4315, 0x6C37, 0xB63B, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BE0 (3040)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BF0 (3056)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0x9D7A, 0x5356, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0C00 (3072)
0x4315, 0x4315, 0x5B96, 0x6C37, 0x6C37, 0x5BD6, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6C37, 0xB63B,   // 0x0C10 (3088)
0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C20 (3104)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C30 (3120)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0x8CF9, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0C40 (3136)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B15, 0x6C37, 0xBE3B, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C50 (3152)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C60 (3168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C70 (3184)
0xFFFF, 0xFFFF, 0xD6FD, 0x8CF9, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0C80 (3200)
0x4315, 0x4315, 0x4315, 0x4315, 0x7477, 0xBE3B, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C90 (3216)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CA0 (3232)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD,   // 0x0CB0 (3248)
0x9539, 0x63D6, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5356, 0x84B8,   // 0x0CC0 (3264)
0xC67C, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CD0 (3280)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CE0 (3296)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77E, 0xB5FB, 0x7CB8, 0x63D6,   // 0x0CF0 (3312)
0x5356, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B55, 0x5B96, 0x7477, 0x9D7A, 0xD6FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D00 (3328)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D10 (3344)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D20 (3360)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xDF3D, 0xB63B, 0x9539, 0x7477, 0x63D6, 0x5B96,   // 0x0D30 (3376)
0x5B96, 0x63D6, 0x6C37, 0x84F8, 0xADBA, 0xD6FD, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D40 (3392)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D50 (3408)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D60 (3424)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D70 (3440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D80 (3456)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D90 (3472)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DA0 (3488)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DB0 (3504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DC0 (3520)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DD0 (3536)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DE0 (3552)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DF0 (3568)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E00 (3584)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E10 (3600)
};

const unsigned short elrs_updatefw[0xE10] PROGMEM ={
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0010 (16)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0020 (32)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0030 (48)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0040 (64)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0050 (80)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0060 (96)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0070 (112)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0080 (128)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0090 (144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00A0 (160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00B0 (176)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x00C0 (192)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xE77E, 0xD6BD, 0xB5FB, 0x9539, 0xADBB, 0xDF3D, 0xFFFF,   // 0x00D0 (208)
0xFFFF, 0xFFFF, 0xF7FE, 0xF7FE, 0xF7FE, 0xF7BE, 0xF7BD, 0xEFBC, 0xEFBC, 0xE77B, 0xE77B, 0xE77A, 0xDF79, 0xDF39, 0xD738, 0xD737,   // 0x00E0 (224)
0xCEF6, 0xC6F5, 0xDF79, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00F0 (240)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0100 (256)
0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0xC6BC, 0xADFB, 0x9539, 0x7CB8, 0x6C37, 0x7C78, 0xBE3C, 0xFFFF, 0xFFFF, 0xF7FE, 0xEFBB, 0xDF79,   // 0x0110 (272)
0xD738, 0xD738, 0xD737, 0xCF36, 0xCEF6, 0xCEF6, 0xC6F5, 0xC6F5, 0xC6F4, 0xBEF4, 0xBEB3, 0xBEB3, 0xB6B1, 0xAE70, 0xD737, 0xF7FE,   // 0x0120 (288)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0130 (304)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xE73E, 0xC67C, 0x9539,   // 0x0140 (320)
0x6C37, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4B15, 0x7C79, 0xEF7E, 0xFFFF, 0xF7FD, 0xDF77, 0xAE70, 0x9E2E, 0xA66F, 0xA62E, 0x9E2D,   // 0x0150 (336)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xAE70, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0160 (352)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0170 (368)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xB5FB, 0x7CB8, 0x5B96, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0180 (384)
0x4315, 0x4314, 0x5B97, 0xCEBD, 0xFFFF, 0xFFFF, 0xE7B9, 0xAE6F, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0190 (400)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xDF79, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01A0 (416)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01B0 (432)
0xFFFF, 0xEF7E, 0xBE3B, 0x8CF9, 0x63D6, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B16, 0x8CF9,   // 0x01C0 (448)
0xE77D, 0xFFFF, 0xF7FD, 0xCF33, 0xA66F, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x01D0 (464)
0xB6B1, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01E0 (480)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0xA5BA, 0x6C37, 0x4315,   // 0x01F0 (496)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5396, 0xBE3C, 0xFFFF, 0xFFFF, 0xEFBA,   // 0x0200 (512)
0xBEF1, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE6F, 0xCEF5, 0xE77B, 0xFFFE,   // 0x0210 (528)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0220 (544)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x8CF9, 0x5356, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0230 (560)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B15, 0x953A, 0xE73E, 0xFFFF, 0xFFFF, 0xD775, 0x9E2E, 0x9E2D, 0x9E2D,   // 0x0240 (576)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xA62E, 0xBEB3, 0xDF79, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0250 (592)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0260 (608)
0xFFFF, 0xFFFF, 0xC67C, 0x7CB8, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0270 (624)
0x4315, 0x4315, 0x4315, 0x4315, 0x5356, 0xA57B, 0xFFFF, 0xFFFF, 0xE7B9, 0xB6B1, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0280 (640)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xB6B2, 0xD738, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0290 (656)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x7CB8, 0x4B15,   // 0x02A0 (672)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x02B0 (688)
0x4315, 0x7439, 0xEF7E, 0xFFFF, 0xF7FD, 0xD776, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x02C0 (704)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xB6B1, 0xDF38, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02D0 (720)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xD6FD, 0x7C78, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x02E0 (736)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x4314, 0x4B56, 0x5B97, 0x63D8, 0x6C38, 0x6C78, 0x7478, 0x953B, 0xE73E, 0xFFFF,   // 0x02F0 (752)
0xFFFF, 0xEFFB, 0xB6F1, 0xA66E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0300 (768)
0x9E2E, 0xB6B1, 0xDF79, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0310 (784)
0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x8CF9, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0320 (800)
0x4315, 0x4B15, 0x5396, 0x7CB9, 0xA5BB, 0xC67C, 0xD6FD, 0xE77E, 0xEF7E, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FD, 0xD776, 0xAE6F,   // 0x0330 (816)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xB6B2, 0xDF7A,   // 0x0340 (832)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xA57A,   // 0x0350 (848)
0x5356, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x4314, 0x63D7, 0x953A, 0xBE3C, 0xD6FD,   // 0x0360 (864)
0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73B, 0xAE31, 0xAEAE, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0370 (880)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xBEF4, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0380 (896)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB63B, 0x63D7, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0390 (912)
0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x4315, 0x5356, 0x7CBA, 0xB63C, 0xE73E, 0xFFFF, 0xFFFF, 0xEF7D, 0xD6B9, 0xC638, 0xC638,   // 0x03A0 (928)
0xC638, 0xC638, 0xC638, 0xC638, 0xBDF7, 0xD67A, 0xEF7D, 0xC675, 0xB6F0, 0x9E2D, 0x9E2E, 0xB6F0, 0xB6F0, 0xA66E, 0x9E2D, 0x9E2D,   // 0x03B0 (944)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F, 0xCEF6, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03C0 (960)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0x7477, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314,   // 0x03D0 (976)
0x4315, 0x5396, 0x8D39, 0xD6FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC5F7, 0x83CE, 0x62CA, 0x62CA, 0x62CB, 0x630B, 0x630B, 0x630C,   // 0x03E0 (992)
0x524A, 0x738F, 0xD6BA, 0xE77B, 0xCF34, 0xA6AE, 0xAEAE, 0xC6F5, 0xCEF6, 0xB6B1, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x03F0 (1008)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xE77A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0400 (1024)
0xEF7E, 0xA5BA, 0x5356, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x4315, 0x5397, 0xA5BB, 0xE73D, 0xFFFF,   // 0x0410 (1040)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xAD75, 0x5ACB, 0x3186, 0x3186, 0x3986, 0x3986, 0x39C6, 0x39C7, 0x3186, 0x4A0A, 0xB5B6, 0xF7FE,   // 0x0420 (1056)
0xDFB7, 0xBF70, 0xBEF2, 0xD6B9, 0xEF7D, 0xDF79, 0xB6F1, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0430 (1072)
0xA62E, 0xC6F5, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x63D7, 0x4B15, 0x4315,   // 0x0440 (1088)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x5397, 0xA5BB, 0xE77D, 0xFFFF, 0xE73C, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0450 (1104)
0xBDF7, 0x630C, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x4A09, 0xAD75, 0xFFFF, 0xE77B, 0xDFF6, 0xC6B5, 0xCE7A,   // 0x0460 (1120)
0xFFFF, 0xFFFE, 0xE7B9, 0xB6B1, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xD738, 0xFFFF,   // 0x0470 (1136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xADFB, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0480 (1152)
0x4315, 0x4315, 0x5356, 0x953B, 0xEF7E, 0xFFFF, 0xD6B9, 0x94B1, 0x94B1, 0xC638, 0xEF7D, 0xF7BE, 0xB5B6, 0x630C, 0x3186, 0x3186,   // 0x0490 (1168)
0x3186, 0x3186, 0x3186, 0x3186, 0x31C6, 0x4A49, 0x9CB3, 0xE73C, 0xF7BE, 0xD739, 0x9D31, 0x83D0, 0xCE79, 0xFFFF, 0xFFFE, 0xDFB7,   // 0x04A0 (1184)
0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xC6F4, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04B0 (1200)
0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x84B8, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x84F9, 0xD6FE,   // 0x04C0 (1216)
0xFFFF, 0xFFFF, 0xA532, 0x39C6, 0x39C7, 0x6B4D, 0xAD75, 0xB5B6, 0x7BCF, 0x4208, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x04D0 (1232)
0x3186, 0x39C7, 0x6B4D, 0xAD75, 0xC638, 0x94B2, 0x4208, 0x3187, 0x738F, 0xF7BE, 0xFFFF, 0xF7FD, 0xD776, 0xA66E, 0x9E2D, 0x9E2D,   // 0x04E0 (1248)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xB6B2, 0xDF7A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xADFB,   // 0x04F0 (1264)
0x5B96, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5B96, 0xB63C, 0xFFFF, 0xFFFF, 0xCE78, 0x734D, 0x3187,   // 0x0500 (1280)
0x3187, 0x39C7, 0x528A, 0x528A, 0x4208, 0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x4208, 0x528A,   // 0x0510 (1296)
0x528B, 0x41C8, 0x2946, 0x3186, 0x4A4A, 0xB5B7, 0xF7BE, 0xFFFF, 0xEFBB, 0xB6F1, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0520 (1312)
0x9E2D, 0x9E2D, 0xA66F, 0xCEF6, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x9539, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0530 (1328)
0x4315, 0x4315, 0x4315, 0x4314, 0x4B56, 0x8D3A, 0xDF3D, 0xFFFF, 0xFFFE, 0x8C70, 0x4A49, 0x3187, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0540 (1344)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3146, 0x3146, 0x3186, 0x39C6,   // 0x0550 (1360)
0x4208, 0x6B0D, 0xDEFB, 0xFFFF, 0xFFFE, 0xDF77, 0xAE6F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xBEB2,   // 0x0560 (1376)
0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x7CB8, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314,   // 0x0570 (1392)
0x5BD7, 0xC6BD, 0xFFFF, 0xFFFF, 0xCE78, 0x5ACB, 0x3187, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0580 (1408)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x39C8, 0xAD35, 0xF7BE,   // 0x0590 (1424)
0xFFFF, 0xF7FC, 0xBEF2, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE71, 0xE77B, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05A0 (1440)
0xFFFF, 0xFFFF, 0xB63B, 0x6C37, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x7479, 0xE73E, 0xFFFF, 0xDEFA,   // 0x05B0 (1456)
0x7BCE, 0x39C7, 0x3187, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x05C0 (1472)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x31C6, 0x3186, 0x630D, 0xC5F8, 0xFFFF, 0xFFFF, 0xD776, 0xA66E,   // 0x05D0 (1488)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9D7A, 0x63D6,   // 0x05E0 (1504)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5397, 0x953A, 0xEF7E, 0xEF7D, 0xA4F3, 0x4208, 0x3186, 0x3186, 0x3186,   // 0x05F0 (1520)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3187, 0x39C7, 0x6B4C, 0x94B1, 0xA4F3, 0x9CB1, 0x7BCE, 0x4208, 0x3187, 0x3186, 0x3186,   // 0x0600 (1536)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x734F, 0xE73C, 0xFFFF, 0xE7B9, 0xB6B0, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0610 (1552)
0x9E2D, 0x9E2D, 0x9E2D, 0xA66F, 0xCF36, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x8D39, 0x5B96, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0620 (1568)
0x4315, 0x4315, 0x4315, 0x6439, 0xB5FB, 0xF7BE, 0xDEBA, 0x738D, 0x3186, 0x3187, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0630 (1584)
0x3186, 0x4A49, 0x8430, 0xC639, 0xF7BE, 0xFFFF, 0xFFFF, 0xD6BB, 0x94B2, 0x528A, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0640 (1600)
0x3186, 0x3186, 0x31C6, 0x4A4A, 0xCE78, 0xFFFF, 0xEFFB, 0xC6F3, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F,   // 0x0650 (1616)
0xC6F5, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x84B8, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6C79,   // 0x0660 (1632)
0xC67C, 0xF7BF, 0xDEFB, 0x8C70, 0x4A49, 0x39C8, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x842F, 0xE73C, 0xFFFF,   // 0x0670 (1648)
0xFFFF, 0xF7BF, 0xFFFF, 0xFFFF, 0xEF7D, 0xA4F3, 0x4208, 0x3187, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x4A49, 0x6B4E,   // 0x0680 (1664)
0xCE79, 0xFFFF, 0xF7FC, 0xC734, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xBEB3, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0690 (1680)
0xFFFF, 0xFFFF, 0x7CB8, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x7478, 0xD6FD, 0xFFFF, 0xF7BE, 0xDEFB,   // 0x06A0 (1696)
0xBDF7, 0x8430, 0x4A49, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x5ACA, 0xB5B5, 0xFFFF, 0xFFFF, 0xCEBE, 0xADFD, 0xBE7D, 0xF7BF,   // 0x06B0 (1712)
0xFFFF, 0xD6B9, 0x6B4C, 0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x4208, 0x738E, 0xB5B6, 0xD6BB, 0xEFBD, 0xFFFF, 0xF7FD, 0xCF35,   // 0x06C0 (1728)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xB6B2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7478, 0x4B55,   // 0x06D0 (1744)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x7C78, 0xDEFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0x8C71, 0x3186,   // 0x06E0 (1760)
0x3186, 0x3186, 0x3187, 0x39C6, 0x7BCE, 0xD6FB, 0xFFFF, 0xEF7E, 0x8D3A, 0x5B97, 0x7479, 0xD6FD, 0xFFFF, 0xE73C, 0x9471, 0x4208,   // 0x06F0 (1776)
0x3187, 0x3186, 0x3186, 0x3186, 0x6B4D, 0xD6BA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FD, 0xCF35, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0700 (1792)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xB6B2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7478, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0710 (1808)
0x4315, 0x4315, 0x4315, 0x7CB8, 0xDF3E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0x94B2, 0x3186, 0x3186, 0x3186, 0x3187, 0x39C6,   // 0x0720 (1824)
0x83CE, 0xDEFC, 0xFFFF, 0xE77E, 0x84FA, 0x4B57, 0x6C39, 0xCEFD, 0xFFFF, 0xEF7C, 0x94B1, 0x4A48, 0x3187, 0x3186, 0x3186, 0x3186,   // 0x0730 (1840)
0x738E, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FD, 0xCF35, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E,   // 0x0740 (1856)
0xB6B2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7CB8, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x7CB8,   // 0x0750 (1872)
0xDEFE, 0xFFFF, 0xFFFF, 0xF7BE, 0xDEFB, 0xA534, 0x5ACB, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x630C, 0xC638, 0xFFFF, 0xFFFF,   // 0x0760 (1888)
0xB63C, 0x8D3B, 0xA5BB, 0xE77E, 0xFFFF, 0xDEFB, 0x7B8E, 0x41C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x4A49, 0x94B2, 0xD6BA, 0xF7BE,   // 0x0770 (1904)
0xFFFF, 0xFFFF, 0xF7FD, 0xCF35, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xB6B2, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0780 (1920)
0xFFFF, 0xFFFF, 0x84B8, 0x5356, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x7479, 0xD6FD, 0xFFFF, 0xE73C, 0xAD34,   // 0x0790 (1936)
0x734D, 0x528A, 0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x4208, 0x94B1, 0xF7BE, 0xFFFF, 0xEF7D, 0xDEFC, 0xE77D, 0xFFFF,   // 0x07A0 (1952)
0xFFFF, 0xB5B6, 0x4A49, 0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x4A49, 0x6B4D, 0x8C72, 0xDEFA, 0xFFFF, 0xF7FC, 0xC734,   // 0x07B0 (1968)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xBEB3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9539, 0x5B96,   // 0x07C0 (1984)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6C79, 0xC67C, 0xF7BE, 0xD6B9, 0x738D, 0x3186, 0x3187, 0x3186, 0x3186,   // 0x07D0 (2000)
0x3186, 0x3186, 0x3186, 0x3186, 0x3187, 0x5ACB, 0xA534, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73C, 0xB5B6, 0x734D, 0x3187, 0x3187,   // 0x07E0 (2016)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x4A0A, 0xC678, 0xFFFF, 0xEFFB, 0xC6F3, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x07F0 (2032)
0x9E2D, 0x9E2D, 0x9E2D, 0xA66E, 0xC6F4, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9D7A, 0x63D6, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0800 (2048)
0x4315, 0x4315, 0x4315, 0x5BD8, 0xA5BA, 0xEF7E, 0xE73C, 0x9470, 0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0810 (2064)
0x3186, 0x3187, 0x4A49, 0x8C70, 0xBDF6, 0xCE79, 0xC637, 0x9CF2, 0x5ACB, 0x3187, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0820 (2080)
0x3186, 0x3186, 0x3186, 0x6B0D, 0xDEFB, 0xFFFF, 0xE7B9, 0xB6F1, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F,   // 0x0830 (2096)
0xCEF6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5FB, 0x6C37, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B56,   // 0x0840 (2112)
0x84F9, 0xE77E, 0xFFFF, 0xCE38, 0x630B, 0x39C7, 0x3187, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x4208,   // 0x0850 (2128)
0x5249, 0x528A, 0x5289, 0x4A08, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x3186, 0x524A, 0xA535,   // 0x0860 (2144)
0xF7BE, 0xFFFF, 0xD776, 0xA66E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE6F, 0xD738, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0870 (2160)
0xFFFF, 0xFFFF, 0xCEBD, 0x7CB8, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x6438, 0xD6FE, 0xFFFF, 0xF7BE,   // 0x0880 (2176)
0xB5B5, 0x4A49, 0x3187, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0890 (2192)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x3187, 0x9472, 0xEF7D, 0xFFFF, 0xF7FD, 0xC733, 0x9E2D,   // 0x08A0 (2208)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xE77A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77E, 0x8CF9,   // 0x08B0 (2224)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x5356, 0xA5BB, 0xEF7E, 0xFFFF, 0xEF7D, 0x7B8D, 0x39C8, 0x3187,   // 0x08C0 (2240)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x08D0 (2256)
0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x524B, 0xCE7A, 0xFFFF, 0xFFFF, 0xDFB9, 0xB6B0, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x08E0 (2272)
0x9E2D, 0x9E2D, 0x9E2D, 0xB6B2, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xA5BA, 0x5356, 0x4315, 0x4315, 0x4315,   // 0x08F0 (2288)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6C37, 0xC67D, 0xFFFF, 0xFFFF, 0xB5B5, 0x630B, 0x3187, 0x3186, 0x3985, 0x39C6, 0x39C7,   // 0x0900 (2304)
0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x39C7, 0x39C7, 0x39C7, 0x3186, 0x39C6,   // 0x0910 (2320)
0x4A49, 0x9CB3, 0xEF7D, 0xFFFF, 0xEFFC, 0xC6F3, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66E, 0xC6F5,   // 0x0920 (2336)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67C, 0x7477, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0930 (2352)
0x4315, 0x4B15, 0x953B, 0xE73E, 0xFFFF, 0xEF7D, 0x9470, 0x3185, 0x3185, 0x630C, 0x9CF3, 0x9CF2, 0x630C, 0x39C7, 0x3186, 0x3186,   // 0x0940 (2368)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x5ACB, 0x8C71, 0x94B2, 0x630C, 0x39C6, 0x31C6, 0x62CC, 0xE73C, 0xFFFF, 0xF7FE,   // 0x0950 (2384)
0xDF78, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xB6B1, 0xDF39, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0960 (2400)
0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x9D7A, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x63D7, 0xA5BC,   // 0x0970 (2416)
0xF7BF, 0xFFFF, 0xCE79, 0x6B4B, 0x630C, 0xA577, 0xE73D, 0xE73C, 0xA4F3, 0x528A, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0980 (2432)
0x3186, 0x4A49, 0x94B2, 0xDEFB, 0xE73C, 0xB5B6, 0x734E, 0x5A8C, 0xA535, 0xFFFF, 0xFFFF, 0xE7FA, 0xBEF2, 0x9E2E, 0x9E2D, 0x9E2D,   // 0x0990 (2448)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xBEF4, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09A0 (2464)
0xC67C, 0x5396, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x63D7, 0xBE3C, 0xF7BF, 0xFFFF, 0xD6BA,   // 0x09B0 (2480)
0x9D36, 0x9D7B, 0xD6FD, 0xFFFF, 0xC637, 0x62CB, 0x3187, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x5289, 0xAD75, 0xEF7D,   // 0x09C0 (2496)
0xFFFF, 0xF7BE, 0xD67A, 0xCE39, 0xEFBD, 0xFFFF, 0xEFFC, 0xC6F3, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x09D0 (2512)
0x9E2D, 0xA66E, 0xD737, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x8CF9, 0x5355, 0x4315,   // 0x09E0 (2528)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x4315, 0x63D7, 0xBE7C, 0xFFFF, 0xEF7E, 0x9539, 0x5BD9, 0x957B, 0xF7BE,   // 0x09F0 (2544)
0xCE79, 0x62CA, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x4209, 0x94B2, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A00 (2560)
0xFFFF, 0xEFBC, 0xC734, 0xA66E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xBEB3, 0xE77B, 0xFFFF,   // 0x0A10 (2576)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xCEBC, 0x63D6, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0A20 (2592)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6C37, 0xB5FC, 0xB63C, 0x6C38, 0x4356, 0x747A, 0xDEFD, 0xD6FB, 0x83CE, 0x4A48, 0x4A49,   // 0x0A30 (2608)
0x4209, 0x41C8, 0x39C8, 0x39C8, 0x3988, 0x4A0A, 0x9473, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7BA, 0xC6F3, 0xA66E, 0x9E2D,   // 0x0A40 (2624)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66E, 0xDF38, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A50 (2640)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0x9D79, 0x5356, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0A60 (2656)
0x4314, 0x4315, 0x5BD7, 0x6C38, 0x4B56, 0x4315, 0x5BD7, 0x9D7A, 0xE73D, 0xCE79, 0x9CB2, 0x9CF3, 0xAD74, 0xB5B5, 0xADB5, 0xADB5,   // 0x0A70 (2672)
0xADB4, 0xBDF6, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFE, 0xEFBC, 0xDF78, 0xBEF2, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0A80 (2688)
0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xBEB3, 0xEFBD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A90 (2704)
0xFFFF, 0xFFFF, 0xDEFD, 0x8CF9, 0x4B15, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0AA0 (2720)
0x4315, 0x4314, 0x4315, 0x5BD8, 0xCEBD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xF7FE,   // 0x0AB0 (2736)
0xF7BD, 0xDF79, 0xC6F4, 0xAE6F, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xB6B2,   // 0x0AC0 (2752)
0xE77A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67C,   // 0x0AD0 (2768)
0x7477, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x4314, 0x4B56,   // 0x0AE0 (2784)
0x9D7B, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xF7FD, 0xE7BA, 0xDFB7, 0xC6F3, 0xAE70, 0xA62E, 0x9E2D,   // 0x0AF0 (2800)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xD737, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B00 (2816)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xBE3B, 0x63D6, 0x4315, 0x4315,   // 0x0B10 (2832)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4314, 0x4315, 0x63D7, 0xB63D, 0xFFFF, 0xFFFF,   // 0x0B20 (2848)
0xFFFE, 0xE77A, 0xCF36, 0xCF36, 0xCF35, 0xC6F4, 0xBEF2, 0xAEB0, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0B30 (2864)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE6F, 0xCF36, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B40 (2880)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xADBA, 0x63D6, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0B50 (2896)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x8CFA, 0xF7BE, 0xFFFF, 0xF7FE, 0xD776, 0xA66E, 0x9E6E,   // 0x0B60 (2912)
0xA66E, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0B70 (2928)
0xAE6F, 0xCEF6, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B80 (2944)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xADFB, 0x63D6, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0B90 (2960)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x6BD7, 0xBE3C, 0xFFFF, 0xFFFF, 0xDF78, 0xAE6E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0BA0 (2976)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xCEF6, 0xF7BD, 0xFFFF, 0xFFFF,   // 0x0BB0 (2992)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BC0 (3008)
0xFFFF, 0xFFFF, 0xEF7E, 0xB63B, 0x7477, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0BD0 (3024)
0x4315, 0x4B55, 0x7C79, 0xF7BF, 0xFFFF, 0xF7FC, 0xCF35, 0xA66F, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0BE0 (3040)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xB6B1, 0xD737, 0xEFBD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BF0 (3056)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF,   // 0x0C00 (3072)
0xC67C, 0x84F8, 0x5B96, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5B97, 0xCEBD,   // 0x0C10 (3088)
0xFFFF, 0xFFFF, 0xEFBB, 0xB6B0, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0C20 (3104)
0xA66F, 0xBEB3, 0xDF38, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C30 (3120)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x84B8, 0x4315,   // 0x0C40 (3136)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B56, 0x84FA, 0xDEFE, 0xFFFF, 0xF7FD, 0xCF34,   // 0x0C50 (3152)
0xA66E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66E, 0xAE71, 0xCEF5, 0xEFBB, 0xFFFF, 0xFFFF,   // 0x0C60 (3168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C70 (3184)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0x84B8, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0C80 (3200)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B56, 0xADBC, 0xFFFF, 0xFFFF, 0xEFBA, 0xBEF2, 0x9E2E, 0x9E2D, 0x9E2D,   // 0x0C90 (3216)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xAE70, 0xC6F5, 0xE77A, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CA0 (3232)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CB0 (3248)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0x9539, 0x5B96, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0CC0 (3264)
0x4315, 0x4315, 0x4315, 0x4315, 0x8CFA, 0xE77E, 0xFFFF, 0xFFFE, 0xCF34, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE6F, 0xB6B2,   // 0x0CD0 (3280)
0xCEF5, 0xDF79, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CE0 (3296)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF,   // 0x0CF0 (3312)
0xC67C, 0x63D6, 0x5356, 0x5BD6, 0x63D6, 0x63D6, 0x6BD7, 0x6C37, 0x6C37, 0x7437, 0x7477, 0x7C78, 0x7CB8, 0x8CF8, 0x8CF8, 0x8CF9,   // 0x0D00 (3328)
0xB5FB, 0xDF3E, 0xFFFF, 0xFFFF, 0xE7B8, 0xB6B1, 0xA66F, 0xAE70, 0xB6B2, 0xBEF4, 0xCF36, 0xDF7A, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D10 (3344)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D20 (3360)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xCEBC, 0x9D7A, 0x9D7A, 0xADFB,   // 0x0D30 (3376)
0xB5FB, 0xBE3B, 0xC67C, 0xCEBC, 0xCEBC, 0xD6FD, 0xDF3D, 0xE73E, 0xE77E, 0xEFBE, 0xF7BE, 0xF7BE, 0xF7BF, 0xF7BF, 0xFFFF, 0xFFFF,   // 0x0D40 (3392)
0xF7BD, 0xDF78, 0xCEF6, 0xD738, 0xE77B, 0xF7BD, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D50 (3408)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D60 (3424)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D70 (3440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D80 (3456)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D90 (3472)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DA0 (3488)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DB0 (3504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DC0 (3520)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DD0 (3536)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DE0 (3552)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DF0 (3568)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E00 (3584)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E10 (3600)
};

const unsigned short elrs_wifimode[0xE10] PROGMEM ={
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0010 (16)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0020 (32)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0030 (48)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0040 (64)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0050 (80)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0060 (96)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0070 (112)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0080 (128)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0090 (144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00A0 (160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00B0 (176)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x00C0 (192)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00D0 (208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00E0 (224)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00F0 (240)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0100 (256)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0110 (272)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0120 (288)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0130 (304)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0140 (320)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0150 (336)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0160 (352)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0170 (368)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0180 (384)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0190 (400)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01A0 (416)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01B0 (432)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x01C0 (448)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01D0 (464)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01E0 (480)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01F0 (496)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0200 (512)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0210 (528)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0220 (544)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0230 (560)
0xF7BE, 0xF7BE, 0xEF7D, 0xE73C, 0xDEFB, 0xCE79, 0xBDF7, 0xB5B6, 0xB5B6, 0xAD75, 0xB5B6, 0xB5B6, 0xBDF7, 0xC638, 0xD6BA, 0xE73C,   // 0x0240 (576)
0xEF7D, 0xF7BE, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0250 (592)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0260 (608)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xD6BA, 0xB5B6, 0x9CF3, 0x8430, 0x738E,   // 0x0270 (624)
0x630C, 0x5ACB, 0x528A, 0x528A, 0x528A, 0x528A, 0x528A, 0x528A, 0x528A, 0x5ACB, 0x5ACB, 0x6B4D, 0x7BCF, 0x94B2, 0xA534, 0xC638,   // 0x0280 (640)
0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0290 (656)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02A0 (672)
0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xD6BA, 0xB5B6, 0x94B2, 0x7BCF, 0x630C, 0x4A49, 0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x02B0 (688)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x4208, 0x528A, 0x6B4D, 0x8430, 0xA534, 0xBDF7, 0xDEFB,   // 0x02C0 (704)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02D0 (720)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xD6BA, 0xA534,   // 0x02E0 (736)
0x7BCF, 0x528A, 0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x02F0 (752)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x5ACB, 0x8430, 0xB5B6, 0xD6BA, 0xEF7D, 0xFFFF,   // 0x0300 (768)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0310 (784)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xCE79, 0x94B2, 0x630C, 0x4208, 0x39C7, 0x3186, 0x3186, 0x3186,   // 0x0320 (800)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0330 (816)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x4208, 0x6B4D, 0xA534, 0xDEFB, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0340 (832)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0350 (848)
0xFFFF, 0xFFFF, 0xD6BA, 0x9CF3, 0x6B4D, 0x4A49, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0360 (864)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0370 (880)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x528A, 0x738E, 0xAD75, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0380 (896)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73C, 0xB5B6, 0x7BCF, 0x4A49,   // 0x0390 (912)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x03A0 (928)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x03B0 (944)
0x3186, 0x3186, 0x3186, 0x3186, 0x528A, 0x94B2, 0xCE79, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03C0 (960)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0x9CF3, 0x528A, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x03D0 (976)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x03E0 (992)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x03F0 (1008)
0x3186, 0x39C7, 0x630C, 0xB5B6, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0400 (1024)
0xFFFF, 0xFFFF, 0xCE79, 0x7BCF, 0x4A49, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0410 (1040)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0420 (1056)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C7, 0x528A,   // 0x0430 (1072)
0x94B2, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBDF7, 0x738E, 0x39C7,   // 0x0440 (1088)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0450 (1104)
0x3186, 0x31C6, 0x31C6, 0x39C6, 0x39C6, 0x39C6, 0x39C6, 0x39C6, 0x39C6, 0x31C6, 0x31C6, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0460 (1120)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x4A49, 0x8430, 0xCE79, 0xFFFF,   // 0x0470 (1136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xBDF7, 0x630C, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0480 (1152)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x31C6, 0x39C7, 0x4A49, 0x528A, 0x5ACB, 0x62CC, 0x6B4D,   // 0x0490 (1168)
0x738E, 0x7B8F, 0x7B8F, 0x738E, 0x6B4D, 0x62CC, 0x5ACB, 0x528A, 0x4A09, 0x39C7, 0x3186, 0x31C6, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x04A0 (1184)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x4208, 0x7BCF, 0xCE79, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04B0 (1200)
0xFFFF, 0xFFFF, 0xDEFB, 0xC638, 0x5ACB, 0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x04C0 (1216)
0x3186, 0x3186, 0x3186, 0x39C7, 0x4208, 0x528A, 0x738F, 0x94B2, 0xAD76, 0xC638, 0xD6BA, 0xE6FC, 0xEF3D, 0xEF7D, 0xEF7D, 0xEF3D,   // 0x04D0 (1232)
0xDEFB, 0xD6BA, 0xBDF8, 0xAD35, 0x8C72, 0x6B4D, 0x4A49, 0x39C7, 0x39C7, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x04E0 (1248)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x4208, 0x7BCF, 0xCE79, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9CF3, 0x7BCF,   // 0x04F0 (1264)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x528A, 0x7BCF,   // 0x0500 (1280)
0xA534, 0xC638, 0xDEFB, 0xE73C, 0xF77E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D,   // 0x0510 (1296)
0xE73C, 0xD6BA, 0xBDF7, 0x94B3, 0x6B4E, 0x4208, 0x39C6, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0520 (1312)
0x3186, 0x3186, 0x3186, 0x4208, 0x94B3, 0xD6BA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x630C, 0x4A49, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0530 (1328)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x39C6, 0x528A, 0x7BCF, 0xAD75, 0xCE79, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0540 (1344)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D,   // 0x0550 (1360)
0xC5F8, 0x94B3, 0x6B4D, 0x4A49, 0x39C6, 0x31C6, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0560 (1376)
0x6B4D, 0xA534, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x4A49, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186,   // 0x0570 (1392)
0x31C6, 0x3186, 0x4208, 0x630C, 0x9CF4, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0580 (1408)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCE7A, 0x94B2,   // 0x0590 (1424)
0x528A, 0x39C7, 0x31C6, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x528A, 0x83D0, 0xFFFF, 0xFFFF,   // 0x05A0 (1440)
0xFFFF, 0xFFFF, 0x4A49, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x4A49, 0x8C31, 0xD6BA,   // 0x05B0 (1456)
0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xF7BD, 0xF7BD, 0xEFBD, 0xF7BD,   // 0x05C0 (1472)
0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xBDF7, 0x734E, 0x4208, 0x31C6,   // 0x05D0 (1488)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x5ACB, 0x8C71, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x738E, 0x5ACB,   // 0x05E0 (1504)
0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x3186, 0x39C6, 0x31C6, 0x5ACB, 0xA4F4, 0xDEFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05F0 (1520)
0xFFFF, 0xFFFF, 0xF7FE, 0xEFBB, 0xDF79, 0xD737, 0xCEF6, 0xC6F4, 0xBEF3, 0xBEB3, 0xBEB3, 0xBEF3, 0xC6F4, 0xCEF6, 0xD737, 0xDF79,   // 0x0600 (1536)
0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFBF, 0xCE7A, 0x8C72, 0x4A49, 0x31C6, 0x39C6, 0x3186, 0x3186,   // 0x0610 (1552)
0x3186, 0x3186, 0x3186, 0x3186, 0x7B8F, 0xB5B6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBDF7, 0x9CF3, 0x4A49, 0x3186, 0x3186, 0x3186,   // 0x0620 (1568)
0x3186, 0x39C6, 0x39C7, 0x5ACC, 0xAD75, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBD, 0xE77A, 0xD737, 0xC6F4,   // 0x0630 (1584)
0xB6B2, 0xAE6F, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xAE70, 0xB6B2, 0xC6F5, 0xD738, 0xE77B, 0xF7BD,   // 0x0640 (1600)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73C, 0x9CB3, 0x528A, 0x3186, 0x31C6, 0x3186, 0x3186, 0x3186, 0x3186, 0x528A,   // 0x0650 (1616)
0xB576, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xDEFB, 0x8430, 0x4A49, 0x4208, 0x39C7, 0x39C7, 0x4208, 0x6B4D, 0xBDB7,   // 0x0660 (1632)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xE77A, 0xCEF5, 0xB671, 0xA62E, 0x9E2E, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0670 (1648)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xA62E, 0xA62E, 0xB6B1, 0xCF37, 0xEFBC, 0xFFFE, 0xFFFF, 0xFFFF,   // 0x0680 (1664)
0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xA534, 0x5ACB, 0x4208, 0x39C7, 0x39C7, 0x4208, 0x528A, 0xA534, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0690 (1680)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFB, 0xB5B6, 0x8C71, 0x7BCF, 0x8430, 0xA534, 0xCE79, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06A0 (1696)
0xFFFF, 0xEFBC, 0xCEF6, 0xB6B1, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x06B0 (1712)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xAE6F, 0xB6B2, 0xD738, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06C0 (1728)
0xEF7D, 0xC638, 0x9CF3, 0x7BCF, 0x7BCF, 0x94B2, 0xBDF7, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06D0 (1744)
0xFFFF, 0xF7BE, 0xDEFB, 0xD6BA, 0xD6BA, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xC6F4, 0xAE6F, 0x9E2D,   // 0x06E0 (1760)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x06F0 (1776)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xCEF6, 0xEFBB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73C, 0xD6BA,   // 0x0700 (1792)
0xD6BA, 0xE73C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0710 (1808)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF7A, 0xB6B2, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0720 (1824)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0730 (1840)
0x9E2D, 0x9E2D, 0xA66F, 0xC6F4, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0740 (1856)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0750 (1872)
0xFFFF, 0xFFFF, 0xE77A, 0xB6B1, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0760 (1888)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F,   // 0x0770 (1904)
0xC6F4, 0xEFBB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0780 (1920)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xC6F4, 0x9E2D,   // 0x0790 (1936)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x07A0 (1952)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66E, 0xD737, 0xF7FE, 0xFFFF,   // 0x07B0 (1968)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07C0 (1984)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF78, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x07D0 (2000)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0x9E2E, 0x9E2E, 0x9E2E, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x07E0 (2016)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xBEF3, 0xE7BB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07F0 (2032)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0800 (2048)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD777, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0810 (2064)
0x9E2D, 0x9E2D, 0xA66E, 0xAE6F, 0xAE6F, 0xAE6F, 0xAE6F, 0xAE6F, 0xA66F, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0820 (2080)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xB6B1, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0830 (2096)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0840 (2112)
0xFFFF, 0xDF78, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0xAE70, 0xC6F4, 0xD777,   // 0x0850 (2128)
0xDF78, 0xE779, 0xE779, 0xDF78, 0xD736, 0xBEF3, 0xA66F, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0860 (2144)
0x9E2D, 0xB6B2, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0870 (2160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xB6B2, 0x9E2D,   // 0x0880 (2176)
0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2E, 0x9E2E, 0xAEB0, 0xCEF5, 0xDF79, 0xEFBB, 0xF7FD, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFE,   // 0x0890 (2192)
0xF7BD, 0xE7BB, 0xDF78, 0xC6F3, 0xAE6F, 0x9E2E, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xC6F5, 0xF7BD, 0xFFFF,   // 0x08A0 (2208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08B0 (2224)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xD738, 0xAE70, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x08C0 (2240)
0x9E2E, 0x9E2E, 0xB6B1, 0xD737, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB,   // 0x08D0 (2256)
0xCF35, 0xAE70, 0x9E2E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xAE70, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08E0 (2272)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08F0 (2288)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCEF6, 0xAE70, 0xA62E, 0xA62E, 0xA62E, 0xA62E, 0xBEB2, 0xE779, 0xF7FE,   // 0x0900 (2304)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xD737, 0xB6B1, 0xA62E,   // 0x0910 (2320)
0xA62E, 0xA62E, 0xA66E, 0xB6B1, 0xDF38, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0920 (2336)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0930 (2352)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xDF79, 0xCEF6, 0xC6F4, 0xC6F5, 0xD737, 0xEFBB, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0940 (2368)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FD, 0xE77A, 0xCF36, 0xC6F5, 0xC6F4, 0xD737, 0xE77A,   // 0x0950 (2384)
0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0960 (2400)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0970 (2416)
0xFFFF, 0xF7BD, 0xEFBC, 0xEFBC, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0xC67C, 0xB5FB, 0xB63B, 0xC67C,   // 0x0980 (2432)
0xE77E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xEFBC, 0xEFBC, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0990 (2448)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09A0 (2464)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09B0 (2480)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xCEBC, 0x9539, 0x63D6, 0x5356, 0x5396, 0x6C37, 0xA5BA, 0xE73D, 0xFFFF, 0xFFFF,   // 0x09C0 (2496)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09D0 (2512)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09E0 (2528)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09F0 (2544)
0xFFFF, 0xCEBC, 0x6C37, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315, 0x5396, 0x8CF9, 0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A00 (2560)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A10 (2576)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A20 (2592)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x9539, 0x4315, 0x4315,   // 0x0A30 (2608)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5356, 0xB5FB, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A40 (2624)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A50 (2640)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A60 (2656)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE7B, 0x5BD6, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0A70 (2672)
0x4315, 0x4315, 0x84F9, 0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A80 (2688)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A90 (2704)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AA0 (2720)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5FB, 0x4B55, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x7478, 0xC67C,   // 0x0AB0 (2736)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AC0 (2752)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AD0 (2768)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AE0 (2784)
0xB63B, 0x5356, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x7CB8, 0xCEBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AF0 (2800)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B00 (2816)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B10 (2832)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0x7CB8, 0x4315, 0x4315,   // 0x0B20 (2848)
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x9D7A, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B30 (2864)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B40 (2880)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B50 (2896)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xBE3B, 0x63D6, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,   // 0x0B60 (2912)
0x4B15, 0x6C37, 0xCEBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B70 (2928)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B80 (2944)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B90 (2960)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xADBA, 0x63D6, 0x4B15, 0x4B15, 0x4B15, 0x4B55, 0x7C78, 0xC67C, 0xF7BF, 0xFFFF,   // 0x0BA0 (2976)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BB0 (2992)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BC0 (3008)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BD0 (3024)
0xFFFF, 0xFFFF, 0xEF7E, 0xC67C, 0xA5BA, 0x9539, 0x9539, 0xADFB, 0xD6FD, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BE0 (3040)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BF0 (3056)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C00 (3072)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E,   // 0x0C10 (3088)
0xD6BD, 0xBE3B, 0xBE3B, 0xDEFD, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C20 (3104)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C30 (3120)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C40 (3136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C50 (3152)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C60 (3168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C70 (3184)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C80 (3200)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C90 (3216)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CA0 (3232)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CB0 (3248)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CC0 (3264)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CD0 (3280)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CE0 (3296)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CF0 (3312)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D00 (3328)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D10 (3344)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D20 (3360)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D30 (3376)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D40 (3392)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D50 (3408)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D60 (3424)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D70 (3440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D80 (3456)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D90 (3472)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DA0 (3488)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DB0 (3504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DC0 (3520)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DD0 (3536)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DE0 (3552)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DF0 (3568)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E00 (3584)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E10 (3600)
};

const unsigned short elrs_motion[0xE10] PROGMEM ={
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xE77A, 0xCF36, 0xBEB3, 0xB671, 0xAE6F,   // 0x0010 (16)
0xB6B2, 0xDF79, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0020 (32)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0030 (48)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0040 (64)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xD738, 0xC6F4, 0xB6B1, 0xAE6F, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0xBEB3, 0xEFBD, 0xFFFF,   // 0x0050 (80)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0060 (96)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0070 (112)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xDF79,   // 0x0080 (128)
0xC6F4, 0xAE70, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xC6F4, 0xEFBD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0090 (144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00A0 (160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00B0 (176)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xCEF6, 0xAE70, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x00C0 (192)
0xAE70, 0xBEB2, 0xBEB3, 0xC6F4, 0xCEF6, 0xE77A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00D0 (208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00E0 (224)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00F0 (240)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xC6F5, 0xA66F, 0x9E2D, 0x9E2D, 0xA62E, 0xAE70, 0xBEB3, 0xDF38, 0xEFBC, 0xF7BD, 0xF7FE,   // 0x0100 (256)
0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0110 (272)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77D, 0xD6FB, 0xDEFC, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0120 (288)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD,   // 0x0130 (304)
0xCEF5, 0xA66F, 0x9E2D, 0x9E2D, 0xA62E, 0xBEB3, 0xDF79, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0140 (320)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0150 (336)
0xFFFF, 0xFFFF, 0xCEFB, 0x9DB5, 0x8532, 0x8D33, 0xB638, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0160 (352)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xD738, 0xAE6F, 0x9E2D, 0x9E2D, 0xAE6F,   // 0x0170 (368)
0xC6F5, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0180 (384)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6BA, 0x8D33, 0x852F,   // 0x0190 (400)
0x852F, 0x856F, 0x8530, 0xBE79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01A0 (416)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xB6B1, 0x9E2D, 0x9E2D, 0xA62E, 0xCEF6, 0xF7BD, 0xFFFF, 0xFFFF, 0xF7FE,   // 0x01B0 (432)
0xDF79, 0xC6F5, 0xBEB3, 0xBEB3, 0xC6F4, 0xDF79, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01C0 (448)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67A, 0x8533, 0x7CEF, 0x852F, 0x852F, 0x856F, 0x852F, 0x9DB6,   // 0x01D0 (464)
0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01E0 (480)
0xFFFF, 0xF7FE, 0xCEF5, 0xA62E, 0x9E2D, 0xA62E, 0xC6F5, 0xF7BD, 0xFFFF, 0xFFFF, 0xEFBC, 0xCEF5, 0xAE70, 0xA62E, 0x9E2D, 0x9E2D,   // 0x01F0 (496)
0x9E2D, 0xBEB2, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0200 (512)
0xFFFF, 0xFFFF, 0xFFFF, 0xCEBB, 0x84F3, 0x7CEF, 0x7D2F, 0x7D2F, 0x852F, 0x852F, 0x7D2F, 0xA5F6, 0xF7BE, 0xFFFF, 0xFFFF, 0xEF7D,   // 0x0210 (528)
0xBE79, 0xB637, 0xD6FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xB671, 0x9E2D,   // 0x0220 (544)
0xA62E, 0xB6B2, 0xEFBC, 0xFFFF, 0xFFFF, 0xEFBB, 0xC6F4, 0xA62E, 0x9E2D, 0xA62D, 0xA62D, 0xA62D, 0xA66E, 0xC6F4, 0xF7BD, 0xFFFF,   // 0x0230 (560)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBB, 0x84F3,   // 0x0240 (576)
0x74B0, 0x7CEF, 0x7CEF, 0x7CEF, 0x7D2F, 0x7CEF, 0x84F2, 0xCEBB, 0xFFFF, 0xFFFF, 0xDF3C, 0x9DB5, 0x8530, 0x852F, 0x8D72, 0xBE79,   // 0x0250 (592)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xC6F5, 0xA62E, 0x9E2D, 0xAE6F, 0xD738, 0xFFFF, 0xFFFF,   // 0x0260 (608)
0xEFBC, 0xC6F4, 0xA62E, 0xA62D, 0xA62E, 0xA62F, 0xA630, 0xAE32, 0xC6B6, 0xE77C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0270 (624)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6BB, 0x7CF3, 0x6CB0, 0x74B0, 0x74B0, 0x74F0, 0x7CEF,   // 0x0280 (640)
0x74F0, 0x7CF2, 0xC6BA, 0xF7BE, 0xFFFF, 0xD6FB, 0x9574, 0x7D2F, 0x8D6E, 0x8DAE, 0x8D6F, 0x9572, 0xD6FB, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0290 (656)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xB6B2, 0x9E2D, 0x9E2D, 0xB6B2, 0xEFBC, 0xFFFF, 0xFFFE, 0xD737, 0xA66F, 0xA62D, 0x9DEE,   // 0x02A0 (672)
0x9DEF, 0xBEB6, 0xCEFA, 0xBE79, 0xB638, 0xC6BA, 0xD6FC, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02B0 (688)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE7A, 0x74B3, 0x6C70, 0x74B0, 0x74B0, 0x74B0, 0x74B0, 0x74B0, 0x7CF3, 0xC67A, 0xFFFF, 0xFFFF,   // 0x02C0 (704)
0xCEFB, 0x8D34, 0x7D2F, 0x856F, 0x8D6F, 0x8D6F, 0x8DAE, 0x8D70, 0xC679, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02D0 (720)
0xD738, 0xAE70, 0x9E2D, 0xA62E, 0xC6F5, 0xF7FE, 0xFFFF, 0xEFBC, 0xBEB2, 0x9E2D, 0xA62D, 0x9DEF, 0xB637, 0xEF7D, 0xD6FB, 0x9535,   // 0x02E0 (736)
0x7CF3, 0x7CB2, 0x84F4, 0xBE3A, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67B,   // 0x02F0 (752)
0x74B3, 0x6470, 0x6C70, 0x6C70, 0x6C70, 0x6CB0, 0x6CB0, 0x7CB3, 0xC67A, 0xFFFF, 0xFFFF, 0xCEFB, 0x8D34, 0x7CEF, 0x852F, 0x852F,   // 0x0300 (768)
0x856F, 0x8D6F, 0x8D6F, 0x8D72, 0xD6FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF36, 0xAE70, 0x9E2D, 0xAE6F,   // 0x0310 (784)
0xD737, 0xFFFE, 0xFFFF, 0xDF79, 0xB671, 0x9E2D, 0xA66E, 0xAE73, 0xE77D, 0xDF3C, 0x84F4, 0x74B0, 0x74B0, 0x74B0, 0x6C70, 0x74B3,   // 0x0320 (800)
0xC67B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6BB, 0x7CB4, 0x6430, 0x6430, 0x6470, 0x6C70,   // 0x0330 (816)
0x6C70, 0x6C70, 0x7CB3, 0xBE7A, 0xFFFF, 0xFFFF, 0xD6FC, 0x8D34, 0x74F0, 0x7D2F, 0x7D2F, 0x852F, 0x852F, 0x852F, 0x8531, 0xBE78,   // 0x0340 (832)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEF6, 0xAE6F, 0x9E2D, 0xAE70, 0xD738, 0xFFFE, 0xFFFF, 0xD737,   // 0x0350 (848)
0xAE70, 0x9E2D, 0xAE70, 0xCEF8, 0xEFBE, 0xA5B7, 0x6CB0, 0x74B0, 0x6C70, 0x6C70, 0x6C70, 0x6431, 0x9DB7, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0360 (864)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE7A, 0x74B4, 0x5BD1, 0x6431, 0x6430, 0x6431, 0x6430, 0x6431, 0x74B3, 0xBE7A, 0xFFFF,   // 0x0370 (880)
0xFFFF, 0xD6FC, 0x8CF4, 0x74B0, 0x74F0, 0x7CEF, 0x7CEF, 0x852F, 0x852F, 0x8531, 0xB638, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0380 (896)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F5, 0xA62E, 0x9E2D, 0xAE70, 0xDF38, 0xFFFF, 0xFFFF, 0xCF36, 0xA62E, 0x9E2D, 0xAE70, 0xE77B,   // 0x0390 (912)
0xEFBE, 0x8D35, 0x74B0, 0x6C70, 0x6C70, 0x6C70, 0x6C70, 0x6431, 0x9D77, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03A0 (928)
0xBE7B, 0x6C74, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x6431, 0x5C31, 0x7473, 0xBE7A, 0xF7BF, 0xFFFF, 0xD6FC, 0x84F4, 0x6C70, 0x74B0,   // 0x03B0 (944)
0x74B0, 0x74F0, 0x7CEF, 0x7CEF, 0x7CF1, 0xB638, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03C0 (960)
0xD737, 0xB671, 0xA66F, 0xB6B2, 0xE77B, 0xFFFF, 0xFFFF, 0xDF39, 0xB671, 0xA66F, 0xBEF3, 0xEFBD, 0xEFBE, 0x8D35, 0x6CB0, 0x6C70,   // 0x03D0 (976)
0x6C70, 0x6430, 0x6430, 0x5BD1, 0x9577, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67B, 0x6C34, 0x5391, 0x5BD1, 0x5BD1,   // 0x03E0 (992)
0x5BD1, 0x5BD1, 0x5BD1, 0x6C33, 0xBE7A, 0xF7FF, 0xFFFF, 0xDF3D, 0x84F5, 0x6430, 0x6C70, 0x6CB0, 0x74B0, 0x74B0, 0x74F0, 0x7CB1,   // 0x03F0 (1008)
0xADF8, 0xF7BE, 0xFFFF, 0xF7BE, 0xE73D, 0xE77D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBD, 0xD738, 0xCEF6, 0xDF79,   // 0x0400 (1024)
0xF7FE, 0xFFFF, 0xFFFF, 0xF7BD, 0xD738, 0xCEF6, 0xE77A, 0xFFFF, 0xDF3D, 0x84F4, 0x6C70, 0x6470, 0x6430, 0x6430, 0x6431, 0x5BD1,   // 0x0410 (1040)
0x9577, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67B, 0x6C34, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x6433, 0xBE3A,   // 0x0420 (1056)
0xFFFF, 0xFFFF, 0xDF3D, 0x84F5, 0x6431, 0x6C70, 0x6C70, 0x6C70, 0x74B0, 0x74B0, 0x74B1, 0xADF8, 0xEFBE, 0xFFFF, 0xE73D, 0xB638,   // 0x0430 (1072)
0x9575, 0x9575, 0xBE78, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xF7BD, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0440 (1088)
0xF7FE, 0xF7FE, 0xFFFF, 0xFFFF, 0xBE7A, 0x7472, 0x6C70, 0x6430, 0x6430, 0x6431, 0x5BD1, 0x5391, 0x9537, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0450 (1104)
0xF7FF, 0xBE3B, 0x6C34, 0x4B51, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x6C34, 0xB63A, 0xFFFF, 0xFFFF, 0xD6FC, 0x7CB5, 0x5BD1,   // 0x0460 (1120)
0x6431, 0x6430, 0x6C70, 0x6C70, 0x6C70, 0x74B2, 0xADF8, 0xEFBE, 0xFFFF, 0xDF3C, 0x9D76, 0x7CF0, 0x7D2F, 0x852F, 0x8530, 0xAE37,   // 0x0470 (1136)
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE,   // 0x0480 (1152)
0xA5B8, 0x6431, 0x6430, 0x6430, 0x6431, 0x5BD1, 0x5BD1, 0x5391, 0x9537, 0xFFFF, 0xFFFF, 0xF7FF, 0xBE3B, 0x63D4, 0x4B52, 0x4B52,   // 0x0490 (1168)
0x4B52, 0x4B52, 0x4B51, 0x4B52, 0x63D4, 0xBE3B, 0xFFFF, 0xFFFF, 0xCEBB, 0x7475, 0x5BD1, 0x5BD1, 0x5C31, 0x6430, 0x6430, 0x6430,   // 0x04A0 (1184)
0x7472, 0xB639, 0xF7BE, 0xFFFF, 0xDF3C, 0x9575, 0x74F0, 0x852F, 0x852F, 0x8D6F, 0x856F, 0x8D73, 0xDF3C, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04B0 (1200)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0x9DB7, 0x6431, 0x6430, 0x6431,   // 0x04C0 (1216)
0x5BD1, 0x5BD1, 0x5BD1, 0x5391, 0x9537, 0xFFFF, 0xFFFF, 0xC67B, 0x63D4, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x63D4,   // 0x04D0 (1232)
0xB63A, 0xFFFF, 0xFFFF, 0xCEBB, 0x7475, 0x5391, 0x5BD1, 0x5BD1, 0x5BD1, 0x6431, 0x6431, 0x6C32, 0xB5F9, 0xF7BF, 0xFFFF, 0xE73D,   // 0x04E0 (1248)
0x9576, 0x74B0, 0x7D2F, 0x7D2F, 0x852F, 0x856F, 0x852F, 0x8D73, 0xDF3C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04F0 (1264)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x9577, 0x6431, 0x6431, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x5391,   // 0x0500 (1280)
0x9538, 0xFFFF, 0xC67B, 0x6C35, 0x4B12, 0x4B52, 0x4B12, 0x4B12, 0x4B12, 0x4B12, 0x5353, 0xADFA, 0xFFFF, 0xFFFF, 0xCEBC, 0x7475,   // 0x0510 (1296)
0x4B51, 0x5391, 0x5391, 0x5B91, 0x5BD1, 0x5BD1, 0x6432, 0xADF9, 0xF7BE, 0xFFFF, 0xE77D, 0x9576, 0x6C70, 0x74EF, 0x7CEF, 0x7D2F,   // 0x0520 (1312)
0x852F, 0x852F, 0x8530, 0xB638, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0530 (1328)
0xFFFF, 0xFFFF, 0xFFFF, 0xE77D, 0x8D36, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x4B52, 0x84F7, 0xC67B, 0x6C35, 0x4B12,   // 0x0540 (1344)
0x4312, 0x4312, 0x4312, 0x4312, 0x4312, 0x4312, 0x5B94, 0xD6FC, 0xFFFF, 0xD6FC, 0x7476, 0x4B52, 0x4B51, 0x5351, 0x5391, 0x5391,   // 0x0550 (1360)
0x5BD1, 0x6432, 0xADF9, 0xF7BE, 0xFFFF, 0xE73D, 0x9576, 0x6C71, 0x74B0, 0x74B0, 0x7CEF, 0x7D2F, 0x852F, 0x7CF0, 0xA5B6, 0xEF7E,   // 0x0560 (1376)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D,   // 0x0570 (1392)
0x84F6, 0x5BD1, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x5351, 0x4B52, 0x5393, 0x5B93, 0x4312, 0x4312, 0x4312, 0x4312, 0x4312, 0x42D2,   // 0x0580 (1408)
0x42D2, 0x42D2, 0x4B13, 0x9D79, 0xC67B, 0x6C35, 0x4312, 0x4B52, 0x4B52, 0x4B52, 0x5391, 0x5391, 0x5BD3, 0xADB9, 0xF7BE, 0xFFFF,   // 0x0590 (1424)
0xE73D, 0x8D36, 0x6430, 0x6C70, 0x74B0, 0x74B0, 0x7CEF, 0x7CEF, 0x7CF1, 0xA5B7, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05A0 (1440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x84B5, 0x5BD1, 0x5BD1, 0x5B91,   // 0x05B0 (1456)
0x5391, 0x5391, 0x4B52, 0x4B52, 0x4B52, 0x4312, 0x4312, 0x4312, 0x4312, 0x42D2, 0x42D2, 0x3AD2, 0x3AD2, 0x3AD2, 0x42D2, 0x42D2,   // 0x05C0 (1472)
0x4312, 0x4312, 0x4312, 0x4312, 0x4B52, 0x4B52, 0x4B51, 0x5B93, 0xA5B9, 0xEFBE, 0xFFFF, 0xE73D, 0x8D36, 0x6431, 0x6C70, 0x6C70,   // 0x05D0 (1488)
0x6CB0, 0x74B0, 0x74EF, 0x74B0, 0xA5B7, 0xEFBE, 0xFFFF, 0xFFFF, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05E0 (1504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFC, 0x7CB5, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x4B51, 0x4B52, 0x4B52,   // 0x05F0 (1520)
0x4B12, 0x4312, 0x4312, 0x4312, 0x42D2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x4312, 0x4312, 0x4312,   // 0x0600 (1536)
0x4B12, 0x4B52, 0x5393, 0x9D78, 0xEFBE, 0xFFFF, 0xDF3D, 0x8CF6, 0x5BD1, 0x6431, 0x6C70, 0x6C70, 0x6CB0, 0x74B0, 0x74B1, 0x9DB6,   // 0x0610 (1552)
0xE77D, 0xFFFF, 0xEFBE, 0xCEBB, 0xBE39, 0xC6BA, 0xE77D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0620 (1568)
0xFFFF, 0xFFFF, 0xFFFF, 0xD6FC, 0x7474, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x4B52, 0x4B52, 0x4B52, 0x4312, 0x4312, 0x42D2, 0x3AD2,   // 0x0630 (1584)
0x3AD2, 0x3AD2, 0x3AD2, 0x3AD3, 0x3A92, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x42D2, 0x4312, 0x4312, 0x4312, 0x5393, 0xA5B9, 0xEFBE,   // 0x0640 (1600)
0xFFFF, 0xDEFC, 0x84F6, 0x5BD1, 0x5C31, 0x6430, 0x6430, 0x6C70, 0x6C70, 0x6C71, 0x9DB7, 0xE77D, 0xFFFF, 0xEF7D, 0xA5F7, 0x8532,   // 0x0650 (1616)
0x84F0, 0x8531, 0xA5B5, 0xE77D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC,   // 0x0660 (1632)
0x7474, 0x5BD1, 0x5391, 0x5391, 0x5351, 0x4B52, 0x4B52, 0x4312, 0x4312, 0x4312, 0x3AD2, 0x3AD2, 0x3AD2, 0x3A92, 0x3A92, 0x3293,   // 0x0670 (1648)
0x3293, 0x3A93, 0x3A93, 0x3A92, 0x3AD2, 0x3AD2, 0x42D2, 0x4312, 0x4B53, 0xADFA, 0xF7FF, 0xFFFF, 0xD6FC, 0x84B6, 0x5391, 0x5BD1,   // 0x0680 (1664)
0x5BD1, 0x6431, 0x6430, 0x6C70, 0x6C72, 0xA5B8, 0xEF7E, 0xFFFF, 0xEF7D, 0xA5B7, 0x7CF0, 0x856F, 0x8D6F, 0x8D6E, 0x8531, 0xC6B9,   // 0x0690 (1680)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBB, 0x7474, 0x5BD1, 0x5391, 0x5391,   // 0x06A0 (1696)
0x4B51, 0x4B52, 0x4B52, 0x4312, 0x4312, 0x42D2, 0x3AD2, 0x3AD2, 0x3A93, 0x3293, 0x3293, 0x3293, 0x3293, 0x3293, 0x3293, 0x3A93,   // 0x06B0 (1712)
0x3AD2, 0x3AD2, 0x3AD2, 0x42D2, 0x63D5, 0xDEFD, 0xFFFF, 0xDF3D, 0x84B6, 0x5352, 0x5391, 0x5BD1, 0x5BD1, 0x6431, 0x6430, 0x6431,   // 0x06C0 (1728)
0xA5B7, 0xF7BE, 0xFFFF, 0xE77D, 0xA5B7, 0x74B0, 0x852F, 0x856F, 0x8D6F, 0x8D6E, 0x8530, 0xBE78, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06D0 (1744)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67B, 0x6C73, 0x5BD1, 0x5391, 0x5391, 0x4B52, 0x4B52, 0x4B12, 0x4312,   // 0x06E0 (1760)
0x4312, 0x3AD2, 0x3AD2, 0x3AD2, 0x3A92, 0x3293, 0x3293, 0x3293, 0x3293, 0x3293, 0x3293, 0x3293, 0x3A93, 0x3AD2, 0x3AD2, 0x3AD2,   // 0x06F0 (1776)
0x4B53, 0x9538, 0xBE3B, 0x84B6, 0x4B52, 0x5391, 0x5391, 0x5BD1, 0x5BD1, 0x6431, 0x6431, 0x9536, 0xE77D, 0xFFFF, 0xE77D, 0x9DB7,   // 0x0700 (1792)
0x74B1, 0x7D2F, 0x852F, 0x852F, 0x856F, 0x856F, 0x8D71, 0xD739, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0710 (1808)
0xFFFF, 0xFFFF, 0xFFFF, 0xA5B9, 0x5BD2, 0x5BD1, 0x5391, 0x5391, 0x4B52, 0x4B52, 0x4312, 0x4312, 0x4312, 0x3AD2, 0x3AD2, 0x3A92,   // 0x0720 (1824)
0x3293, 0x3293, 0x3293, 0x3253, 0x3253, 0x3253, 0x3293, 0x3293, 0x3293, 0x3A92, 0x3AD2, 0x3AD2, 0x42D2, 0x4B53, 0x5353, 0x4B52,   // 0x0730 (1840)
0x4B52, 0x5391, 0x5391, 0x5BD1, 0x5BD1, 0x6432, 0x9577, 0xDF3D, 0xFFFF, 0xEF7E, 0xA5B7, 0x74B1, 0x7CEF, 0x7CEF, 0x852F, 0x852F,   // 0x0740 (1856)
0x856F, 0x8530, 0xADF5, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0x9D78,   // 0x0750 (1872)
0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x4B52, 0x4B52, 0x4312, 0x4312, 0x4312, 0x3AD2, 0x3AD2, 0x3A92, 0x3293, 0x3293, 0x3293, 0x3253,   // 0x0760 (1888)
0x3253, 0x3253, 0x3293, 0x3293, 0x3293, 0x3A92, 0x3AD2, 0x3AD2, 0x4312, 0x4312, 0x4B12, 0x4B52, 0x4B52, 0x5391, 0x5391, 0x5BD1,   // 0x0770 (1904)
0x63D2, 0x9DB8, 0xEF7E, 0xFFFF, 0xE77D, 0x9D77, 0x6C71, 0x74B0, 0x7CEF, 0x7CEF, 0x852F, 0x852F, 0x7CF0, 0xA5F6, 0xEFBD, 0xFFFF,   // 0x0780 (1920)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0x9D77, 0x5BD1, 0x5BD1, 0x5391, 0x5391,   // 0x0790 (1936)
0x4B52, 0x4B52, 0x4B12, 0x4312, 0x4312, 0x3AD2, 0x3AD2, 0x3A92, 0x3293, 0x3293, 0x3293, 0x3253, 0x3253, 0x3253, 0x3293, 0x3293,   // 0x07A0 (1952)
0x3A93, 0x3A92, 0x3AD2, 0x3AD2, 0x42D2, 0x4312, 0x4B12, 0x4B52, 0x4B52, 0x5391, 0x5391, 0x5BD2, 0x9537, 0xE77D, 0xFFFF, 0xEF7E,   // 0x07B0 (1968)
0x9DB7, 0x6C71, 0x74B0, 0x74B0, 0x7CEF, 0x7CEF, 0x852F, 0x7CF0, 0x9DB5, 0xE77D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07C0 (1984)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x8D36, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x4B52, 0x4B52, 0x4B12, 0x4312,   // 0x07D0 (2000)
0x4312, 0x3AD2, 0x3AD2, 0x3A92, 0x3A93, 0x3293, 0x3293, 0x3293, 0x3293, 0x3293, 0x3293, 0x3293, 0x3A93, 0x3AD2, 0x3AD2, 0x3AD2,   // 0x07E0 (2016)
0x4312, 0x4312, 0x4B12, 0x4B52, 0x4B52, 0x5391, 0x5391, 0x9537, 0xE77E, 0xFFFF, 0xEF7E, 0x9DB8, 0x6C71, 0x6C70, 0x74B0, 0x74EF,   // 0x07F0 (2032)
0x7CEF, 0x7CEF, 0x7CF0, 0x9575, 0xDF3C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0800 (2048)
0xFFFF, 0xFFFF, 0xDF3D, 0x84F5, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x4B51, 0x4B52, 0x4B12, 0x4312, 0x4312, 0x42D2, 0x3AD2, 0x3AD2,   // 0x0810 (2064)
0x3A92, 0x3A93, 0x3293, 0x3293, 0x3293, 0x3293, 0x3293, 0x3293, 0x3A92, 0x3AD2, 0x3AD2, 0x42D2, 0x4312, 0x4312, 0x4B52, 0x4B52,   // 0x0820 (2080)
0x4B51, 0x5391, 0x5392, 0xBE3A, 0xFFFF, 0xE77D, 0x9D77, 0x6C32, 0x6C70, 0x6CB0, 0x74B0, 0x74EF, 0x7CEF, 0x74B0, 0x9575, 0xDF3C,   // 0x0830 (2096)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FC, 0x7CB4,   // 0x0840 (2112)
0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x5351, 0x4B52, 0x4B52, 0x4312, 0x4312, 0x4312, 0x3AD2, 0x3AD2, 0x3AD2, 0x3A93, 0x3A93, 0x3293,   // 0x0850 (2128)
0x3293, 0x3293, 0x3A92, 0x3A92, 0x3AD2, 0x3AD2, 0x3AD2, 0x4312, 0x4312, 0x4312, 0x4B52, 0x4B52, 0x4B51, 0x5391, 0x5391, 0x9577,   // 0x0860 (2144)
0xC67B, 0x9537, 0x6431, 0x6C70, 0x6C70, 0x74B0, 0x74B0, 0x74F0, 0x74B0, 0x9DB6, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0870 (2160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBB, 0x7474, 0x5BD1, 0x5BD1, 0x5391, 0x5391,   // 0x0880 (2176)
0x5391, 0x4B51, 0x4B52, 0x4B52, 0x4312, 0x4312, 0x42D2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3A92, 0x3A93, 0x3A92, 0x3A92, 0x3AD2,   // 0x0890 (2192)
0x3AD2, 0x3AD2, 0x4312, 0x4312, 0x4312, 0x4B52, 0x4B52, 0x4B52, 0x5391, 0x5391, 0x5391, 0x6432, 0x6C73, 0x6431, 0x6430, 0x6C70,   // 0x08A0 (2208)
0x6C70, 0x74B0, 0x74B0, 0x74B0, 0x9D76, 0xE77D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08B0 (2224)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBB, 0x7474, 0x5BD1, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x4B51, 0x4B52, 0x4B52,   // 0x08C0 (2240)
0x4312, 0x4312, 0x4312, 0x42D2, 0x42D2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x42D2, 0x42D2, 0x4312, 0x4312,   // 0x08D0 (2256)
0x4312, 0x4B52, 0x4B52, 0x4B51, 0x5391, 0x5391, 0x5BD1, 0x5BD1, 0x6431, 0x6431, 0x6C70, 0x6C70, 0x6CB0, 0x74B0, 0x6CB0, 0x9575,   // 0x08E0 (2272)
0xE77D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08F0 (2288)
0xFFFF, 0xFFFF, 0xEF7E, 0x9577, 0x63D1, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x5391, 0x4B52, 0x4B52, 0x4B52, 0x4312, 0x4312, 0x4312,   // 0x0900 (2304)
0x4312, 0x42D2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x3AD2, 0x42D2, 0x42D2, 0x4312, 0x4312, 0x4312, 0x4B52, 0x4B52, 0x4B51, 0x5391,   // 0x0910 (2320)
0x5391, 0x5391, 0x5BD1, 0x5BD1, 0x6430, 0x6430, 0x6C70, 0x6C70, 0x6CB0, 0x6C71, 0x9576, 0xDF3C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0920 (2336)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFC,   // 0x0930 (2352)
0x8CF6, 0x5BD1, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x4B51, 0x4B52, 0x4B52, 0x4B52, 0x4312, 0x4312, 0x4312, 0x4312, 0x4312, 0x4312,   // 0x0940 (2368)
0x42D2, 0x42D2, 0x4312, 0x4312, 0x4312, 0x4312, 0x4312, 0x4B52, 0x4B52, 0x4B52, 0x4B91, 0x5391, 0x5391, 0x5BD1, 0x5BD1, 0x5C31,   // 0x0950 (2384)
0x6431, 0x6470, 0x6C70, 0x6C70, 0x6C71, 0x9576, 0xE77D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0960 (2400)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFC, 0x84F6, 0x5BD1, 0x5BD1,   // 0x0970 (2416)
0x5BD1, 0x5391, 0x5391, 0x4B51, 0x4B52, 0x4B52, 0x4B52, 0x4B12, 0x4B12, 0x4312, 0x4312, 0x4312, 0x4312, 0x4312, 0x4312, 0x4312,   // 0x0980 (2432)
0x4312, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B51, 0x5391, 0x5391, 0x5BD1, 0x5BD1, 0x5BD1, 0x6430, 0x6430, 0x6C70, 0x6C70, 0x6430,   // 0x0990 (2448)
0x9576, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09A0 (2464)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x8D36, 0x5BD1, 0x5BD1, 0x5B91, 0x5391, 0x5391,   // 0x09B0 (2480)
0x4B51, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B12, 0x4312, 0x4312, 0x4B12, 0x4B12, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B52,   // 0x09C0 (2496)
0x5351, 0x5391, 0x5391, 0x5391, 0x5BD1, 0x5BD1, 0x6431, 0x6430, 0x6470, 0x6C70, 0x6430, 0x8D35, 0xDF3C, 0xF7BE, 0xEFBB, 0xEFBC,   // 0x09D0 (2512)
0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xEFBB, 0xEFBC, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09E0 (2528)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0x8D36, 0x5BD2, 0x5BD1, 0x5391, 0x5391, 0x5391, 0x5391, 0x4B51, 0x4B52,   // 0x09F0 (2544)
0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B52, 0x4B51, 0x5391, 0x5391, 0x5391, 0x5B91, 0x5BD1,   // 0x0A00 (2560)
0x5BD1, 0x6431, 0x6430, 0x6430, 0x6C70, 0x6431, 0x9576, 0xE77C, 0xF7BE, 0xCEF7, 0xB6B1, 0xBEB2, 0xDF79, 0xFFFF, 0xFFFF, 0xF7BD,   // 0x0A10 (2576)
0xCEF6, 0xB6B1, 0xBEB3, 0xE77B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A20 (2592)
0xFFFF, 0xFFFF, 0xFFFF, 0xE77D, 0x9537, 0x5BD1, 0x5BD1, 0x5B91, 0x5391, 0x5391, 0x5391, 0x5391, 0x4B51, 0x4B52, 0x4B52, 0x4B52,   // 0x0A30 (2608)
0x4B52, 0x4B52, 0x4B52, 0x4B51, 0x5351, 0x5391, 0x5391, 0x5391, 0x5391, 0x5BD1, 0x5BD1, 0x5BD1, 0x6431, 0x6430, 0x6430, 0x6470,   // 0x0A40 (2624)
0x6432, 0xA5B7, 0xE77D, 0xFFFF, 0xEFBC, 0xB6B1, 0x9E2D, 0x9E2D, 0xCEF6, 0xFFFF, 0xFFFF, 0xE77B, 0xB6B1, 0x9E2D, 0xAE6F, 0xD738,   // 0x0A50 (2640)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A60 (2656)
0xE73D, 0x9536, 0x5BD1, 0x5BD1, 0x5BD1, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391,   // 0x0A70 (2672)
0x5391, 0x5391, 0x5391, 0x5B91, 0x5BD1, 0x5BD1, 0x5BD1, 0x63D1, 0x6430, 0x6430, 0x6430, 0x6C32, 0xADF8, 0xEFBE, 0xFFFF, 0xFFFF,   // 0x0A80 (2688)
0xDF7A, 0xAE70, 0x9E2D, 0xA62E, 0xCEF6, 0xFFFF, 0xFFFF, 0xE77A, 0xB671, 0x9E2D, 0xAE70, 0xD738, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A90 (2704)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77D, 0x8CF6, 0x5BD1,   // 0x0AA0 (2720)
0x5BD1, 0x5BD1, 0x5BD1, 0x5B91, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5391, 0x5B91, 0x5BD1, 0x5BD1,   // 0x0AB0 (2736)
0x5BD1, 0x5BD1, 0x6431, 0x6430, 0x6430, 0x6430, 0x7473, 0xB639, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFE, 0xD737, 0xAE6F, 0x9E2D, 0xAE6F,   // 0x0AC0 (2752)
0xD737, 0xFFFF, 0xFFFF, 0xDF79, 0xB671, 0x9E2D, 0xAE70, 0xDF38, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AD0 (2768)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x8CF6, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1,   // 0x0AE0 (2784)
0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x6431, 0x6431, 0x6430, 0x6430,   // 0x0AF0 (2800)
0x6C31, 0x84F5, 0xCEBA, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xB6B2, 0xA62E, 0x9E2D, 0xAE70, 0xDF79, 0xFFFF, 0xFFFE, 0xD737,   // 0x0B00 (2816)
0xAE70, 0x9E2D, 0xB671, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B10 (2832)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFC, 0x8CF6, 0x6431, 0x6431, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1,   // 0x0B20 (2848)
0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x5BD1, 0x6431, 0x6430, 0x6430, 0x6430, 0x6431, 0x7CB4, 0xA5B8, 0xDF3C, 0xFFFF, 0xFFFF,   // 0x0B30 (2864)
0xFFFE, 0xF7BD, 0xE77A, 0xBEF4, 0xA62E, 0x9E2D, 0x9E2D, 0xBEB3, 0xEFBD, 0xFFFF, 0xF7FE, 0xC6F4, 0xA62E, 0x9E2D, 0xB6B1, 0xE77B,   // 0x0B40 (2880)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B50 (2896)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x8D36, 0x5C31, 0x6431, 0x6431, 0x6431, 0x6431, 0x5BD1, 0x5C31, 0x5C31, 0x5C31, 0x6431,   // 0x0B60 (2912)
0x6431, 0x6430, 0x6430, 0x6430, 0x6431, 0x6C72, 0x9D77, 0xCEBB, 0xF7BE, 0xFFFF, 0xF7BD, 0xE77B, 0xDF38, 0xC6F5, 0xAE71, 0xA62E,   // 0x0B70 (2928)
0x9E2D, 0x9E2D, 0xB671, 0xDF79, 0xFFFF, 0xFFFF, 0xEFBB, 0xB671, 0x9E2D, 0x9E2D, 0xBEB2, 0xEFBD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B80 (2944)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B90 (2960)
0xFFFF, 0xDF3D, 0x9536, 0x6431, 0x6430, 0x6430, 0x6430, 0x6431, 0x6430, 0x6430, 0x6430, 0x6431, 0x6430, 0x6431, 0x6431, 0x7473,   // 0x0BA0 (2976)
0x9576, 0xBE3A, 0xF7BE, 0xFFFF, 0xFFFF, 0xF7BE, 0xD737, 0xB6B1, 0xAE70, 0xA62E, 0x9E2D, 0x9E2D, 0xA62E, 0xAE70, 0xD738, 0xF7FE,   // 0x0BB0 (2992)
0xFFFF, 0xF7BD, 0xCEF5, 0xA62E, 0x9E2D, 0xA66F, 0xCF36, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BC0 (3008)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x9536,   // 0x0BD0 (3024)
0x6431, 0x6430, 0x6470, 0x6470, 0x6430, 0x6430, 0x6430, 0x6431, 0x7473, 0x84F5, 0xA5B8, 0xC67B, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BE0 (3040)
0xFFFF, 0xF7BD, 0xBEB3, 0x9E2D, 0x9E2D, 0x9E2D, 0x9E2D, 0xA66F, 0xBEB3, 0xDF39, 0xF7FE, 0xFFFF, 0xF7FE, 0xD738, 0xAE6F, 0x9E2D,   // 0x0BF0 (3056)
0x9E2E, 0xBEB3, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C00 (3072)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0x9D77, 0x6C72, 0x6431, 0x6C72,   // 0x0C10 (3088)
0x7CB4, 0x8D35, 0x9536, 0xADF8, 0xC67B, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xD738, 0xB6B2,   // 0x0C20 (3104)
0xAE70, 0xB6B2, 0xBEF4, 0xD737, 0xEFBB, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF39, 0xB671, 0x9E2D, 0x9E2D, 0xAE70, 0xDF38, 0xFFFF, 0xFFFF,   // 0x0C30 (3120)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C40 (3136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xB639, 0xA5B7, 0xB639, 0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C50 (3152)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE77B, 0xDF79, 0xE77B, 0xF7BE, 0xFFFF,   // 0x0C60 (3168)
0xFFFF, 0xFFFF, 0xF7FE, 0xDF79, 0xB6B2, 0x9E2D, 0x9E2D, 0xA62E, 0xC6F5, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C70 (3184)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C80 (3200)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C90 (3216)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xEFBC, 0xD737, 0xB6B1,   // 0x0CA0 (3232)
0x9E2D, 0x9E2D, 0x9E2D, 0xB6B2, 0xE77A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CB0 (3248)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CC0 (3264)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CD0 (3280)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xF7FE, 0xF7BE, 0xF7BD, 0xE77B, 0xD737, 0xBEB3, 0xAE6F, 0xA62E, 0x9E2D, 0xA62E, 0xB6B1, 0xDF79,   // 0x0CE0 (3296)
0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CF0 (3312)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D00 (3328)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xD738,   // 0x0D10 (3344)
0xC6F5, 0xC6F4, 0xB6B2, 0xB671, 0xAE6F, 0xA62E, 0x9E2D, 0x9E2D, 0xA62E, 0xBEB3, 0xE77A, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D20 (3360)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D30 (3376)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D40 (3392)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCEF6, 0xAE70, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D,   // 0x0D50 (3408)
0x9E2D, 0x9E2D, 0xA62E, 0xAE70, 0xCEF6, 0xEFBB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D60 (3424)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D70 (3440)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D80 (3456)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xBEB3, 0xA62E, 0x9E2D, 0x9E2D, 0x9E2D, 0xA62E, 0xA66F, 0xB671, 0xC6F4, 0xDF38,   // 0x0D90 (3472)
0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DA0 (3488)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DB0 (3504)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DC0 (3520)
0xFFFF, 0xF7FE, 0xDF39, 0xBEB3, 0xB6B2, 0xB6B2, 0xBEB3, 0xC6F5, 0xD737, 0xDF79, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DD0 (3536)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DE0 (3552)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DF0 (3568)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xEFBD,   // 0x0E00 (3584)
0xEFBC, 0xEFBC, 0xF7BD, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E10 (3600)
};

const unsigned short elrs_fan[0xE10] PROGMEM ={
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0010 (16)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0020 (32)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0030 (48)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE,   // 0x0040 (64)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0050 (80)
0xE77E, 0xDF3D, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0060 (96)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0070 (112)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0080 (128)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0xD6BC, 0xBE3B, 0xA5BA, 0xADFA, 0xDEFD, 0xFFFF,   // 0x0090 (144)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00A0 (160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00B0 (176)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00C0 (192)
0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0xCEBC, 0x7477, 0x5B95, 0x7437, 0x9D79, 0xCEBC, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00D0 (208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00E0 (224)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x00F0 (240)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xCEBC, 0x5B95,   // 0x0100 (256)
0x4B14, 0x63D6, 0xADBA, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0110 (272)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0120 (288)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0130 (304)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x7437, 0x4314, 0x5B95, 0xC67B, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0140 (320)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0150 (336)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0160 (352)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0170 (368)
0xFFFF, 0xFFFF, 0xFFFF, 0xC67B, 0x4B14, 0x4B55, 0xA57A, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0180 (384)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0190 (400)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01A0 (416)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA5BA, 0x4B14,   // 0x01B0 (432)
0x6C36, 0xE77E, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xC6BC, 0xD6FD, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01C0 (448)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01D0 (464)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01E0 (480)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0x9539, 0x4314, 0x8CF8, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x01F0 (496)
0xFFFF, 0xFFFF, 0xFFFF, 0xADBA, 0x7C77, 0x7C77, 0xA57A, 0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0200 (512)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xCEBC,   // 0x0210 (528)
0xBE3B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0220 (544)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0x9538, 0x4314, 0x9538, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA5BA, 0x6C36, 0x5B95,   // 0x0230 (560)
0x8CF9, 0xC67C, 0xEF7D, 0xFFFF, 0xFFFF, 0xF7BD, 0xDF38, 0xCF35, 0xC6F4, 0xC6F3, 0xC6F3, 0xC6F3, 0xC6F4, 0xCEF6, 0xDF39, 0xF7FE,   // 0x0240 (576)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x9539, 0xA57A, 0xFFFF, 0xFFFF,   // 0x0250 (592)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9D39,   // 0x0260 (608)
0x4314, 0x9538, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x84B8, 0x4B54, 0x94F9, 0xBE3C, 0xEF7E, 0xFFFF, 0xFFFF, 0xE779,   // 0x0270 (624)
0xC6F4, 0xBEB2, 0xAE6F, 0xA66E, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xAE70, 0xBEB2, 0xCEF5, 0xDF38, 0xFFFF, 0xFFFF,   // 0x0280 (640)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x6C36, 0x9D79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0290 (656)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xADFA, 0x4314, 0x8CF8, 0xEFBE, 0xFFFF, 0xFFFF,   // 0x02A0 (672)
0xFFFF, 0xFFFF, 0xADBA, 0x63D6, 0x63D6, 0xA5BA, 0xF7BF, 0xFFFF, 0xFFFF, 0xF7BC, 0xCF35, 0xAE70, 0x9E2D, 0xA62D, 0xA62D, 0xA62D,   // 0x02B0 (688)
0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xAE6F, 0xC6F4, 0xD737, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02C0 (704)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE3B, 0x4B14, 0xADBA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x02D0 (720)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67C, 0x4314, 0x7CB7, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xA57A, 0x4B14, 0x8CF8,   // 0x02E0 (736)
0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xC6F4, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xA66E, 0xA62E,   // 0x02F0 (752)
0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0x9E2D, 0xA66D, 0xD736, 0xEFBD, 0xFFFF, 0xEF7E, 0xDEFD, 0xF7BF, 0xFFFF, 0xFFFF,   // 0x0300 (768)
0xFFFF, 0xFFFF, 0x9539, 0x4314, 0xC67C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0310 (784)
0xEF7E, 0x5355, 0x63D6, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xA5BA, 0x4314, 0x9539, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE,   // 0x0320 (800)
0xCF36, 0xA62E, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xBEB3, 0xC6F4, 0xCEF5, 0xCF36, 0xD737, 0xCF36, 0xC6F5, 0xC6F4, 0xBEB3, 0xAE70,   // 0x0330 (816)
0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA66D, 0xBEB4, 0xF7BD, 0xEFBE, 0xADBB, 0xB5FB, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x63D6,   // 0x0340 (832)
0x63D6, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9D79, 0x4314, 0xD6FD, 0xFFFF,   // 0x0350 (848)
0xFFFF, 0xFFFF, 0xF7BE, 0xADBA, 0x4314, 0x9539, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xBEB3, 0xA62D, 0xA62D, 0xA62D,   // 0x0360 (864)
0xA66E, 0xC6F4, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xBEF3, 0xB6B2, 0xA66F, 0xA62E,   // 0x0370 (880)
0x9E2D, 0xA66D, 0xC6F4, 0xF7BE, 0xD6FC, 0x7C78, 0xA5BA, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67C, 0x4314, 0xA57A, 0xF7BE, 0xFFFF,   // 0x0380 (896)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0x4314, 0x84B8, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE3B, 0x5355,   // 0x0390 (912)
0x9538, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xB6B1, 0xA62D, 0xA62D, 0xA62D, 0xB6B1, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03A0 (928)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xB6B2, 0xA62E, 0xA62D, 0xA62D, 0xA66D, 0xD737,   // 0x03B0 (944)
0xFFFF, 0xBE3B, 0x63D6, 0xB5FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7C77, 0x4314, 0xC67C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03C0 (960)
0xFFFF, 0xFFFF, 0xEFBE, 0x84B8, 0x4B55, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0x7477, 0x7C77, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03D0 (976)
0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xB6B2, 0xA62D, 0xA62D, 0xA62D, 0xB6B2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x03E0 (992)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xF7BD, 0xCEF6, 0xA66E, 0xA62E, 0xA62D, 0xA62D, 0x9E2D, 0xB6B0, 0xE77B, 0xFFFF, 0x953A, 0x63D6,   // 0x03F0 (1008)
0xE73E, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0x5355, 0x9539, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0x4B54,   // 0x0400 (1024)
0x9D79, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9539, 0x5B95, 0xCEBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB,   // 0x0410 (1040)
0xBEB2, 0xA62D, 0xA62D, 0xA62D, 0xB671, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xCEF6,   // 0x0420 (1056)
0xA62E, 0xA62E, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xB6AF, 0xDF39, 0xFFFF, 0xD6BD, 0x6396, 0x7437, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0430 (1072)
0xFFFF, 0x7C77, 0x4314, 0xBE3B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xA57A, 0x5355, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0440 (1088)
0xBE3B, 0x5355, 0x9539, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xC6F4, 0xA62E, 0xA62D, 0xA62D,   // 0x0450 (1104)
0xAE70, 0xDF7A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD737, 0xA66F, 0xA62E, 0xA62D, 0xA62D, 0xA62D, 0xA62D,   // 0x0460 (1120)
0xA62D, 0xA62E, 0xA66F, 0xB6B2, 0xE77A, 0xFFFF, 0xFFFF, 0xC67C, 0x4B54, 0xBE3B, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x5355, 0xA5BA,   // 0x0470 (1136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0x6BD6, 0x7CB7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x8CF8, 0x7477, 0xDF3E, 0xFFFF,   // 0x0480 (1152)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD737, 0xA66E, 0xA62D, 0xA62D, 0xA66F, 0xCEF6, 0xFFFF, 0xFFFF,   // 0x0490 (1168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD737, 0xA66F, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xAE6F, 0xB6B2, 0xDF38,   // 0x04A0 (1184)
0xF7FE, 0xFFFF, 0xFFFF, 0xE73E, 0x6C36, 0x6BD6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7477, 0x5B96, 0xD6BC, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04B0 (1200)
0xFFFF, 0xD6FD, 0x63D6, 0xD6FC, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0x63D6, 0x9D7B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x04C0 (1216)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF79, 0xAE70, 0xA62D, 0xA62D, 0xA62E, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD737,   // 0x04D0 (1232)
0xAE6F, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xAE70, 0xBEB2, 0xCF36, 0xE77B, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF,   // 0x04E0 (1248)
0xCEBC, 0x4B14, 0xCEBC, 0xFFFF, 0xFFFF, 0xFFFF, 0x9D79, 0x4314, 0xADBA, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xC67B, 0x84B8, 0xFFFF,   // 0x04F0 (1264)
0xFFFF, 0xFFFF, 0xFFFF, 0x9539, 0x7437, 0xDF3C, 0xFFFF, 0xF7BC, 0xE77B, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0500 (1280)
0xEFBC, 0xBEB3, 0xA62D, 0xA62D, 0xA62D, 0xB6B1, 0xF7BE, 0xDF79, 0xD737, 0xC6F5, 0xBEF3, 0xA66F, 0xA62D, 0xA62D, 0xA62D, 0xA62D,   // 0x0510 (1296)
0xA62E, 0xB6B1, 0xCEF5, 0xDF79, 0xEFBC, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x5B96, 0x7CB7, 0xF7BF,   // 0x0520 (1312)
0xFFFF, 0xFFFF, 0xDEFD, 0x5B95, 0x9539, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xB63B, 0xB5FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7CB8,   // 0x0530 (1328)
0xADBB, 0xFFFF, 0xEFBA, 0xBEF3, 0xBEB3, 0xCEF6, 0xE77B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEF6, 0xA62D, 0xA62D,   // 0x0540 (1344)
0xA62D, 0xA66E, 0xBEB3, 0xAE70, 0xA66E, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA66F, 0xBEB3, 0xD737, 0xE77B, 0xF7BD, 0xFFFF,   // 0x0550 (1360)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xA5BA, 0x5355, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0x7CB7,   // 0x0560 (1376)
0x6C36, 0xE73D, 0xFFFF, 0xFFFF, 0xEFBE, 0xD6FD, 0xE73E, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77E, 0x9539, 0xE77D, 0xEFBD, 0xBEF2, 0xA62D,   // 0x0570 (1392)
0xA62E, 0xAE6F, 0xBEB2, 0xDF79, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF38, 0xA62E, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D,   // 0x0580 (1408)
0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xBEB2, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0590 (1424)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0x4B54, 0xCEBC, 0xFFFF, 0xFFFF, 0xFFFF, 0x9539, 0x4B14, 0xB5FB, 0xFFFF, 0xFFFF,   // 0x05A0 (1440)
0xFFFF, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0xD6FC, 0xFFFF, 0xD776, 0x9E2E, 0xA62D, 0xA62D, 0xA62E, 0xA62E, 0xB670,   // 0x05B0 (1456)
0xD737, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B, 0xB6B1, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xAE6D, 0xAE6F, 0xAE6F, 0xAE6D, 0xA66D,   // 0x05C0 (1472)
0xA62D, 0xA62D, 0xB6B1, 0xE77A, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05D0 (1488)
0xFFFF, 0xF7BF, 0x5355, 0x9538, 0xEFBE, 0xFFFF, 0xFFFF, 0xA5BA, 0x4B14, 0x9D79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x05E0 (1504)
0xFFFF, 0xFFFF, 0xF7BF, 0xFFFF, 0xF7FE, 0xC6F3, 0x9E2D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xAE6F, 0xCEF6, 0xEFBD, 0xFFFF,   // 0x05F0 (1520)
0xFFFF, 0xF7BD, 0xBEF3, 0xA62D, 0xA62D, 0xA62D, 0xAE70, 0xB676, 0xB636, 0xB636, 0xB676, 0xAE71, 0xA66D, 0xA62D, 0xA62D, 0xA62E,   // 0x0600 (1536)
0xC6F5, 0xE77A, 0xEFBC, 0xEFBC, 0xF7BD, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0x6C37, 0x6BD6,   // 0x0610 (1552)
0xE77E, 0xFFFF, 0xFFFF, 0xBE3B, 0x5355, 0x9539, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0620 (1568)
0xDF3A, 0xB6B0, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xC6F4, 0xEFBC, 0xFFFF, 0xE77B, 0xB6B2, 0xA62D,   // 0x0630 (1584)
0xA62E, 0xAE70, 0xD73A, 0xE77E, 0x84B8, 0x7477, 0xDEFD, 0xDF3C, 0xB6B1, 0xA66D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xAE70, 0xB6B2,   // 0x0640 (1600)
0xC6F4, 0xD737, 0xE77A, 0xEFBD, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0x9539, 0x5B95, 0xE77E, 0xFFFF, 0xFFFF, 0xD6FD,   // 0x0650 (1616)
0x63D6, 0x8CF8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD737, 0xA66F, 0xA62D, 0xA62E,   // 0x0660 (1632)
0xA62E, 0xA66E, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xBEB2, 0xDF79, 0xBEB3, 0xA62D, 0xA62D, 0xAE6E, 0xC6B6, 0xC67B, 0x3AD4,   // 0x0670 (1648)
0x42D4, 0x4314, 0x3AD5, 0xB63B, 0xCEF9, 0xAEAE, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA66E, 0xAE70, 0xBEB2,   // 0x0680 (1664)
0xD737, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xADFB, 0x5355, 0xE73E, 0xFFFF, 0xFFFF, 0xE77E, 0x7477, 0x84F8, 0xFFFF, 0xFFFF,   // 0x0690 (1680)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF36, 0xA62E, 0xA62D, 0xA62E, 0xBEB3, 0xD737, 0xA62E, 0xA62E,   // 0x06A0 (1696)
0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA66D, 0xB6B2, 0xEFBE, 0x3A95, 0x4314, 0x4314, 0x4314, 0x4314, 0x3A95,   // 0x06B0 (1712)
0xBE79, 0xCEF6, 0xA66D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xAE6F, 0xBEB3, 0xE77A, 0xFFFF,   // 0x06C0 (1728)
0xFFFF, 0xFFFF, 0xC67C, 0x5355, 0xE73E, 0xFFFF, 0xFFFF, 0xEFBE, 0x7CB7, 0x84B8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x06D0 (1744)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF36, 0xA62E, 0xA62D, 0xA62D, 0xC6F4, 0xFFFF, 0xEFBC, 0xAE70, 0xA62D, 0xA62D, 0xA62D, 0xA62D,   // 0x06E0 (1760)
0xA62D, 0xA62D, 0xA62D, 0xA66D, 0xB6B2, 0xE77D, 0x3A94, 0x4314, 0x4314, 0x4314, 0x4314, 0x3A95, 0xC67B, 0xD6F8, 0xAE6D, 0x9E2D,   // 0x06F0 (1776)
0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xA66E, 0xBEB3, 0xEFBC, 0xFFFF, 0xFFFF, 0xCEBC, 0x5395,   // 0x0700 (1792)
0xE73E, 0xFFFF, 0xFFFF, 0xF7BE, 0x84B8, 0x84F8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0710 (1808)
0xD737, 0xA66F, 0xA62D, 0xA62D, 0xBEF4, 0xFFFF, 0xFFFF, 0xF7BD, 0xD738, 0xA66F, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA66D,   // 0x0720 (1824)
0xB6B2, 0xEF7D, 0x3A95, 0x4314, 0x4314, 0x4314, 0x4314, 0x3255, 0xBE7A, 0xCEF6, 0xA66D, 0xA62D, 0xA62E, 0xAE70, 0xAE70, 0xA62D,   // 0x0730 (1840)
0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xAE6F, 0xD736, 0xFFFF, 0xFFFF, 0xC67C, 0x5B95, 0xE73E, 0xFFFF, 0xFFFF, 0xF7BE,   // 0x0740 (1856)
0x84B8, 0x8CF8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF38, 0xAE6F, 0xA62D, 0xA62D,   // 0x0750 (1872)
0xBEB3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xEFBC, 0xD738, 0xBEF3, 0xAE70, 0xA62D, 0xA62D, 0xA62D, 0xAE6E, 0xC6B6, 0xDEFC, 0x3255,   // 0x0760 (1888)
0x3AD4, 0x42D4, 0x2A56, 0xCEFA, 0xDF39, 0xAE6F, 0xA62D, 0xA62D, 0xB670, 0xEFBC, 0xEFBC, 0xEFBC, 0xD737, 0xAE6F, 0xA62D, 0xA62D,   // 0x0770 (1904)
0xA62D, 0xA62D, 0xA62E, 0xC6F3, 0xFFFF, 0xFFFF, 0xBE3B, 0x6BD6, 0xE77E, 0xFFFF, 0xFFFF, 0xEF7E, 0x7CB7, 0x8CF8, 0xFFFF, 0xFFFF,   // 0x0780 (1920)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xB6B2, 0xA62D, 0xA62D, 0xBEB2, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0790 (1936)
0xFFFF, 0xFFFF, 0xFFFE, 0xF7BD, 0xEFBC, 0xD738, 0xAE70, 0xA62D, 0xA62D, 0xAE6F, 0xCEF8, 0xEF7C, 0xA5B9, 0x9539, 0xE77C, 0xDF79,   // 0x07A0 (1952)
0xB6B0, 0xA62D, 0xA62D, 0xA66F, 0xD737, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xEFBB, 0xD737, 0xAE6F, 0xA62D, 0xA62D, 0xA62D, 0xBEB1,   // 0x07B0 (1968)
0xF7FE, 0xF7FF, 0xADFB, 0x7CB7, 0xEF7E, 0xFFFF, 0xFFFF, 0xDF3D, 0x7CB7, 0x9D79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07C0 (1984)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F4, 0xA62D, 0xA62D, 0xAE70, 0xEFBB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x07D0 (2000)
0xFFFF, 0xFFFF, 0xCEF6, 0xA62D, 0xA62D, 0xA62D, 0xAEAF, 0xD735, 0xB675, 0xAE35, 0xCF36, 0xB6B0, 0xA62D, 0xA62D, 0xA62D, 0xBEB3,   // 0x07E0 (2016)
0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xC6F5, 0xA62E, 0xA62D, 0xA62D, 0xBEB1, 0xFFFF, 0xF7BF, 0xA5BA, 0x9D79,   // 0x07F0 (2032)
0xEFBE, 0xFFFF, 0xFFFF, 0xCEBC, 0x7CB7, 0xADBA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0800 (2048)
0xFFFF, 0xCEF6, 0xA62D, 0xA62D, 0xA62E, 0xC6F4, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xC6F4,   // 0x0810 (2064)
0xA62D, 0xA62D, 0xA62D, 0xA66D, 0xAE6D, 0xAE6E, 0xA66D, 0xA62D, 0xA62D, 0xA62D, 0xA66F, 0xD738, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0820 (2080)
0xFFFF, 0xFFFF, 0xFFFF, 0xDF79, 0xAE70, 0xA62D, 0xA62D, 0xBEB2, 0xFFFF, 0xF7BF, 0xA5BA, 0xCEBC, 0xF7FF, 0xFFFF, 0xFFFF, 0xC67B,   // 0x0830 (2096)
0x8CF8, 0xC67B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF79, 0xAE70, 0xA62D,   // 0x0840 (2112)
0xA62D, 0xAE70, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xB670, 0xA62D, 0xA62D, 0x9E2D,   // 0x0850 (2128)
0xA62D, 0xA62D, 0x9E2D, 0xA62D, 0xA62D, 0xA62D, 0xA66F, 0xCF36, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF38,   // 0x0860 (2144)
0xAE6F, 0xA62D, 0xA62D, 0xBEF2, 0xFFFF, 0xF7FF, 0xCEBC, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE3B, 0xA579, 0xE73D, 0xFFFF, 0xFFFF,   // 0x0870 (2160)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xC6F5, 0xA62D, 0xA62D, 0xA66E, 0xCF36, 0xFFFF,   // 0x0880 (2176)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF79, 0xB6B2, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xAE6F, 0xAE6F, 0xA62E,   // 0x0890 (2192)
0xA62D, 0xA62D, 0xA62D, 0xB6B1, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEF5, 0xA62D, 0xA62D, 0xA62E, 0xCF34,   // 0x08A0 (2208)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBC, 0xCEBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08B0 (2224)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF79, 0xA66F, 0xA62D, 0xA62D, 0xA66E, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08C0 (2240)
0xFFFF, 0xF7FE, 0xD737, 0xA62E, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xB6B1, 0xD737, 0xDF38, 0xBEB3, 0xA62E, 0xA62D, 0xA62D, 0xAE70,   // 0x08D0 (2256)
0xE77A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xBEB2, 0xA62D, 0xA62D, 0xAE70, 0xE77A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08E0 (2272)
0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x08F0 (2288)
0xFFFF, 0xFFFF, 0xF7FE, 0xCEF5, 0xA62D, 0xA62D, 0xA62D, 0xAE70, 0xEFBD, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCEF6, 0xA62D, 0xA62D,   // 0x0900 (2304)
0xA62D, 0xA62D, 0xA62E, 0xB6B1, 0xF7FE, 0xFFFF, 0xFFFF, 0xDF79, 0xAE70, 0xA62D, 0xA62D, 0xAE6F, 0xD737, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0910 (2320)
0xFFFF, 0xFFFF, 0xCEF6, 0xA62E, 0xA62D, 0xA62D, 0xC6F3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0920 (2336)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77B,   // 0x0930 (2352)
0xAE6F, 0xA62D, 0xA62D, 0xA62D, 0xB6B1, 0xEFBC, 0xF7FE, 0xEFBC, 0xB6B2, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xB6B1, 0xE77B,   // 0x0940 (2368)
0xFFFF, 0xFFFF, 0xFFFF, 0xD737, 0xA66F, 0xA62D, 0xA62D, 0xAE6F, 0xD738, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xB6B1, 0xA62D,   // 0x0950 (2384)
0xA62D, 0xA66E, 0xD737, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0960 (2400)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77A, 0xA62E, 0xA62D, 0xA62D,   // 0x0970 (2416)
0xA62D, 0xA62E, 0xA66F, 0xA62E, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xB671, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEF6,   // 0x0980 (2432)
0xA62E, 0xA62D, 0xA62D, 0xAE70, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB6B2, 0xA62D, 0xA62D, 0xA62D, 0xC6F4, 0xFFFF, 0xFFFF,   // 0x0990 (2448)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09A0 (2464)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE779, 0x9E2D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D,   // 0x09B0 (2480)
0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xB671, 0xDF38, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xC6F4, 0xA62E, 0xA62D, 0xA62D, 0xAE70,   // 0x09C0 (2496)
0xE77B, 0xFFFF, 0xFFFF, 0xFFFF, 0xBEB3, 0xA62E, 0xA62D, 0xA62D, 0xAE6F, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09D0 (2512)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x09E0 (2528)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF78, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62E, 0xB6B1,   // 0x09F0 (2544)
0xDF38, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xBEB3, 0xA62E, 0xA62D, 0xA62D, 0xAE70, 0xE77A, 0xFFFF, 0xFFFF, 0xBEB3,   // 0x0A00 (2560)
0xA62E, 0xA62D, 0xA62D, 0xA62D, 0xCF36, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A10 (2576)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF,   // 0x0A20 (2592)
0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB, 0xB6B1, 0x9E2D, 0xA62D, 0xA62D, 0xA62D, 0xA66F, 0xBEB3, 0xDF7A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A30 (2608)
0xFFFF, 0xFFFF, 0xE77A, 0xB6B2, 0xA62E, 0xA62D, 0xA62D, 0xB6B1, 0xF7BD, 0xFFFE, 0xB6B2, 0xA62E, 0xA62D, 0xA62D, 0xA62D, 0xCEF5,   // 0x0A40 (2624)
0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A50 (2640)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73D, 0xBE3B, 0xDF3D, 0xFFFF, 0xFFFF,   // 0x0A60 (2656)
0xEFBB, 0xE779, 0xCEF6, 0xC6F4, 0xC6F5, 0xD738, 0xEFBC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD738, 0xB670,   // 0x0A70 (2672)
0xA62E, 0xA62D, 0xA62D, 0xAE6F, 0xC6F4, 0xBEB2, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xCEF5, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A80 (2688)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0A90 (2704)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE, 0xB63B, 0x84B8, 0xC67C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xF7BD,   // 0x0AA0 (2720)
0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD737, 0xAE70, 0xA62E, 0xA62D, 0xA62D, 0xA62E,   // 0x0AB0 (2736)
0xA62D, 0xA62D, 0xA62D, 0x9E2D, 0xA62D, 0xCEF6, 0xF7FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AC0 (2752)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AD0 (2768)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0x9D79, 0x63D6, 0xADFA, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0AE0 (2784)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD737, 0xAE70, 0xA62E, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xA62D, 0xB6B1,   // 0x0AF0 (2800)
0xD736, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B00 (2816)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0xE77E, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B10 (2832)
0xFFFF, 0xF7FF, 0x9539, 0x5355, 0x9539, 0xBE7C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B20 (2848)
0xFFFF, 0xFFFF, 0xDF38, 0xB6B1, 0xA62E, 0xA62D, 0x9E2D, 0xA66D, 0xA66D, 0xAE70, 0xC6F5, 0xE77A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B30 (2864)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B40 (2880)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67C, 0xADBA, 0xD6FC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xADBA,   // 0x0B50 (2896)
0x63D6, 0x5355, 0xADFA, 0xD6FC, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xCF36,   // 0x0B60 (2912)
0xBEB1, 0xB6AF, 0xB6B0, 0xB6B2, 0xB675, 0xE779, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B70 (2928)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0B80 (2944)
0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0x9D79, 0x7CB7, 0xC67B, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0x7CB7, 0x4B55, 0x5B95,   // 0x0B90 (2960)
0xADBA, 0xDEFD, 0xE73E, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xDF3A, 0xDF39, 0xE77B, 0xFFFF,   // 0x0BA0 (2976)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BB0 (2992)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BC0 (3008)
0xF7BE, 0x8CF8, 0x5B95, 0xADBA, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FC, 0x7C77, 0x4B55, 0x4B55, 0x7477, 0xADFA,   // 0x0BD0 (3024)
0xDF3D, 0xE77E, 0xEF7E, 0xEF7E, 0xEF7E, 0xEF7E, 0xEF7E, 0xEF7E, 0xEFBE, 0xF7BE, 0xEF7E, 0xE73D, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BE0 (3040)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0BF0 (3056)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9D79, 0x4314,   // 0x0C00 (3072)
0x7CB7, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7E, 0xA5BA, 0x6C36, 0x5355, 0x5355, 0x5355, 0x5B95, 0x63D6,   // 0x0C10 (3088)
0x6C36, 0x6C37, 0x6BD7, 0x6BD7, 0x6BD7, 0x7477, 0x9D7A, 0xD6FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C20 (3104)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C30 (3120)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB63B, 0x4314, 0x5355, 0xADBA, 0xFFFF,   // 0x0C40 (3136)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xDEFD, 0xADBA, 0x8CF8, 0x7CB7, 0x7477, 0x7477, 0x8CF8, 0xA5BA,   // 0x0C50 (3152)
0xCEBC, 0xEFBE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C60 (3168)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C70 (3184)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xD6BC, 0x6C36, 0x4314, 0x5B96, 0xBE3B, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C80 (3200)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xF7BE, 0xEFBE, 0xEFBE, 0xEFBE, 0xF7BE, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0C90 (3216)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CA0 (3232)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CB0 (3248)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE77E, 0xBE3B, 0x4B55, 0x4B14, 0x63D6, 0x9539, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CC0 (3264)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CD0 (3280)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CE0 (3296)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0CF0 (3312)
0xFFFF, 0xFFFF, 0xFFFF, 0xD6FD, 0xBE3B, 0x5B95, 0x4314, 0x5B96, 0x7C77, 0x9D79, 0xD6FC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D00 (3328)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3D, 0xD6FC, 0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D10 (3344)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D20 (3360)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D30 (3376)
0xFFFF, 0xD6FC, 0xBE3B, 0x8CF8, 0x5355, 0x4314, 0x5395, 0x7437, 0x84B8, 0x8CF8, 0x9539, 0x9D79, 0xA5BA, 0xA5BA, 0xA57A, 0x9D79,   // 0x0D40 (3392)
0x9539, 0x8CF8, 0x8CF8, 0x8CF8, 0xB5FB, 0xDF3D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D50 (3408)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D60 (3424)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE,   // 0x0D70 (3440)
0xC67C, 0xADBA, 0x9D79, 0x7CB7, 0x5B95, 0x4B54, 0x4B14, 0x4B14, 0x4B54, 0x4B55, 0x5355, 0x5B95, 0x6C36, 0x84B8, 0xA5BA, 0xBE7B,   // 0x0D80 (3456)
0xDF3D, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0D90 (3472)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DA0 (3488)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBE,   // 0x0DB0 (3504)
0xCEBC, 0xB63B, 0xADFA, 0xA5BA, 0xA5BA, 0xA5BA, 0xADFA, 0xBE3B, 0xCEBC, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DC0 (3520)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DD0 (3536)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DE0 (3552)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0DF0 (3568)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E00 (3584)
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,   // 0x0E10 (3600)
};

const unsigned short elrs_vtx[0xE10] PROGMEM = {
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73F, 0xD6DC,
0xC65C, 0xC67C, 0xC65C, 0xC67C, 0xD6BD, 0xE75E, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xC69E, 0x9519, 0x6418, 0x4B35, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4B34, 0x5BD8, 0x9518, 0xBE7E, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF77E,
0x953C, 0x5B74, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x5334, 0x8D1A, 0xE73E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0x957C, 0x4B34, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4B33, 0x8D1C, 0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD71F, 0x6BD3, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x6393, 0xCEDF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6BC,
0x4B56, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4316, 0xCE5A, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6BF, 0x5332, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x5312, 0xBE7F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE75E, 0x5376, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B36, 0xE71D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xF7FF, 0x8434, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x6BD3, 0xF7DF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE1E, 0x4B13, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B13, 0x9D9D,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0x5377, 0x4B14, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x5355, 0xF79F, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xBE1A, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0xB5B8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0x7C13,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x6B93, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF7F, 0x5312, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x5311, 0xCEFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xADFE, 0x5312, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4B13, 0xA5BD, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0x84DA, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x747A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x5BD8, 0x4B14,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x5376, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7C76, 0xA5DD, 0xCE9C, 0xDF1F, 0xEF7E, 0xE75F,
0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F,
0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xEF7E, 0xE75F, 0xE73E, 0xCE9D,
0xADFC, 0x7C57, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xEF9D, 0xD737, 0xC6F6, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5,
0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5,
0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xC6F5, 0xCEF5, 0xCF37, 0xEF9C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF79C, 0xB6B1, 0xA62E,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62F, 0xB6B0, 0xEF5C, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF96, 0xA611, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xAE30, 0xCF56, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xF79C, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xE73B, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7BA, 0xCEF8, 0xD716, 0xCF17, 0xCF55, 0xAE52,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA62E, 0xAE90, 0xC713, 0xA630, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xAE50, 0xC714, 0xAE6F, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xAE50, 0xCF35, 0xD716, 0xCF17,
0xD717, 0xDF79, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE6F, 0xA62E, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xBE55, 0xFFFB, 0xFFFD, 0xCE96, 0xA62E, 0xA64D, 0xA62E, 0xAE6E, 0xEF9D, 0xFFFE, 0xD776, 0xAE10,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64E, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xA66E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA62E, 0xB6B1,
0xFFFF, 0xFFFF, 0xF7FA, 0xAE13, 0xA64E, 0x9E2E, 0xAE32, 0xE7D9, 0xFFFF, 0xFFFF, 0xBEF2, 0xA62F, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA64F, 0xA64E,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xBE54, 0xF7FB, 0xFFFF, 0xFFDE, 0xAE90, 0xA62E,
0xA62E, 0xAE6E, 0xEF9D, 0xFFFE, 0xFFFD, 0xCEB6, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA66E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E4E, 0xFFFF, 0xFFFF, 0xF7FB, 0xB634, 0xA64E, 0x9E2E, 0xAE10, 0xD797, 0xFFFF, 0xFFFF,
0xC712, 0xA610, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA64F, 0xA64E, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xEF9C, 0xF7DC, 0xAE70, 0xA62E, 0xA62E, 0xA64D, 0xA610, 0xCF54, 0xF7FC, 0xCEB6, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xFFDF, 0xFFFF,
0xFFFF, 0xFFFF, 0xA66E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB6B1, 0xA62E,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xB68F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xF7DD, 0xF7DE, 0xF7FC, 0xDF3A,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xD718, 0xEFFC, 0xF7DD, 0xF7DE,
0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF35, 0xAE0F, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA60F, 0xC713, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE75B, 0xAE6F, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0xA64F, 0xDF3A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE75A, 0xCEF5, 0xC6B3, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2,
0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2,
0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xCF14, 0xDF3A, 0xFFFD, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE9F, 0x4B12, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B12, 0xADFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0x6BB2, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x5B52, 0xEFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5D9, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0xA577, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFDF, 0x5377, 0x4B14, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x42F5, 0x4B35, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA5DD, 0x4B13,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B13, 0x957D, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DF, 0x6BB2, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x5B52, 0xEFBF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCE7B, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0xBE19, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x84DB, 0x4B13, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4B14, 0x7479,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xEFDF, 0x6393, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5B72, 0xDF7F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEDC, 0x4B36, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0xCE7B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE7F, 0x4B12, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4B12, 0xA5DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5D8, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0xA558, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB65F, 0x4B33, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B33, 0x9D9D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xD6BB, 0x5377, 0x4B14, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x4315, 0x5376, 0xC65B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DF, 0x9D38,
0x4B35, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x8C95, 0xE77F, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD71F, 0x94F7, 0x4B56, 0x4B14,
0x4315, 0x4B15, 0x4315, 0x4B15, 0x42F5, 0x4B35, 0x8CB7, 0xD6DF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6DD, 0xB5FC, 0xA59B, 0xA59A, 0xADDC,
0xD6DC, 0xF7DF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
};

const unsigned short elrs_joystick[0xE10] PROGMEM = {
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xAD77, 0x63B6, 0x7C78, 0xDEDC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xA5DD, 0x4B13, 0x4315, 0x4315, 0x4B33, 0x84DB, 0xF79E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7C7A, 0x4B13,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x5333, 0xA59C, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7479, 0x4314, 0x4B12, 0xAE1F, 0x5396, 0x4315,
0x4315, 0x4315, 0x6BD3, 0xD73F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7479, 0x4B13, 0x52F1, 0xF7FF, 0xF7BF, 0xA557, 0x4B15, 0x4B15, 0x4B14, 0x7459,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCE9C,
0xDEFC, 0xFFFF, 0x7479, 0x4314, 0x5310, 0xEFFF, 0xFFFF, 0xFFFF, 0x955C, 0x4B13, 0x4B15, 0x5397, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE7F, 0x5311, 0x4AF4, 0x7C99, 0x63F8, 0x4B14,
0x52F1, 0xF7FF, 0xCEDF, 0x7414, 0x42F5, 0x4B15, 0x4B14, 0x8CFA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6DF, 0x4B12, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B13, 0x7CDB, 0x4315, 0x4315,
0x4315, 0x4316, 0x94F6, 0xEFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xD6BB, 0x5BB7, 0x4B14, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x73F5, 0xD6FF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6DC,
0x6C18, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4316, 0xA578, 0xF7DF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC69F, 0x5311, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
0x4315, 0x5B96, 0x42F5, 0x4B15, 0x4315, 0x5B96, 0xC63A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE7F, 0x4B12, 0x4B14, 0x6C39, 0x63D7, 0x4315, 0x5311, 0xE7BF, 0x9D9C, 0x5334,
0x4315, 0x4315, 0x4B12, 0xA5BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FD, 0xDF39, 0xBED3, 0xB670, 0xA64F, 0xAE4E,
0xCEB7, 0xFFFD, 0xFFFF, 0xD69B, 0xD6BC, 0xFFFF, 0x7479, 0x4B13, 0x52F1, 0xF7FF, 0xFFFF, 0xEF7D, 0x7C9A, 0x4B13, 0x4315, 0x5BB7,
0xFFFF, 0xFFFF, 0xE7B9, 0xBE52, 0xA64F, 0xAE6E, 0xB672, 0xCF14, 0xEF7C, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xD71A, 0xA66E, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA610, 0xD797, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0x7479, 0x4314, 0x5310, 0xEFFF, 0xFFFF, 0xD6BC, 0x63F7, 0x4315, 0x4B14, 0x63D8, 0xFFFF, 0xFFFF, 0xAE8F, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA62E, 0xB6B1, 0xF79D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xBED2, 0xA60F, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xA5F0, 0xD775, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7479, 0x4B13,
0x52F1, 0xDF5F, 0x7C9A, 0x4B13, 0x42F5, 0x4B15, 0x5312, 0xBE7F, 0xFFFF, 0xFFFF, 0xA64E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xAE11, 0xE7B7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCEB8, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xB633, 0xEFFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7C99, 0x4314, 0x4315, 0x4B36, 0x4315, 0x4315,
0x4B14, 0x6C39, 0xE71D, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF54, 0xA610, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64E, 0xA64E,
0xF79D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDFB8, 0xADF0, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xB673, 0xD735, 0xDF39, 0xE79A,
0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x953C, 0x4B13, 0x42F5, 0x4B15, 0x4315, 0x5B96, 0xC63B, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xF79D, 0xDF59, 0xDF37, 0xC6F3, 0xAE50, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xBE34, 0xFFFC, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xB690, 0x9E2F, 0xA64D, 0x9E2E, 0xAE10, 0xD777, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xEFBF, 0x7414, 0x4315, 0x4B36, 0xAD98, 0xF7DF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xBED1, 0xA60F, 0xA64D, 0x9E2E, 0xA610, 0xD777, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7C, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xD6F9, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xEF5E, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xF7FB, 0xB633, 0x9E2E, 0xA62D, 0xA60E, 0xB690, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC697, 0xA64D, 0x9E2E, 0xA62E, 0xA66F, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xF7DF, 0xDF59, 0xE79B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBB, 0xDF3A, 0xFFDE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF3B, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xEF9D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FB, 0xADF2, 0x9E2E, 0xA62D, 0xA610, 0xCF34, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FB, 0xB632,
0x9E2E, 0xA62D, 0xDF1A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xE75B, 0xA62E, 0xA62D, 0xADF2, 0xF7FA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA64F, 0xA62E, 0x9E2E, 0xA62D, 0xCEB8, 0xFFFD,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xDF96, 0xA5F1, 0xA64D, 0x9E2E, 0xADF1, 0xE7FA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF96, 0xA5F1, 0xA64D, 0x9E2E, 0xBE54, 0xFFFD,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC676, 0xA64D, 0x9E2E,
0xA610, 0xCF56, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC713, 0xA610, 0xA64D, 0x9E2E, 0xB633, 0xF7FC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB6B1, 0xA62E, 0x9E2E, 0xA62D,
0xBE55, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD776, 0xAE10, 0x9E2E, 0xA62D, 0xB634, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC675, 0x9E2E, 0xA62D, 0xA610, 0xCF54, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xE7B8, 0xADF0, 0x9E2E, 0xA62D, 0xA5F1, 0xE7B7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xA64E, 0x9E2E, 0xA64D, 0x9E2E, 0xDF19, 0xFFFE, 0xFFFF, 0xFFFF,
0xF7DD, 0xF7BE, 0xCF55, 0xA610, 0xA64D, 0x9E2E, 0xB653, 0xEFFB, 0xF7DD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0xEFFA, 0xBE55, 0xA64D, 0x9E2E, 0xA62F, 0xC734, 0xF7DD, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFC, 0xB634,
0xA64D, 0x9E2E, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFE, 0xDF19, 0x9E2E, 0xA62D, 0xA62E, 0xA64E, 0xFFBE, 0xFFFF, 0xEFFB, 0xBE53, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xA62E, 0xA64E, 0xDF1A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE75B, 0xA64E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xB633, 0xEFF9, 0xFFFE, 0xD6F8, 0x9E2E, 0xA62D, 0xA62E, 0xA64E,
0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xB635,
0xA64D, 0x9E2E, 0xA62F, 0xB6D2, 0xFFFF, 0xFFFF, 0xC734, 0xA610, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xAE12, 0xF7FC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xAE13, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA62F, 0xBEF3, 0xFFFF, 0xF7BE, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xE73A, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1, 0x9E2E, 0xA62D, 0xA5F0, 0xDF96,
0xFFFF, 0xFFFF, 0xC714, 0xA60F, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xADF2, 0xFFFB,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FC, 0xB612, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0xA60F, 0xC6F2, 0xFFFF, 0xFFFF, 0xB691, 0xA62E, 0x9E2E, 0xA62D, 0xBE55, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xA610, 0xA64D, 0x9E2E, 0xAE12, 0xF7FB, 0xFFFF, 0xFFFF, 0xEFFA, 0xB633,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64E, 0x9E4E, 0xDF19, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFE, 0xDF3B, 0xA64E, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xB632, 0xE7FA, 0xFFFF, 0xFFFF,
0xD775, 0xA610, 0xA64D, 0x9E2E, 0xAE11, 0xEFFA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xAE6F, 0xA62E, 0x9E2E, 0xA62D, 0xCEB7, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBD, 0xF7BC, 0xCF34, 0xAE0F,
0x9E2E, 0xA62D, 0xB633, 0xEFD9, 0xEF9D, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDE, 0xF79C,
0xE7DA, 0xBE53, 0x9E2E, 0xA62D, 0xA60F, 0xC713, 0xEF9C, 0xF7BC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FB, 0xADF1, 0x9E2E, 0xA62D,
0xA5F0, 0xD755, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE77C, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xEF7C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD796, 0xA610, 0xA64D, 0x9E2E, 0xBE54, 0xF7FD,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xBE76, 0xA64D, 0x9E2E,
0xA610, 0xCF55, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC676, 0xA64D, 0x9E2E, 0xA62E, 0xAE90, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCEB7, 0x9E2E, 0xA62D, 0xA62E, 0xAE6F, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD797, 0xAE10, 0x9E2E, 0xA62D, 0xBE55, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC695, 0x9E2E, 0xA62D, 0xA5F0, 0xD755, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE75A, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xF79D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FB, 0xAE13, 0xA64D, 0x9E2E, 0xA60F, 0xC734, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xF7FA, 0xAE13, 0xA64D, 0x9E2E, 0xDF19, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF3B, 0xA64D, 0x9E2E, 0xAE12, 0xEFFA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xA66E, 0x9E2E, 0xA64D, 0x9E2E, 0xD6F8, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xDF97, 0xAE10, 0x9E2E, 0xA62D, 0xA5F1, 0xEFF9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDE,
0xDF39, 0xE79A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xE79B, 0xDF38, 0xF79E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F,
0x9E2E, 0xA62D, 0xB613, 0xFFFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBEF2, 0xA62F, 0xA64D, 0x9E2E,
0xBE54, 0xF7FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xAE11, 0xDFD8,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDE, 0xA62E, 0xA62D, 0x9E2E, 0xA62D, 0xD6F9, 0xFFFD, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FC, 0xB633, 0x9E2E, 0xA62D, 0xA60F, 0xC712, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFE, 0xDF3B, 0xA64D, 0x9E2E, 0xA64E, 0x9E2E, 0xFFDE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFD, 0xCED8, 0xA64D, 0x9E2E, 0xA62E, 0xA64F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCEB7,
0x9E2E, 0xA62D, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xF7FD, 0xDF39, 0xCEF5, 0xCEF5, 0xC6D5, 0xCEF4, 0xC6D5, 0xCEF4, 0xC6D5, 0xCEF4, 0xC6D5, 0xCEF4, 0xC6D5, 0xD736,
0xE75C, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF79C,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xF79D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xBE55, 0xA64D, 0x9E2E, 0xA610, 0xCF55,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF1A, 0xA66E, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA62E, 0xB6B1, 0xFFDE, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA66E, 0x9E2E, 0xA64D, 0x9E2E,
0xE75B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xBE34, 0x9E2E, 0xA62D, 0xA5F0, 0xD775, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD776, 0xAE10, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xB613, 0xF7FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE70, 0xA62E, 0x9E2E, 0xA62D, 0xDF1A, 0xFFFE, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFC, 0xB655, 0xA64D, 0x9E2E, 0xA610, 0xCF55, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xAE6F, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA60F, 0xC735, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE6F, 0x9E2E, 0xA64D, 0x9E2E, 0xE75A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCEB6,
0x9E2E, 0xA62D, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE73A,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xB692, 0xC6F3, 0xC6D4, 0xC6D3, 0xC6D4, 0xC6D3, 0xC6D4, 0xC6D3, 0xC6D4, 0xC6D3, 0xBED3, 0xAE50,
0x9E2E, 0xA62D, 0xA62E, 0xAE6E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xEF7D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF1A, 0xA64D, 0x9E2E, 0xA64E, 0xA64E,
0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xB634, 0xA64D, 0x9E2E, 0xA62E, 0xB6B1,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7D, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xDF3A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCEB8, 0xA64D, 0x9E2E, 0xA64E, 0xA64F,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDE, 0xA62E, 0xA62D, 0x9E2E, 0xA62D, 0xBE75, 0xFFFC, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xAE0F, 0x9E2E, 0xA62D, 0xA5F0, 0xDF96, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB690, 0xA62E, 0x9E2E, 0xA62D, 0xAE12, 0xF7FA, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7B8, 0xAE11, 0x9E2E, 0xA62D, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC713, 0xA60F, 0xA64D, 0x9E2E, 0xA62F, 0xB6D2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFD, 0xD6F9, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xB633, 0xF7FC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDFB7, 0xA5F1, 0xA64D, 0x9E2E, 0xA62E, 0xAE70, 0xF7BD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFE, 0xE75C, 0xA66E, 0x9E2E, 0xA64D, 0x9E2E, 0xAE11, 0xE7F9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xEFFA, 0xB612, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xAE31, 0xCF34, 0xEF7C, 0xF7DC, 0xEFBB, 0xDF38, 0xAE90, 0xA62E, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xE75B, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xF7FC, 0xC675, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xA610, 0xC712, 0xE75B, 0xF7DC, 0xEFBC, 0xE75A, 0xBED2, 0xA62E, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xCEB7, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE75C, 0xA64E, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA60F, 0xC734, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xAE6F, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA62E, 0xB6B1, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF97, 0xAE11, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xD6D8, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFFA, 0xB633, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xBE55, 0xF7FB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF9E, 0xB6B0, 0xA62F, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xB652, 0xDF98, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xC713, 0xA610, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xAE30, 0xC735, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7B, 0xBEF3, 0xAE50, 0x9E2E, 0xA62D, 0xA62E, 0xA64E, 0xBE94, 0xDF77, 0xFFDF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xCF15, 0xB651, 0x9E2E, 0xA62D, 0xA62E, 0xA64D, 0xB672, 0xCF35, 0xF7BE, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xF7BE, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xF7BE, 0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
};

const unsigned short elrs_rxwifi[0xE10] PROGMEM = {
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xF7DE, 0xF7DD, 0xF7DE, 0xF7DD, 0xF7DE, 0xF7DD, 0xF7DE,
0xF7DE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBC, 0xE77A, 0xE77A, 0xE779, 0xE77A, 0xE779, 0xE77A, 0xE779,
0xE77A, 0xE779, 0xDF78, 0xC6B4, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xBE94, 0xE798,
0xE77A, 0xE779, 0xE77A, 0xE779, 0xE77A, 0xE779, 0xE77A, 0xE779, 0xE75A, 0xF7BB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFD, 0xD6F9, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xD6F8, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FC, 0xADF2,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xADF2, 0xFFFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xAE13, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xB612, 0xF7FC,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7B, 0xAE6F, 0xA62E, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xAE6E, 0xE75B, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFD, 0xD71A,
0xB6B1, 0xAE71, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xAE70, 0xB6B2, 0xDF19, 0xF7FD, 0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF,
0xFFFE, 0xFFDF, 0xFFFE, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA64E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xB6D1, 0xA62F, 0xA64E, 0x9E2E, 0xA62E, 0xAE91, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDFB8, 0xAE10,
0xA62E, 0xA64D, 0xA5F1, 0xDFB7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1, 0xA62E, 0xA64D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1,
0xA62E, 0xA64D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFC, 0xADBB, 0xADBA,
0x9D5A, 0xADBB, 0xB5FB, 0xCE9D, 0xEF5E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB65F, 0x4B13, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x5377, 0x8CD7, 0xBE5E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1, 0xA62E, 0xA64D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7459, 0x4B13, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x5B74, 0x9D7C, 0xF79F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0x955C, 0x4B13, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5B73, 0xA5DE,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1,
0xA62E, 0xA64D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DF, 0xB5B8, 0x94F9, 0x9539,
0x9D3A, 0x94F9, 0x7C79, 0x63B5, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x7C35, 0xE77F, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xF7BF, 0xC65B, 0x7CBA, 0x4B14, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5377, 0xE71D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1, 0xA62E, 0xA64D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF79E,
0x8CFB, 0x5313, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x5B53, 0xCEFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF1D, 0x63D7, 0x4315,
0x4315, 0x4315, 0x4315, 0x4B77, 0xEF5D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1,
0xA62E, 0xA64D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCE9E, 0xADB9, 0x9D5A, 0xA57A,
0xADBA, 0xCE7D, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DF, 0x8C95, 0x4315, 0x4B15, 0x4315, 0x4B15,
0x6BD3, 0xF7DF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xADB9, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BD8,
0xB5D9, 0xEF9F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x84DA, 0x4314, 0x4315, 0x4315, 0x4B13, 0x8D3C, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1, 0xA62E, 0xA64D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xF7FF, 0x73F3, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x5355, 0xAD99, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DF, 0x8454, 0x4315, 0x4B15, 0x4315, 0x4B15, 0xDEFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x94D7,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x6419, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xF79F, 0x5396, 0x4315, 0x4315, 0x4315, 0x8495, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1,
0xA62E, 0xA64D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xA5BB, 0x9518, 0x951A, 0x9519,
0x7C79, 0x5B75, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x6393, 0xDF5F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB63F, 0x5312,
0x4315, 0x4B15, 0x5312, 0xD71F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD71F, 0x8CD7,
0x4B35, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BB7, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0x73F4, 0x4315, 0x4315, 0x4B13, 0x8D1C,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1, 0xA62E, 0xA64D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD71F, 0x6BD3, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x8C95, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC63A, 0x4315, 0x4B15, 0x4315, 0x5356, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0x7459, 0x4314, 0x4315, 0x4315, 0x5312, 0xBE7F,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0x5376, 0x4315, 0x4315, 0x4315, 0xDEFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7F9, 0xADF1,
0xA62E, 0xA64D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE79F, 0x6B92, 0x4315, 0x4B15, 0x42F4, 0x63F7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0x7CBA, 0x4B13, 0x4315, 0x4B15, 0xAD78, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFF9, 0xA5F1, 0xA64E, 0x9E2E, 0xAE11, 0xE7D9,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF7F, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xD6BC, 0x4315, 0x4315, 0x4315, 0x4315, 0xDEFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE1E, 0x4B13, 0x4315, 0x4315,
0x8CB5, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFE, 0xDF39, 0xC6F4, 0xC6D3, 0xBED2, 0xA62E, 0xA62E, 0xA64D, 0xA62F, 0xBED1, 0xC6D4, 0xC6F4, 0xDF19, 0xFFFD,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD69B, 0x5BD8, 0x4B14, 0x4315, 0x5BB7, 0xCE7B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0x6C19, 0x4B14, 0x4315, 0x4B15, 0x9D17, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEFF, 0x5311, 0x4315, 0x4B15, 0x6BD3, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB6B0, 0xA62F,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA62E, 0xAE90, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xBE7F, 0x4B13, 0x4315, 0x4315, 0x4315, 0x4315, 0x5312, 0xBE9F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB65F, 0x4B12, 0x4315, 0x4315,
0x6BD3, 0xF7FF, 0xFFFF, 0xFFFF, 0xEFDF, 0x5311, 0x4315, 0x4315, 0x5B51, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF19, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xD6F9, 0xFFFD, 0xFFFF, 0xFFDF, 0x5376, 0x4B14, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x42F4, 0x63F7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE79F, 0x5310, 0x4315, 0x4B15, 0x5311, 0xEFDF, 0xFFFF, 0xFFFF,
0xF7FF, 0x5B30, 0x4315, 0x4B15, 0x52F1, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC6B7, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xC696, 0xFFFD, 0xFFFF, 0xCE9C, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0xF7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0x6392, 0x4315, 0x4315, 0x5311, 0xD71F, 0xFFFF, 0xFFFF, 0xF7FF, 0x5B72, 0x4315, 0x4315,
0x5311, 0xE7BF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFD, 0xCE96, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xC676, 0xFFFC, 0xFFFF, 0xC63A, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0x4315, 0x4B15, 0xF79F, 0xFFFF, 0xFFFF, 0xFFFF,
0xF7FF, 0x8454, 0x4315, 0x4B15, 0x4AF2, 0xCEDF, 0xFFFF, 0xFFFF, 0xF7FF, 0x6371, 0x4315, 0x4B15, 0x52F1, 0xEFDF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC697, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xC696, 0xFFFD, 0xFFFF, 0xD6DC,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x5BD8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x7C35, 0x4315, 0x4315,
0x5312, 0xC6BF, 0xFFFF, 0xFFFF, 0xF7FF, 0x5331, 0x4315, 0x4315, 0x5310, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCE96, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xC676, 0xFFFC, 0xFFFF, 0xFFFF, 0x63D8, 0x4B14, 0x4315, 0x4B15,
0x4315, 0x4B15, 0x4B12, 0xBE5F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0x8434, 0x4315, 0x4B15, 0x4B12, 0xCEFF, 0xFFFF, 0xFFFF,
0xF7FF, 0x6BB2, 0x4315, 0x4B15, 0x73F3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC697, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xC696, 0xFFFD, 0xFFFF, 0xFFFF, 0xDF5F, 0x6393, 0x4315, 0x4315, 0x4315, 0x4316, 0xB5D9, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x8496, 0x4315, 0x4315, 0x5331, 0xE79F, 0xFFFF, 0xFFFF, 0xFFFF, 0xCE9C, 0x63D6, 0x6418,
0xE71D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFD, 0xCE96, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xC676, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xB61D, 0xA559, 0xB5BA, 0xE75F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xE71C, 0x5B97, 0x5355, 0xB5D9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC697, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xC696, 0xFFFD, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCE96, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xC676, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC697, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xC696, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFD, 0xCE96, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xC676, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC697, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xC696, 0xFFFD, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCE96, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xC676, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC697, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xC696, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFD, 0xCE96, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xC676, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC697, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xC696, 0xFFFD, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xCE96, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D,
0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xC676, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xD6F9, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xA64E, 0x9E2E,
0xA64E, 0x9E2E, 0xA64E, 0x9E2E, 0xD6F8, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFDE, 0xA64F, 0xA64E, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xA64D, 0xA62E, 0xAE6E,
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FB, 0xC6B6,
0xB690, 0xAE71, 0xAE90, 0xAE70, 0xAE90, 0xAE70, 0xAE90, 0xAE70, 0xAE90, 0xAE71, 0xC6B6, 0xEFFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
};

const unsigned short elrs_backpack[0xE10] PROGMEM = {
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDD, 0xF7BD, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xDF97, 0xBE94, 0xA64E, 0x9E2E, 0xA64D, 0x9E2E, 0xB671, 0xC715, 0xFFDE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFFA, 0xBE53, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xAE11, 0xD775, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF9E, 0xAE6F, 0x9E2E, 0xA64D, 0x9E2E, 0xA64E, 0xAE90, 0xB6B0, 0xA650,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xD6F8, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xF7FC, 0xBE33, 0x9E2E, 0xA62D, 0xA610, 0xCF34, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF98, 0xB652, 0x9E2E, 0xA62D,
0xA5F1, 0xE7B7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD775, 0xA610,
0xA64E, 0x9E2E, 0xE75B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xB690, 0x9E2F, 0xA62E, 0xAE90, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB6B1, 0xA62E, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D, 0xEF9D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA66E, 0x9E2E, 0xA610, 0xD797, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFC, 0xAE13, 0xA64D, 0x9E2E, 0xE73A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0x9E2E, 0xA62D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFC, 0xBE34,
0x9E2E, 0xA62D, 0xDEF9, 0xFFFD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xF7BE, 0xD757, 0xD738, 0xD737, 0xD738, 0xD737, 0xD738, 0xD737, 0xD738, 0xD737, 0xD738, 0xD737, 0xCF18, 0xA64D, 0x9E2E,
0xA62F, 0xC714, 0xD737, 0xD738, 0xD737, 0xD738, 0xD737, 0xD738, 0xD737, 0xD738, 0xD755, 0xAE31, 0xA64D, 0x9E2E, 0xC6B4, 0xCF37,
0xD737, 0xD738, 0xD737, 0xD738, 0xD737, 0xD738, 0xD737, 0xD738, 0xD737, 0xD738, 0xE75B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFFA, 0xADF1, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xA60F, 0xC712, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD796, 0xA610, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA62E, 0xAE70, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD776, 0xAE10, 0x9E2E, 0xA62D, 0xAE50, 0xAE6F, 0xAE70, 0xAE6F, 0xAE70, 0xAE6F, 0xAE70, 0xAE6F,
0xAE70, 0xAE6F, 0xAE70, 0xAE6F, 0xAE70, 0xAE6F, 0xAE50, 0xB6B1, 0xBEB4, 0xC6F4, 0xC6D5, 0xC6D4, 0xBEB2, 0xB670, 0xAE70, 0xAE6F,
0xAE70, 0xAE6F, 0xAE70, 0xAE6F, 0xAE70, 0xAE6F, 0xAE70, 0xAE6F, 0xAE70, 0xAE6F, 0xAE70, 0xAE6F, 0xA62E, 0xA62D, 0xA62E, 0xAE6F,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xD796, 0xA610, 0xA64D, 0x9E2E, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xF7BF, 0xF7DF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE8F, 0x9E2F, 0xA62E, 0xAE70, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD776, 0xAE10, 0x9E2E, 0xA62D,
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE75F, 0xBDF9, 0x7C99, 0x63B5,
0x4315, 0x4B15, 0x42F5, 0x4B15, 0x5355, 0x6C38, 0x9D18, 0xCE9E, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE70, 0xA62E, 0xA62E, 0xAE6F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD796, 0xA610, 0xA64D, 0x9E2E, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC67E, 0x7C56, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x5B74, 0x955C, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xAE8F, 0x9E2F, 0xA62E, 0xAE70, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD776, 0xAE10, 0x9E2E, 0xA62D, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xCEDF, 0x7C34, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
0x42F5, 0x4B15, 0x5334, 0x9D7C, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE70, 0xA62E, 0xA62E, 0xAE6F,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xD796, 0xA610, 0xA64D, 0x9E2E, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xA578, 0x4B35, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x5354, 0x6C59, 0x9539, 0x9D7B, 0xADBB, 0x9D5A, 0x84D9, 0x63D6, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x6BD4, 0xCEFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE8F, 0x9E2F, 0xA62E, 0xAE70, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD776, 0xAE10, 0x9E2E, 0xA62D,
0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFBE, 0x7459, 0x4B13, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x8497, 0xCE9E, 0xFFDF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE75F, 0xB5B9, 0x5BB7, 0x4B14, 0x42F5, 0x4B15, 0x42F5, 0x4B35, 0xC63B, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE70, 0xA62E, 0xA62E, 0xAE6F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD796, 0xA610, 0xA64D, 0x9E2E, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF,
0xEFBF, 0x6BF4, 0x4315, 0x4315, 0x4315, 0x4315, 0x8475, 0xD73F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5FA, 0x5376, 0x4315, 0x4315, 0x4315, 0x5332, 0xA5FE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xAE8F, 0x9E2F, 0xA62E, 0xAE70, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD776, 0xAE10, 0x9E2E, 0xA62D, 0xF79E, 0xFFFF, 0xFFFF, 0xFFBF, 0x6C18, 0x4B14, 0x42F5, 0x4B15,
0x5333, 0xB61E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xE77F, 0x8475, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0xBE1A, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE6F, 0xA62E, 0xA60E, 0xAE6F,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xDFB7, 0xA5F1, 0xA64D, 0x9E2E, 0xEF7C, 0xFFFF, 0xFFFF, 0xA578, 0x4315, 0x4315, 0x4B15, 0x5377, 0xDEFC, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xEF9F, 0xBE3B, 0x9D5A, 0x84D9, 0x84B8, 0x8CFA, 0xADDA, 0xCEDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0x8D1B, 0x4B14, 0x4315, 0x4315, 0x5B51, 0xE7BF, 0xFFFF, 0xFFFF, 0xA66E, 0x9E2E, 0xA62E, 0xAE90, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D,
0xCEB8, 0xFFFD, 0xFFFF, 0xA557, 0x42F5, 0x4B15, 0x5B73, 0xDF3F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEBF, 0x8476, 0x4315, 0x4B15,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x5B74, 0xA59C, 0xF79F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA537, 0x42F5, 0x4B15,
0x5311, 0xEFBF, 0xFFFF, 0xFFBE, 0xA62E, 0xA62D, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xB612, 0xEFFB, 0xFFFF, 0xF79F,
0x84B9, 0x7458, 0xE71D, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBF, 0x8496, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x5B73, 0xB63F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA59C, 0x6C16, 0xBE3A, 0xFFFF, 0xFFFD, 0xCEB8,
0xA64D, 0x9E2E, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D, 0xA60F, 0xBED2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xE6FC, 0x5377, 0x4B14, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B35, 0x5377, 0x4B14, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
0x42F5, 0x4B15, 0x94F7, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDFB8, 0xAE10, 0x9E2E, 0xA62D, 0xA60F, 0xC6F2,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xCEB6, 0xF7FD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCEFF, 0x5333, 0x4315, 0x4315,
0x4315, 0x4315, 0x7C56, 0xB63E, 0xF79E, 0xFFFF, 0xFFFF, 0xFFDF, 0xDF1F, 0x9D38, 0x5396, 0x4315, 0x4315, 0x4315, 0x4B13, 0x7CBA,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE75B, 0xA64E, 0x9E2E, 0xA64D, 0x9E2E, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xA60E, 0xB68F, 0xE73B, 0xFFFE, 0xFFFF, 0xF79E, 0x5376, 0x4B14, 0x42F5, 0x4B15, 0x6394, 0xC69F, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBF, 0x9D16, 0x4315, 0x4B15, 0x42F5, 0x4B15, 0xA557, 0xFFFF, 0xFFFF, 0xF7BD,
0xBEF3, 0xA60F, 0x9E2E, 0xA62D, 0xA62E, 0xA62D, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xBED1, 0xA60F, 0xA64D, 0x9E2E,
0xA64E, 0xA66F, 0xE77D, 0xBE59, 0x4315, 0x4315, 0x4B14, 0x6419, 0xF7BE, 0xF7BF, 0xDF58, 0xD738, 0xD737, 0xD738, 0xDF58, 0xD738,
0xD737, 0xD738, 0xEF7C, 0xFFFE, 0xB65F, 0x4B13, 0x4315, 0x4315, 0x6BB2, 0xF7FF, 0xB6B0, 0x9E2F, 0xA64D, 0x9E2E, 0xA62E, 0xA66F,
0xBED1, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D, 0xF7FD, 0xDF19, 0xA64F, 0xA62D, 0x9E2E, 0xA62D, 0xCEB8, 0xEF9B,
0x4B56, 0x4B14, 0x7414, 0xEFBF, 0xD776, 0xAE10, 0xA60F, 0xB6B0, 0xEF9D, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF55, 0xAE30, 0xA60E, 0xB68F,
0xF7BF, 0xC65A, 0x4315, 0x4B15, 0x9D17, 0xF7DF, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xC676, 0xF7FB, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xF7FC, 0xD6F9, 0xAE8F, 0x9E2F, 0xAE11, 0xE7D9, 0xEF9F, 0xCEBD, 0xFFFD, 0xD6FA,
0xA64E, 0x9E2E, 0xC6B6, 0xF7FD, 0xADFD, 0x63D5, 0x5B95, 0x7C9A, 0xE77D, 0xEF9E, 0xA64E, 0x9E2E, 0xB653, 0xEFFB, 0xE75E, 0xD6DD,
0xFFFD, 0xC6B7, 0xA64E, 0xA64F, 0xC6B5, 0xE7BA, 0xFFFF, 0xFFFF, 0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D,
0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xC714, 0xAE2F, 0xC6D5, 0xD736, 0xDF59, 0xDF37, 0xD718, 0xDF57, 0xF7FF, 0xA536,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x5B32, 0xDF5F, 0xEFBB, 0xDF18, 0xD718, 0xDF57, 0xD737, 0xD716, 0xB671, 0xB670, 0xEF7C, 0xFFFE,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xDFB7, 0xA5F1, 0xA64E, 0xA64E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD71F, 0x4B12, 0x4315, 0x4315, 0x4315, 0x4315,
0x4B14, 0x747A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xA610, 0xA62E, 0xB6B1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF97, 0xAE10,
0xA62E, 0xA64E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA5DE, 0x5312, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4315, 0x5355, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xCF34, 0xAE0F, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDFB7, 0xA5F1, 0xA64E, 0xA64E, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xAE1E, 0x4B13, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5377, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xA610,
0xA62E, 0xB6B1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D,
0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF97, 0xAE10, 0xA62E, 0xA64E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF7F, 0x5B51,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4AF3, 0x951B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xAE0F, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xDFB7, 0xA5F1, 0xA64E, 0xA64E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBE1A, 0x4B35, 0x4315, 0x4315, 0x4315,
0x73F3, 0xEFBF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xA610, 0xA62E, 0xB6B1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF97, 0xAE10,
0xA62E, 0xA64E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6FF, 0x9D17, 0x84B8, 0xADDB, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xCF34, 0xAE0F, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDFB7, 0xA5F1, 0xA64E, 0xA64E, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xA610,
0xA62E, 0xB6B1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D,
0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF97, 0xAE10, 0xA62E, 0xA64E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xAE0F, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xDFB7, 0xA5F1, 0xA64E, 0xA64E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xA610, 0xA62E, 0xB6B1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF97, 0xAE10,
0xA62E, 0xA64E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xCF34, 0xAE0F, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDFB7, 0xA5F1, 0xA64E, 0xA64E, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xA610,
0xA62E, 0xB6B1, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D,
0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF97, 0xAE10, 0xA62E, 0xA64E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xCF34, 0xAE0F, 0xA60F, 0xBED1, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xE7B8, 0xB653, 0xB690, 0xAE91, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD755, 0xB652, 0xB671, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D,
0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D,
0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6F3, 0xA60F, 0xA60F, 0xC6F2, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xA5F1, 0xA64D, 0x9E2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xC713, 0xA60F, 0xA62F, 0xBEF3, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xADF0, 0x9E2E, 0xA62D, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xBEF3, 0xA60F, 0xA60F, 0xC6F2,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xEFF9, 0xA5F1, 0xA64D, 0x9E2E, 0xE75A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xAE8F, 0x9E2E, 0xA60F, 0xC734, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xBE54, 0x9E2E, 0xA62D,
0xA60F, 0xB6B0, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2,
0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2, 0xBEB3, 0xBEB2,
0xBEB3, 0xBEB2, 0xB6B2, 0xAE4F, 0x9E2E, 0xA62D, 0xA5F1, 0xE7D8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF9D, 0xA66E, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xD6D8, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFDB, 0xC695, 0xA62E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xB632, 0xDF76, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xEFBD, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC,
0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC,
0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xEFBC, 0xF7BC, 0xF7DD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
};

const unsigned short elrs_vrxwifi[0xE10] PROGMEM = {
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xDEFD, 0xBE3C, 0x9D7A, 0x953A, 0x84D9, 0x8CF9, 0x953A, 0xA599, 0xBE5D, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFBE, 0xB5FD, 0x8497, 0x4B36, 0x4B15, 0x42F5, 0x4B15,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4315, 0x5376, 0x8CB7, 0xBE5D, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xEF5E, 0x8D1B, 0x5334, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5B74, 0x955C, 0xF7BE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BE, 0x8D1C, 0x5333,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x5354, 0xA59C, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC69F, 0x5B74, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x4B34, 0x5BB7, 0x7457, 0x7C79, 0x7C98, 0x6C38, 0x5BB7, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x6BD4, 0xCEFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xF7FF, 0xA537, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x63D7, 0xA559, 0xD6DE, 0xFFDF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFBE, 0xC69E, 0x9D38, 0x5377, 0x4B14, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
0xB5B9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0x7479, 0x4314,
0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x7459, 0xD6BB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC65B, 0x6418, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B13, 0x851C, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBF, 0x7C34, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x63B7,
0xD69B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xC63A, 0x5376, 0x4B14, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x8CB6, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0x7459, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x9D17, 0xEFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xEF9F, 0x8476, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B13, 0x7CDB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5B8,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x4B13, 0xAE1E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6DE, 0xBDFA,
0x9D3A, 0x9519, 0x94F9, 0x9D5A, 0xBDFB, 0xDEFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x955C, 0x4B13,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0xCE5B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x94F7, 0x4315, 0x4315, 0x4315, 0x4316,
0xCE7B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6DC, 0x84DA, 0x5355, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0x5B94, 0x8D1B, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5D9, 0x4315, 0x4315, 0x4315, 0x4315,
0xB5B8, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEDB, 0x4315, 0x4B15, 0x5312, 0xBE5E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xE6FC, 0x6C39, 0x4B14, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15,
0x4B14, 0x84BA, 0xE75E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xA5BD, 0x5312, 0x4315, 0x5355, 0xE75E, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF3F, 0xB5FB, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0x9D7C, 0x4B13, 0x4315, 0x4315,
0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5353, 0xA5FE,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6DD, 0xBE1A, 0xE77F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFBF, 0x8474, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B35,
0x6BF7, 0x7C58, 0x7458, 0x63D6, 0x4B15, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x94D6, 0xF7FF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xF7BF, 0x6418, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x5334, 0x84DB, 0xD6DC, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xF7DF, 0xBE3B, 0x7479, 0x4314, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B14, 0x747A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0x8434, 0x42F5, 0x4B15,
0x42F5, 0x4B15, 0x4B14, 0x7459, 0xE73D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFC,
0x63F8, 0x4B14, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x94D6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC69F, 0x4B12, 0x4315, 0x4315, 0x4315, 0x4316, 0xBDF9, 0xF7FF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xA558, 0x4315, 0x4315,
0x4315, 0x4315, 0x5311, 0xD75F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC6BF, 0x5311, 0x42F5, 0x4B15, 0x5312, 0xBE5E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x9D9D, 0x5312, 0x42F5, 0x4B15, 0x5311, 0xE77F,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xF7FF, 0x94F7, 0x4B35, 0x4B76, 0xCE7B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE73E, 0xEF5D, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB5FA, 0x4B55, 0x4315, 0xAD98, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF79F, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0xB598, 0x5356, 0x4B14, 0x4315, 0x5B96, 0xBE1A, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDF, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0x84FB, 0x4B14, 0x4315, 0x4315, 0x4315, 0x4315, 0x4B13, 0x9D9D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD6BB, 0x42F5, 0x4B15,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B35, 0xEF5E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x94F7, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315,
0x4315, 0x4315, 0xB5D9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FF, 0x8C95, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x9D37, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0x9D17, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0x4315, 0xB5D9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDEFC, 0x42F5, 0x4B15,
0x42F5, 0x4B15, 0x42F5, 0x4B15, 0x42F5, 0x4B35, 0xEF7E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x955C, 0x4B13, 0x4315, 0x4315, 0x4315, 0x4315,
0x5332, 0xADFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC619, 0x5BB7, 0x4B14, 0x4314, 0x6C18, 0xCE7B, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF9D,
0xCF14, 0xB672, 0xA64E, 0x9E2E, 0xA64F, 0xAE70, 0xBEB3, 0xC6F4, 0xD717, 0xD738, 0xDF59, 0xDF7A, 0xE77A, 0xDF7A, 0xE77A, 0xDF7A,
0xE779, 0xDF39, 0xD737, 0xCEF6, 0xBED3, 0xB692, 0xAE6F, 0x9E2E, 0xA62E, 0xA64F, 0xBE93, 0xD757, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0xBED2, 0xA62E, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xAE31, 0xD755, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD796, 0xA611, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xBE74, 0xEFFB, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFDE, 0xFFDD, 0xEFBA, 0xCEB6,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xA62E, 0xAE4E, 0xDF3A, 0xF7BB, 0xF7BE, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF3B, 0xBED2, 0xAE50, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xAE50, 0xBED2, 0xE73A, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF9C,
0xAE6F, 0xA62E, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xA62E, 0xAE6F, 0xEF7D, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D8, 0xA611, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xAE11, 0xDFD9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xA64F, 0xA62E, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xA62E, 0xAE6F, 0xFFDF, 0xFFFF,
0xFFFE, 0xD71A, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xDF3A, 0xFFFE, 0xFFFD, 0xBE54, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xAE11, 0xD775, 0xEF7C, 0xF7BC, 0xF7DE, 0xFFFE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE,
0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE,
0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDF, 0xFFDE, 0xFFDE, 0xFFDE, 0xF7BD, 0xEF9B,
0xD756, 0xAE30, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xBE55, 0xFFFC, 0xF7FB, 0xADF2, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xE73A, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF3B, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xAE12, 0xF7FC, 0xEFFA, 0xADF1, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xF79E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xA5F1, 0xF7F9,
0xEFF9, 0xA5F1, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xFFDE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7DF, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xADF1, 0xE7F9, 0xE7D9, 0xADF0, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFDE, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xA5F1, 0xEFD8, 0xEFF9, 0xA5F1, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xFFDE, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xF7DF, 0xCF36, 0xBEB4, 0xC6B4, 0xCF16, 0xFFDE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BF, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xADF1, 0xE7F9, 0xEFF9, 0xADF1, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xF79E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF97, 0xAE11,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xAE11, 0xDF97, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7BD, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xA5F1, 0xEFF9,
0xF7FB, 0xA5F2, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xE75B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE77C, 0xA64E, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64E, 0x9E4E, 0xEF7C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xDF3B, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xADF1, 0xF7FB, 0xFFFC, 0xB633, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xBE55, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFFA, 0xB632, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xAE12, 0xEFF9,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xF7FC, 0xC675, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xB614, 0xFFFC, 0xFFFD, 0xCEB8, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA62E, 0xA66F,
0xCED5, 0xD737, 0xEF9B, 0xF7DD, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEFDB, 0xD718,
0xAE6E, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64E, 0xA66F, 0xD718, 0xEFBB, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FD, 0xE77C, 0xD757, 0xC6D6, 0xAE6F, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xCED7, 0xFFFE, 0xFFFF, 0xF79D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0xA62E, 0xAE6E, 0xAE71, 0xB691, 0xB691, 0xB691, 0xB691, 0xB691, 0xAE6F, 0xA64E, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0xA62E, 0xAE6F, 0xA64F, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xA62E, 0xAE6F, 0xB671, 0xB691, 0xB691, 0xB691,
0xB691, 0xB670, 0xA64F, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xA62E, 0xA64D, 0xF79D, 0xFFFF,
0xFFFF, 0xFFFF, 0xC713, 0xA610, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xAE11, 0xDFB8, 0xDFB7, 0xA5F1,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA62F, 0xC714, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xC695,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA64E, 0xDF3B, 0xFFFE, 0xFFFE, 0xE73A, 0xA62E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xC676, 0xFFFC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD776, 0xAE32, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xB653, 0xE7B9, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE7D9, 0xB653, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xAE51, 0xD777,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE799, 0xD6F6, 0xB6B1, 0xAE4F, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xB632, 0xE7B7, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD776, 0xAE10, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xAE30, 0xBED1, 0xCED7, 0xE799, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFE, 0xE75C, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xD6D8, 0xFFFE, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xF7FB, 0xAE13, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E, 0xA64D, 0x9E2E,
0xA62E, 0xAE70, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xD756, 0xAE30, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D,
0x9E2E, 0xA62D, 0xA60F, 0xC6F2, 0xFFDF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xEF9C,
0xAE6F, 0xA62E, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0x9E2E, 0xA62D, 0xBE74, 0xEFFA, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xDF77, 0xC6B5, 0xAE8F, 0xA64E, 0xA64D, 0xA64E, 0xA62D, 0xA64E, 0xB671, 0xC734, 0xF79C, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFD, 0xDF5A, 0xC6D2, 0xAE71,
0xA64D, 0xA64E, 0xA64D, 0xA64E, 0xAE4F, 0xB6B1, 0xD6F7, 0xE7DA, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xF7FE, 0xF7BD, 0xF7FF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFBE, 0xF7DE, 0xFFDE,
0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
};