const uint8_t Pragma_Sans37pt7bBitmaps[] PROGMEM = {
  0x00, 0xFE, 0x40, 0xB4, 0x24, 0x92, 0xBF, 0x28, 0xAF, 0xD2, 0x51, 0x40,
  0x10, 0x20, 0x43, 0xE9, 0x52, 0x34, 0x3C, 0x1C, 0x2C, 0x5C, 0xB7, 0xC2,
  0x04, 0x08, 0x71, 0x22, 0x48, 0xA2, 0x30, 0x74, 0x02, 0xE1, 0x44, 0x51,
  0x24, 0x50, 0xE0, 0x38, 0x24, 0x44, 0x28, 0x30, 0x50, 0x89, 0x86, 0x86,
  0x79, 0xC0, 0x12, 0x24, 0x48, 0x88, 0x88, 0x88, 0x84, 0x42, 0x21, 0x84,
  0x42, 0x21, 0x11, 0x11, 0x11, 0x12, 0x24, 0x48, 0x25, 0x5C, 0xEA, 0x90,
  0x10, 0x23, 0xF8, 0x81, 0x02, 0x00, 0xC0, 0xFC, 0x80, 0x02, 0x04, 0x10,
  0x20, 0x41, 0x02, 0x04, 0x10, 0x20, 0x41, 0x02, 0x04, 0x10, 0x20, 0x80,
  0x3C, 0x42, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x42, 0x3C, 0x2E, 0x92,
  0x49, 0x24, 0x7B, 0x30, 0x41, 0x08, 0x63, 0x18, 0x43, 0xF0, 0x7A, 0x30,
  0x43, 0x30, 0x30, 0x61, 0x8D, 0xE0, 0x04, 0x18, 0x70, 0xA2, 0x48, 0xBF,
  0x82, 0x04, 0x08, 0x7A, 0x08, 0x2E, 0xCC, 0x10, 0x41, 0x89, 0xE0, 0x3C,
  0xC3, 0x06, 0xEE, 0x78, 0x70, 0xE1, 0x64, 0x78, 0xFC, 0x10, 0x82, 0x10,
  0x43, 0x08, 0x21, 0x00, 0x7D, 0x06, 0x0E, 0x33, 0x98, 0xE0, 0xC1, 0xC6,
  0xF8, 0x79, 0x8A, 0x0C, 0x1C, 0x6F, 0x40, 0x83, 0xC4, 0xF0, 0x82, 0x83,
  0x04, 0x23, 0x10, 0x40, 0x81, 0x81, 0xFC, 0x00, 0x3F, 0x81, 0x03, 0x02,
  0x08, 0x46, 0x20, 0xF0, 0xC2, 0x11, 0x31, 0x00, 0x02, 0x00, 0x1F, 0x83,
  0x0C, 0x40, 0x2C, 0xF3, 0x80, 0x98, 0xE9, 0x91, 0x99, 0x09, 0xD0, 0x94,
  0xFA, 0x30, 0x41, 0xF0, 0x08, 0x06, 0x05, 0x02, 0x83, 0x61, 0x10, 0x88,
  0xFE, 0x41, 0x20, 0x80, 0xFD, 0x0E, 0x0C, 0x18, 0x5F, 0xA1, 0xC1, 0x87,
  0xF8, 0x3E, 0x86, 0x04, 0x08, 0x10, 0x20, 0x40, 0x42, 0x7C, 0xF8, 0x86,
  0x82, 0x81, 0x81, 0x81, 0x81, 0x82, 0x86, 0xF8, 0xFE, 0x08, 0x20, 0xFA,
  0x08, 0x20, 0x83, 0xF0, 0xFE, 0x08, 0x20, 0xFA, 0x08, 0x20, 0x82, 0x00,
  0x3E, 0x40, 0x80, 0x80, 0x80, 0x87, 0x81, 0xC1, 0x41, 0x3E, 0x83, 0x06,
  0x0C, 0x1F, 0xF0, 0x60, 0xC1, 0x83, 0x04, 0xFF, 0xC0, 0x04, 0x10, 0x41,
  0x04, 0x10, 0x41, 0xCD, 0xE0, 0x8D, 0x32, 0x45, 0x0C, 0x1C, 0x2C, 0x48,
  0x89, 0x08, 0x82, 0x08, 0x20, 0x82, 0x08, 0x20, 0x83, 0xF0, 0xC0, 0xF0,
  0x3C, 0x0E, 0x85, 0xA1, 0x64, 0x99, 0x26, 0x49, 0x8C, 0x63, 0x10, 0x83,
  0x86, 0x8D, 0x19, 0x32, 0x62, 0xC5, 0x87, 0x04, 0x3C, 0x42, 0x81, 0x81,
  0x81, 0x81, 0x81, 0x81, 0x42, 0x3C, 0xFD, 0x0E, 0x0C, 0x18, 0x7F, 0xA0,
  0x40, 0x81, 0x00, 0x3C, 0x21, 0x20, 0x50, 0x28, 0x14, 0x0A, 0x05, 0x02,
  0x42, 0x1E, 0x02, 0x01, 0x00, 0x78, 0xFD, 0x0E, 0x0C, 0x18, 0x5F, 0xA1,
  0xC1, 0x83, 0x04, 0x7C, 0x82, 0x02, 0x03, 0x01, 0x80, 0x81, 0x86, 0xF8,
  0xFE, 0x20, 0x40, 0x81, 0x02, 0x04, 0x08, 0x10, 0x20, 0x83, 0x06, 0x0C,
  0x18, 0x30, 0x60, 0xC1, 0x44, 0x70, 0x41, 0x20, 0x90, 0x44, 0x42, 0x21,
  0x90, 0x50, 0x28, 0x0C, 0x04, 0x00, 0x41, 0x05, 0x0C, 0x24, 0x30, 0x88,
  0xA2, 0x24, 0x98, 0x92, 0x42, 0x45, 0x06, 0x14, 0x18, 0x60, 0x60, 0x80,
  0x41, 0x11, 0x8C, 0x82, 0x80, 0xC0, 0x60, 0x50, 0x64, 0x21, 0x20, 0xC0,
  0x82, 0x89, 0x11, 0x42, 0x82, 0x04, 0x08, 0x10, 0x20, 0xFE, 0x0C, 0x10,
  0x41, 0x02, 0x08, 0x20, 0xC1, 0xFC, 0xF8, 0x88, 0x88, 0x88, 0x88, 0x88,
  0x88, 0x88, 0x8F, 0x80, 0x81, 0x01, 0x02, 0x04, 0x04, 0x08, 0x10, 0x10,
  0x20, 0x40, 0x40, 0x81, 0x01, 0x02, 0xF1, 0x11, 0x11, 0x11, 0x11, 0x11,
  0x11, 0x11, 0x1F, 0x10, 0xA4, 0x91, 0xFF, 0xF0, 0xD4, 0x78, 0x10, 0x7F,
  0x86, 0x17, 0xC0, 0x82, 0x08, 0x2E, 0xCE, 0x18, 0x61, 0x8F, 0xE0, 0x7E,
  0x21, 0x08, 0x61, 0xE0, 0x04, 0x10, 0x5D, 0xCE, 0x18, 0x61, 0xC5, 0xF0,
  0x7B, 0x28, 0xFF, 0x82, 0x07, 0x80, 0x34, 0x4F, 0x44, 0x44, 0x44, 0x77,
  0x38, 0x61, 0x87, 0x17, 0xC1, 0x07, 0xE0, 0x84, 0x21, 0x6C, 0xC6, 0x31,
  0x8C, 0x40, 0xBF, 0x80, 0x20, 0x12, 0x49, 0x24, 0x9C, 0x84, 0x21, 0x3B,
  0x73, 0x14, 0x94, 0x40, 0xFF, 0xC0, 0xB3, 0x66, 0x62, 0x31, 0x18, 0x8C,
  0x46, 0x22, 0xB6, 0x63, 0x18, 0xC6, 0x20, 0x7B, 0x38, 0x61, 0x87, 0x37,
  0x80, 0xBB, 0x38, 0x61, 0x86, 0x3F, 0xA0, 0x82, 0x00, 0x77, 0x38, 0x61,
  0x87, 0x17, 0xC1, 0x04, 0x10, 0xF8, 0x88, 0x88, 0x80, 0x74, 0x20, 0xE1,
  0x0F, 0xC0, 0x4F, 0x44, 0x44, 0x44, 0x8C, 0x63, 0x18, 0xCD, 0xA0, 0x44,
  0x89, 0x11, 0x42, 0x85, 0x04, 0x00, 0x44, 0x52, 0x94, 0xA4, 0xAA, 0x32,
  0x8C, 0x63, 0x18, 0x44, 0x50, 0x60, 0x82, 0x84, 0x91, 0x00, 0x8C, 0x63,
  0x18, 0xCD, 0xA1, 0x0F, 0x80, 0xF8, 0xC4, 0x44, 0x63, 0xE0, 0x19, 0x88,
  0x42, 0x10, 0x84, 0x44, 0x10, 0x42, 0x10, 0x84, 0x30, 0xC0, 0xFF, 0xF0,
  0xC3, 0x08, 0x42, 0x10, 0x84, 0x10, 0x44, 0x42, 0x10, 0x84, 0x66, 0x00,
  0x68, 0x80 };

const GFXglyph Pragma_Sans37pt7bGlyphs[] PROGMEM = {
  {     0,   1,   1,   3,    0,    0 },   // 0x20 ' '
  {     1,   1,  10,   3,    1,   -9 },   // 0x21 '!'
  {     3,   3,   2,   5,    1,   -9 },   // 0x22 '"'
  {     4,   6,  10,   7,    0,   -9 },   // 0x23 '#'
  {    12,   7,  16,   8,    1,  -12 },   // 0x24 '$'
  {    26,  10,  10,  12,    1,   -9 },   // 0x25 '%'
  {    39,   8,  10,  10,    1,   -9 },   // 0x26 '&'
  {    49,   1,   2,   3,    1,   -9 },   // 0x27 '''
  {    50,   4,  18,   6,    1,  -13 },   // 0x28 '('
  {    59,   4,  18,   6,    1,  -13 },   // 0x29 ')'
  {    68,   5,   6,   7,    1,   -9 },   // 0x2A '*'
  {    72,   7,   6,   7,    0,   -7 },   // 0x2B '+'
  {    78,   1,   2,   3,    1,    0 },   // 0x2C ','
  {    79,   6,   1,   7,    0,   -4 },   // 0x2D '-'
  {    80,   1,   1,   3,    1,    0 },   // 0x2E '.'
  {    81,   7,  17,   8,    0,  -13 },   // 0x2F '/'
  {    96,   8,  10,  10,    1,   -9 },   // 0x30 '0'
  {   106,   3,  10,   5,    1,   -9 },   // 0x31 '1'
  {   110,   6,  10,   8,    1,   -9 },   // 0x32 '2'
  {   118,   6,  10,   8,    1,   -9 },   // 0x33 '3'
  {   126,   7,  10,   8,    0,   -9 },   // 0x34 '4'
  {   135,   6,  10,   8,    1,   -9 },   // 0x35 '5'
  {   143,   7,  10,   8,    0,   -9 },   // 0x36 '6'
  {   152,   6,  10,   7,    1,   -9 },   // 0x37 '7'
  {   160,   7,  10,   9,    1,   -9 },   // 0x38 '8'
  {   169,   7,  10,   9,    1,   -9 },   // 0x39 '9'
  {   178,   1,   7,   3,    1,   -6 },   // 0x3A ':'
  {   179,   1,   8,   3,    1,   -6 },   // 0x3B ';'
  {   180,   6,   8,   7,    0,   -8 },   // 0x3C '<'
  {   186,   6,   4,   7,    0,   -6 },   // 0x3D '='
  {   189,   6,   8,   7,    0,   -8 },   // 0x3E '>'
  {   195,   5,  10,   7,    1,   -9 },   // 0x3F '?'
  {   202,  12,  12,  14,    1,   -9 },   // 0x40 '@'
  {   220,   9,  10,   9,    0,   -9 },   // 0x41 'A'
  {   232,   7,  10,   9,    1,   -9 },   // 0x42 'B'
  {   241,   7,  10,   9,    1,   -9 },   // 0x43 'C'
  {   250,   8,  10,  10,    1,   -9 },   // 0x44 'D'
  {   260,   6,  10,   8,    1,   -9 },   // 0x45 'E'
  {   268,   6,  10,   8,    1,   -9 },   // 0x46 'F'
  {   276,   8,  10,  10,    1,   -9 },   // 0x47 'G'
  {   286,   7,  10,   9,    1,   -9 },   // 0x48 'H'
  {   295,   1,  10,   3,    1,   -9 },   // 0x49 'I'
  {   297,   6,  10,   7,    0,   -9 },   // 0x4A 'J'
  {   305,   7,  10,   8,    1,   -9 },   // 0x4B 'K'
  {   314,   6,  10,   8,    1,   -9 },   // 0x4C 'L'
  {   322,  10,  10,  12,    1,   -9 },   // 0x4D 'M'
  {   335,   7,  10,   9,    1,   -9 },   // 0x4E 'N'
  {   344,   8,  10,  10,    1,   -9 },   // 0x4F 'O'
  {   354,   7,  10,   9,    1,   -9 },   // 0x50 'P'
  {   363,   9,  13,  10,    1,   -9 },   // 0x51 'Q'
  {   378,   7,  10,   9,    1,   -9 },   // 0x52 'R'
  {   387,   7,  10,   8,    0,   -9 },   // 0x53 'S'
  {   396,   7,  10,   9,    1,   -9 },   // 0x54 'T'
  {   405,   7,  10,   9,    1,   -9 },   // 0x55 'U'
  {   414,   9,  10,   9,    0,   -9 },   // 0x56 'V'
  {   426,  14,  10,  14,    0,   -9 },   // 0x57 'W'
  {   444,   9,  10,   9,    0,   -9 },   // 0x58 'X'
  {   456,   7,  10,   9,    1,   -9 },   // 0x59 'Y'
  {   465,   7,  10,   9,    1,   -9 },   // 0x5A 'Z'
  {   474,   4,  18,   5,    1,  -13 },   // 0x5B '['
  {   483,   7,  17,   8,    0,  -13 },   // 0x5C '\'
  {   498,   4,  18,   5,    0,  -13 },   // 0x5D ']'
  {   507,   6,   4,   7,    0,   -9 },   // 0x5E '^'
  {   510,  12,   1,  13,    0,    1 },   // 0x5F '_'
  {   512,   2,   3,   3,    0,   -9 },   // 0x60 '`'
  {   513,   6,   7,   8,    1,   -6 },   // 0x61 'a'
  {   519,   6,  10,   8,    1,   -9 },   // 0x62 'b'
  {   527,   5,   7,   7,    1,   -6 },   // 0x63 'c'
  {   532,   6,  10,   8,    1,   -9 },   // 0x64 'd'
  {   540,   6,   7,   8,    1,   -6 },   // 0x65 'e'
  {   546,   4,  10,   4,    0,   -9 },   // 0x66 'f'
  {   551,   6,  10,   8,    1,   -6 },   // 0x67 'g'
  {   559,   5,  10,   7,    1,   -9 },   // 0x68 'h'
  {   566,   1,   9,   3,    1,   -8 },   // 0x69 'i'
  {   568,   3,  13,   4,    0,   -9 },   // 0x6A 'j'
  {   573,   5,  10,   7,    1,   -9 },   // 0x6B 'k'
  {   580,   1,  10,   3,    1,   -9 },   // 0x6C 'l'
  {   582,   9,   7,  11,    1,   -6 },   // 0x6D 'm'
  {   590,   5,   7,   7,    1,   -6 },   // 0x6E 'n'
  {   595,   6,   7,   8,    1,   -6 },   // 0x6F 'o'
  {   601,   6,  10,   8,    1,   -6 },   // 0x70 'p'
  {   609,   6,  10,   8,    1,   -6 },   // 0x71 'q'
  {   617,   4,   7,   5,    1,   -6 },   // 0x72 'r'
  {   621,   5,   7,   6,    1,   -6 },   // 0x73 's'
  {   626,   4,   8,   4,    0,   -7 },   // 0x74 't'
  {   630,   5,   7,   7,    1,   -6 },   // 0x75 'u'
  {   635,   7,   7,   7,    0,   -6 },   // 0x76 'v'
  {   642,  10,   7,  11,    0,   -6 },   // 0x77 'w'
  {   651,   7,   7,   7,    0,   -6 },   // 0x78 'x'
  {   658,   5,  10,   7,    1,   -6 },   // 0x79 'y'
  {   665,   5,   7,   7,    1,   -6 },   // 0x7A 'z'
  {   670,   5,  18,   7,    1,  -13 },   // 0x7B '{'
  {   682,   1,  12,   3,    1,  -10 },   // 0x7C '|'
  {   684,   5,  18,   7,    1,  -13 },   // 0x7D '}'
  {   696,   5,   2,   5,    0,   -9 } }; // 0x7E '~'

const GFXfont Pragma_Sans37pt7b PROGMEM = {
  (uint8_t  *)Pragma_Sans37pt7bBitmaps,
  (GFXglyph *)Pragma_Sans37pt7bGlyphs,
  0x20, 0x7E, 15 };

// Approx. 1370 bytes
