const uint8_t Pragma_Sans314pt7bBitmaps[] PROGMEM = {
  0x00, 0xFF, 0xFF, 0xFF, 0xFC, 0x0F, 0xDE, 0xF7, 0xBD, 0x80, 0x0C, 0x60,
  0xC6, 0x0C, 0x40, 0x84, 0x08, 0x41, 0x8C, 0xFF, 0xFF, 0xFF, 0x18, 0x81,
  0x08, 0x10, 0x81, 0x08, 0xFF, 0xFF, 0xFF, 0x31, 0x82, 0x10, 0x21, 0x02,
  0x30, 0x63, 0x06, 0x30, 0x01, 0x80, 0x03, 0x00, 0x06, 0x00, 0x0C, 0x00,
  0x18, 0x00, 0x30, 0x03, 0xF8, 0x0F, 0xFC, 0x39, 0x98, 0xE3, 0x01, 0x86,
  0x03, 0x0C, 0x07, 0x18, 0x07, 0x30, 0x07, 0xE0, 0x03, 0xF0, 0x01, 0xF8,
  0x03, 0x38, 0x06, 0x38, 0x0C, 0x70, 0x18, 0xE0, 0x31, 0xD0, 0x63, 0x38,
  0xCE, 0x7F, 0xF8, 0x1F, 0xC0, 0x06, 0x00, 0x0C, 0x00, 0x18, 0x00, 0x30,
  0x00, 0x60, 0x00, 0x1E, 0x01, 0x0F, 0xF0, 0x43, 0x86, 0x18, 0x60, 0x62,
  0x0C, 0x0C, 0xC1, 0x81, 0x90, 0x38, 0x64, 0x03, 0xFD, 0x80, 0x1E, 0x20,
  0x00, 0x0C, 0x00, 0x03, 0x1C, 0x00, 0x4F, 0xE0, 0x19, 0x0C, 0x02, 0x40,
  0xC0, 0x88, 0x18, 0x31, 0x03, 0x04, 0x20, 0x61, 0x82, 0x18, 0x20, 0x7F,
  0x08, 0x07, 0x80, 0x07, 0xC0, 0x07, 0xF0, 0x07, 0x1C, 0x03, 0x06, 0x01,
  0x83, 0x00, 0xC1, 0x80, 0x61, 0x80, 0x19, 0xC0, 0x0F, 0x80, 0x07, 0x80,
  0x07, 0xE0, 0x0E, 0x30, 0x66, 0x0C, 0x66, 0x03, 0x33, 0x01, 0xF1, 0x80,
  0x78, 0xC0, 0x1C, 0x70, 0x1F, 0x1C, 0x3D, 0x87, 0xF8, 0x60, 0xF0, 0x00,
  0xF7, 0x00, 0x03, 0x06, 0x04, 0x0C, 0x18, 0x18, 0x30, 0x30, 0x30, 0x60,
  0x60, 0x60, 0xE0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0,
  0xC0, 0xE0, 0x60, 0x60, 0x60, 0x30, 0x30, 0x30, 0x18, 0x18, 0x0C, 0x06,
  0x06, 0x03, 0xC0, 0x60, 0x60, 0x30, 0x18, 0x18, 0x0C, 0x0C, 0x0C, 0x06,
  0x06, 0x06, 0x07, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
  0x03, 0x06, 0x06, 0x06, 0x06, 0x0C, 0x0C, 0x0C, 0x18, 0x18, 0x30, 0x60,
  0x60, 0xC0, 0x06, 0x00, 0x60, 0x46, 0x0E, 0x67, 0x36, 0xC0, 0xF0, 0x0F,
  0x03, 0x6C, 0xE6, 0x74, 0x62, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06,
  0x00, 0x60, 0x06, 0x0F, 0xFF, 0xFF, 0xF0, 0x60, 0x06, 0x00, 0x60, 0x06,
  0x00, 0x60, 0xF7, 0x00, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x0C, 0x00, 0x20,
  0x01, 0x80, 0x04, 0x00, 0x10, 0x00, 0xC0, 0x02, 0x00, 0x18, 0x00, 0x60,
  0x01, 0x00, 0x0C, 0x00, 0x30, 0x00, 0x80, 0x06, 0x00, 0x18, 0x00, 0x40,
  0x03, 0x00, 0x08, 0x00, 0x20, 0x01, 0x80, 0x04, 0x00, 0x30, 0x00, 0xC0,
  0x02, 0x00, 0x18, 0x00, 0x60, 0x01, 0x00, 0x0C, 0x00, 0x30, 0x00, 0x80,
  0x06, 0x00, 0x10, 0x00, 0xC0, 0x03, 0x00, 0x00, 0x07, 0xE0, 0x1F, 0xF8,
  0x38, 0x1C, 0x70, 0x0E, 0x60, 0x06, 0x60, 0x06, 0xC0, 0x03, 0xC0, 0x03,
  0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03,
  0x60, 0x06, 0x60, 0x06, 0x70, 0x0E, 0x38, 0x1C, 0x1F, 0xF8, 0x07, 0xE0,
  0x06, 0x1C, 0x79, 0xF6, 0x68, 0xC1, 0x83, 0x06, 0x0C, 0x18, 0x30, 0x60,
  0xC1, 0x83, 0x06, 0x0C, 0x18, 0x30, 0x0F, 0x81, 0xFF, 0x1C, 0x1C, 0xC0,
  0x60, 0x03, 0x00, 0x18, 0x00, 0xC0, 0x06, 0x00, 0x60, 0x07, 0x00, 0x70,
  0x07, 0x00, 0x70, 0x07, 0x00, 0x70, 0x03, 0x00, 0x30, 0x03, 0x80, 0x3F,
  0xFF, 0xFF, 0xF0, 0x0F, 0xC0, 0x7F, 0x83, 0x87, 0x08, 0x0E, 0x00, 0x18,
  0x00, 0x60, 0x03, 0x80, 0x1C, 0x07, 0xE0, 0x1F, 0x80, 0x03, 0x80, 0x06,
  0x00, 0x0C, 0x00, 0x30, 0x00, 0xF0, 0x03, 0x60, 0x19, 0xC0, 0xE3, 0xFF,
  0x03, 0xF0, 0x00, 0x60, 0x03, 0x80, 0x0E, 0x00, 0x78, 0x03, 0xE0, 0x0D,
  0x80, 0x66, 0x03, 0x98, 0x0C, 0x60, 0x61, 0x83, 0x06, 0x1C, 0x18, 0x60,
  0x63, 0xFF, 0xFF, 0xFF, 0xC0, 0x18, 0x00, 0x60, 0x01, 0x80, 0x06, 0x00,
  0x18, 0x7F, 0xE7, 0xFE, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0xC0, 0x0C,
  0xF0, 0xDF, 0xCE, 0x0E, 0xC0, 0x60, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30,
  0x03, 0x00, 0x6E, 0x0E, 0x7F, 0xC1, 0xF0, 0x07, 0xC0, 0x7F, 0xC3, 0xC3,
  0x8C, 0x06, 0x70, 0x01, 0x80, 0x0E, 0x00, 0x38, 0xF8, 0xEF, 0xF3, 0xE0,
  0xEF, 0x01, 0xB8, 0x03, 0xE0, 0x0F, 0x80, 0x3E, 0x00, 0xD8, 0x03, 0x70,
  0x18, 0xE0, 0xE1, 0xFF, 0x03, 0xF0, 0xFF, 0xFF, 0xFF, 0xC0, 0x0E, 0x00,
  0x60, 0x07, 0x00, 0x30, 0x01, 0x80, 0x18, 0x00, 0xC0, 0x0C, 0x00, 0x60,
  0x07, 0x00, 0x30, 0x03, 0x80, 0x18, 0x01, 0xC0, 0x0C, 0x00, 0x60, 0x06,
  0x00, 0x30, 0x00, 0x0F, 0xC0, 0x40, 0x82, 0x03, 0x18, 0x06, 0x60, 0x19,
  0x80, 0x67, 0x03, 0x8E, 0x1C, 0x1F, 0xE0, 0x7F, 0x87, 0x03, 0x98, 0x06,
  0xC0, 0x0F, 0x00, 0x3C, 0x00, 0xF0, 0x03, 0x60, 0x19, 0xC0, 0xE3, 0xFF,
  0x03, 0xF0, 0x0F, 0xC0, 0xFF, 0x87, 0x07, 0x18, 0x0E, 0xC0, 0x1B, 0x00,
  0x7C, 0x00, 0xF0, 0x03, 0xC0, 0x0D, 0x80, 0x77, 0x07, 0xCF, 0xF3, 0x0F,
  0x8C, 0x00, 0x30, 0x01, 0x80, 0x06, 0x60, 0x31, 0xC1, 0xC3, 0xFE, 0x03,
  0xE0, 0xF0, 0x00, 0x00, 0xF0, 0xF0, 0x00, 0x00, 0xF7, 0x00, 0x00, 0x10,
  0x03, 0x00, 0xE0, 0x18, 0x07, 0x00, 0xC0, 0x38, 0x06, 0x00, 0xE0, 0x03,
  0x00, 0x1C, 0x00, 0x60, 0x03, 0x80, 0x0C, 0x00, 0x70, 0x03, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF,
  0x80, 0x0C, 0x00, 0x70, 0x01, 0x80, 0x0E, 0x00, 0x30, 0x01, 0xC0, 0x06,
  0x00, 0x70, 0x0C, 0x03, 0x80, 0x60, 0x1C, 0x03, 0x00, 0xE0, 0x0C, 0x00,
  0x00, 0x00, 0x3E, 0x3F, 0xCC, 0x38, 0x07, 0x00, 0xC0, 0x30, 0x0C, 0x03,
  0x01, 0x81, 0xC3, 0xE0, 0xC0, 0x30, 0x0C, 0x03, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x00, 0xC0, 0x00, 0xFE, 0x00, 0x03, 0xFF, 0xC0, 0x07, 0x81, 0xE0,
  0x0E, 0x00, 0x78, 0x18, 0x00, 0x18, 0x30, 0x00, 0x0C, 0x70, 0x7C, 0x0E,
  0x61, 0xFE, 0x06, 0x61, 0x03, 0x06, 0xC0, 0x03, 0x83, 0xC0, 0x01, 0x83,
  0xC0, 0x39, 0x83, 0xC1, 0xFF, 0x83, 0xC3, 0x83, 0x83, 0xC3, 0x01, 0x83,
  0xC3, 0x01, 0x83, 0xC3, 0x01, 0x82, 0x63, 0x83, 0x86, 0x61, 0xFF, 0x86,
  0x70, 0xF8, 0xEC, 0x30, 0x00, 0x78, 0x18, 0x00, 0x00, 0x1C, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x03, 0xFF, 0x00, 0x00, 0xFC, 0x00, 0x01, 0x80, 0x01,
  0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x07, 0xE0, 0x06, 0x60, 0x06, 0x60, 0x0E,
  0x30, 0x0C, 0x30, 0x0C, 0x30, 0x1C, 0x18, 0x18, 0x18, 0x18, 0x1C, 0x3F,
  0xFC, 0x3F, 0xFC, 0x30, 0x0E, 0x60, 0x06, 0x60, 0x06, 0xE0, 0x07, 0xC0,
  0x03, 0xFF, 0xC3, 0xFF, 0x8C, 0x07, 0x30, 0x0E, 0xC0, 0x1B, 0x00, 0x6C,
  0x01, 0xB0, 0x0C, 0xC0, 0x73, 0xFF, 0x0F, 0xFF, 0x30, 0x0E, 0xC0, 0x1F,
  0x00, 0x3C, 0x00, 0xF0, 0x03, 0xC0, 0x1F, 0x00, 0xEF, 0xFF, 0x3F, 0xF8,
  0x07, 0xF0, 0x3F, 0xF8, 0xF0, 0x73, 0x80, 0x06, 0x00, 0x0C, 0x00, 0x30,
  0x00, 0x60, 0x00, 0xC0, 0x01, 0x80, 0x03, 0x00, 0x06, 0x00, 0x0C, 0x00,
  0x18, 0x00, 0x18, 0x00, 0x30, 0x00, 0x70, 0x00, 0x78, 0x1C, 0x7F, 0xF0,
  0x3F, 0x80, 0xFF, 0x81, 0xFF, 0xC3, 0x03, 0xC6, 0x01, 0xCC, 0x01, 0x98,
  0x01, 0xB0, 0x03, 0x60, 0x03, 0xC0, 0x07, 0x80, 0x0F, 0x00, 0x1E, 0x00,
  0x3C, 0x00, 0x78, 0x01, 0xB0, 0x03, 0x60, 0x0C, 0xC0, 0x39, 0x81, 0xE3,
  0xFF, 0x87, 0xFC, 0x00, 0xFF, 0xFF, 0xFF, 0xF0, 0x01, 0x80, 0x0C, 0x00,
  0x60, 0x03, 0x00, 0x18, 0x00, 0xC0, 0x07, 0xFE, 0x3F, 0xF1, 0x80, 0x0C,
  0x00, 0x60, 0x03, 0x00, 0x18, 0x00, 0xC0, 0x06, 0x00, 0x3F, 0xFF, 0xFF,
  0xF0, 0xFF, 0xFF, 0xFF, 0xF0, 0x01, 0x80, 0x0C, 0x00, 0x60, 0x03, 0x00,
  0x18, 0x00, 0xC0, 0x07, 0xFE, 0x3F, 0xF1, 0x80, 0x0C, 0x00, 0x60, 0x03,
  0x00, 0x18, 0x00, 0xC0, 0x06, 0x00, 0x30, 0x01, 0x80, 0x00, 0x07, 0xF0,
  0x3F, 0xF8, 0xF0, 0x73, 0x80, 0x06, 0x00, 0x0C, 0x00, 0x30, 0x00, 0x60,
  0x00, 0xC0, 0x01, 0x80, 0xFF, 0x01, 0xFE, 0x00, 0x3C, 0x00, 0x78, 0x00,
  0xD8, 0x01, 0xB0, 0x03, 0x70, 0x06, 0x78, 0x3C, 0x7F, 0xF0, 0x3F, 0x80,
  0xC0, 0x0F, 0x00, 0x3C, 0x00, 0xF0, 0x03, 0xC0, 0x0F, 0x00, 0x3C, 0x00,
  0xF0, 0x03, 0xC0, 0x0F, 0xFF, 0xFF, 0xFF, 0xF0, 0x03, 0xC0, 0x0F, 0x00,
  0x3C, 0x00, 0xF0, 0x03, 0xC0, 0x0F, 0x00, 0x3C, 0x00, 0xF0, 0x03, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30,
  0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30,
  0x03, 0x00, 0x34, 0x03, 0xE0, 0x67, 0x0E, 0x3F, 0xC1, 0xF0, 0xC0, 0x73,
  0x03, 0x8C, 0x1C, 0x30, 0x60, 0xC3, 0x03, 0x18, 0x0C, 0xE0, 0x37, 0x00,
  0xF8, 0x03, 0xC0, 0x0F, 0x80, 0x37, 0x00, 0xCC, 0x03, 0x18, 0x0C, 0x30,
  0x30, 0x60, 0xC0, 0xC3, 0x01, 0x8C, 0x07, 0x30, 0x0E, 0xC0, 0x06, 0x00,
  0x30, 0x01, 0x80, 0x0C, 0x00, 0x60, 0x03, 0x00, 0x18, 0x00, 0xC0, 0x06,
  0x00, 0x30, 0x01, 0x80, 0x0C, 0x00, 0x60, 0x03, 0x00, 0x18, 0x00, 0xC0,
  0x06, 0x00, 0x3F, 0xFF, 0xFF, 0xF0, 0xE0, 0x00, 0xFC, 0x00, 0x1F, 0xC0,
  0x07, 0xF8, 0x00, 0xFF, 0x00, 0x1F, 0xB0, 0x06, 0xF6, 0x00, 0xDE, 0x60,
  0x33, 0xCC, 0x06, 0x79, 0x80, 0xCF, 0x18, 0x31, 0xE3, 0x06, 0x3C, 0x60,
  0xC7, 0x86, 0x30, 0xF0, 0xC6, 0x1E, 0x0D, 0x83, 0xC1, 0xB0, 0x78, 0x36,
  0x0F, 0x03, 0x81, 0xE0, 0x70, 0x30, 0xE0, 0x07, 0xC0, 0x0F, 0xC0, 0x1F,
  0x80, 0x3D, 0x80, 0x7B, 0x80, 0xF3, 0x01, 0xE3, 0x03, 0xC7, 0x07, 0x86,
  0x0F, 0x06, 0x1E, 0x0E, 0x3C, 0x0C, 0x78, 0x0C, 0xF0, 0x1D, 0xE0, 0x1B,
  0xC0, 0x1F, 0x80, 0x3F, 0x00, 0x3E, 0x00, 0x70, 0x07, 0xE0, 0x1F, 0xF8,
  0x38, 0x1C, 0x70, 0x0E, 0x60, 0x06, 0x60, 0x06, 0xC0, 0x03, 0xC0, 0x03,
  0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03,
  0x60, 0x06, 0x60, 0x06, 0x70, 0x0E, 0x38, 0x1C, 0x1F, 0xF8, 0x07, 0xE0,
  0xFF, 0xC3, 0xFF, 0xCC, 0x03, 0xB0, 0x06, 0xC0, 0x0F, 0x00, 0x3C, 0x00,
  0xF0, 0x03, 0xC0, 0x0F, 0x00, 0x6C, 0x03, 0xBF, 0xFC, 0xFF, 0xC3, 0x00,
  0x0C, 0x00, 0x30, 0x00, 0xC0, 0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0x07,
  0xE0, 0x07, 0xFE, 0x03, 0x81, 0xC1, 0xC0, 0x38, 0x60, 0x06, 0x18, 0x01,
  0x8C, 0x00, 0x33, 0x00, 0x0C, 0xC0, 0x03, 0x30, 0x00, 0xCC, 0x00, 0x33,
  0x00, 0x0C, 0xC0, 0x03, 0x30, 0x00, 0xCC, 0x00, 0x71, 0x80, 0x18, 0x60,
  0x06, 0x0C, 0x03, 0x01, 0xFF, 0x80, 0x3F, 0xC0, 0x01, 0x80, 0x00, 0x60,
  0x00, 0x18, 0x00, 0x03, 0x00, 0x00, 0xE3, 0x80, 0x1F, 0xE0, 0x00, 0x80,
  0xFF, 0xC7, 0xFF, 0x30, 0x1D, 0x80, 0x7C, 0x01, 0xE0, 0x0F, 0x00, 0x78,
  0x03, 0xC0, 0x36, 0x03, 0xBF, 0xF9, 0xFF, 0xCC, 0x07, 0x60, 0x0F, 0x00,
  0x78, 0x03, 0xC0, 0x1E, 0x00, 0xF0, 0x07, 0x80, 0x30, 0x0F, 0xC0, 0xFF,
  0xE7, 0x03, 0x98, 0x00, 0x60, 0x01, 0x80, 0x06, 0x00, 0x1E, 0x00, 0x3E,
  0x00, 0x3F, 0x00, 0x1F, 0x00, 0x0E, 0x00, 0x1C, 0x00, 0x30, 0x00, 0xC0,
  0x03, 0x00, 0x1F, 0xC0, 0xE7, 0xFF, 0x03, 0xF0, 0xFF, 0xFF, 0xFF, 0xF0,
  0x30, 0x00, 0xC0, 0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x03, 0x00,
  0x0C, 0x00, 0x30, 0x00, 0xC0, 0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0,
  0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0xC0, 0x0F, 0x00, 0x3C, 0x00,
  0xF0, 0x03, 0xC0, 0x0F, 0x00, 0x3C, 0x00, 0xF0, 0x03, 0xC0, 0x0F, 0x00,
  0x3C, 0x00, 0xF0, 0x03, 0xC0, 0x0F, 0x00, 0x3C, 0x00, 0xF8, 0x06, 0x60,
  0x18, 0xE1, 0xC1, 0xFE, 0x03, 0xF0, 0xC0, 0x03, 0xE0, 0x03, 0x60, 0x06,
  0x60, 0x06, 0x30, 0x0E, 0x30, 0x0C, 0x30, 0x0C, 0x18, 0x1C, 0x18, 0x18,
  0x1C, 0x18, 0x0C, 0x38, 0x0C, 0x30, 0x0E, 0x30, 0x06, 0x60, 0x06, 0x60,
  0x03, 0x60, 0x03, 0xC0, 0x03, 0xC0, 0x01, 0xC0, 0x01, 0x80, 0xC0, 0x0E,
  0x00, 0x7C, 0x01, 0xC0, 0x19, 0x80, 0x38, 0x03, 0x30, 0x0F, 0x00, 0x66,
  0x01, 0xB0, 0x1C, 0x60, 0x36, 0x03, 0x0C, 0x0C, 0xC0, 0x61, 0x81, 0x8C,
  0x1C, 0x38, 0x31, 0x83, 0x03, 0x06, 0x30, 0x60, 0x61, 0x83, 0x0C, 0x0E,
  0x30, 0x63, 0x00, 0xC6, 0x0C, 0x60, 0x19, 0x81, 0x8C, 0x03, 0x30, 0x1B,
  0x80, 0x36, 0x03, 0x60, 0x07, 0x80, 0x6C, 0x00, 0xF0, 0x07, 0x80, 0x1E,
  0x00, 0xE0, 0x01, 0xC0, 0x1C, 0x00, 0x60, 0x03, 0x38, 0x03, 0x0E, 0x03,
  0x83, 0x03, 0x81, 0xC1, 0x80, 0x71, 0xC0, 0x19, 0xC0, 0x06, 0xC0, 0x03,
  0xE0, 0x00, 0xE0, 0x00, 0x70, 0x00, 0x7C, 0x00, 0x77, 0x00, 0x31, 0x80,
  0x30, 0xE0, 0x38, 0x38, 0x38, 0x0C, 0x18, 0x03, 0x1C, 0x01, 0xDC, 0x00,
  0x70, 0x60, 0x06, 0x70, 0x0E, 0x30, 0x0C, 0x38, 0x1C, 0x18, 0x18, 0x0C,
  0x30, 0x0E, 0x70, 0x06, 0x60, 0x07, 0xE0, 0x03, 0xC0, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x80, 0xFF, 0xFF, 0xFF, 0xF0, 0x01, 0x80, 0x0E,
  0x00, 0x70, 0x01, 0x80, 0x0E, 0x00, 0x70, 0x01, 0x80, 0x0C, 0x00, 0x70,
  0x01, 0x80, 0x0C, 0x00, 0x70, 0x01, 0x80, 0x0C, 0x00, 0x60, 0x01, 0x80,
  0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0,
  0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0,
  0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0,
  0xC0, 0xC0, 0xC0, 0xFF, 0xFF, 0xC0, 0x03, 0x00, 0x04, 0x00, 0x18, 0x00,
  0x20, 0x00, 0xC0, 0x03, 0x00, 0x04, 0x00, 0x18, 0x00, 0x60, 0x00, 0x80,
  0x03, 0x00, 0x0C, 0x00, 0x10, 0x00, 0x60, 0x00, 0x80, 0x02, 0x00, 0x0C,
  0x00, 0x10, 0x00, 0x60, 0x01, 0x80, 0x02, 0x00, 0x0C, 0x00, 0x30, 0x00,
  0x40, 0x01, 0x80, 0x06, 0x00, 0x08, 0x00, 0x30, 0x00, 0x40, 0x01, 0x00,
  0x06, 0x00, 0x08, 0x00, 0x30, 0xFF, 0xFF, 0x03, 0x03, 0x03, 0x03, 0x03,
  0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
  0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
  0x03, 0x03, 0x03, 0x03, 0xFF, 0xFF, 0x04, 0x01, 0xC0, 0x2C, 0x0C, 0x83,
  0x18, 0x41, 0x98, 0x16, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC9,
  0xB2, 0x1F, 0x0F, 0xF9, 0x83, 0x00, 0x30, 0x06, 0x10, 0xDF, 0xFF, 0x07,
  0xC0, 0x78, 0x0F, 0x01, 0xF0, 0x77, 0xFE, 0x7C, 0xC0, 0xC0, 0x0C, 0x00,
  0xC0, 0x0C, 0x00, 0xC0, 0x0C, 0x00, 0xCF, 0x8D, 0xFC, 0xF0, 0xEC, 0x06,
  0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x6F, 0x0E,
  0xDF, 0xCC, 0xF8, 0x0F, 0x8F, 0xF7, 0x01, 0x80, 0xC0, 0x30, 0x0C, 0x03,
  0x00, 0xC0, 0x30, 0x06, 0x01, 0xC3, 0x3F, 0xC7, 0xE0, 0x00, 0x30, 0x03,
  0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x1F, 0x33, 0xFB, 0x70, 0xF6, 0x03,
  0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0x60, 0x37, 0x0F,
  0x3F, 0xB1, 0xF3, 0x1F, 0x03, 0xFC, 0x70, 0xC6, 0x06, 0xC0, 0x6F, 0xFE,
  0xFF, 0xEC, 0x00, 0xC0, 0x0C, 0x00, 0x60, 0x07, 0x02, 0x3F, 0xE0, 0xF8,
  0x06, 0x3C, 0x61, 0x83, 0x06, 0x3F, 0xFF, 0x30, 0x60, 0xC1, 0x83, 0x06,
  0x0C, 0x18, 0x30, 0x60, 0xC1, 0x80, 0x1F, 0x33, 0xFB, 0x70, 0xF6, 0x03,
  0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0x60, 0x37, 0x0F,
  0x3F, 0xB1, 0xF3, 0x00, 0x30, 0x03, 0x00, 0x30, 0x06, 0x60, 0xE7, 0xFC,
  0x3F, 0x80, 0xC0, 0x18, 0x03, 0x00, 0x60, 0x0C, 0x01, 0x80, 0x33, 0xC6,
  0xFE, 0xF0, 0xDC, 0x0F, 0x01, 0xE0, 0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x03,
  0xC0, 0x78, 0x0F, 0x01, 0xE0, 0x30, 0xF0, 0xFF, 0xFF, 0xFF, 0xF0, 0x0C,
  0x30, 0x00, 0x0C, 0x30, 0xC3, 0x0C, 0x30, 0xC3, 0x0C, 0x30, 0xC3, 0x0C,
  0x30, 0xC3, 0x0C, 0x63, 0xBC, 0xE0, 0xC0, 0x18, 0x03, 0x00, 0x60, 0x0C,
  0x01, 0x80, 0x30, 0x76, 0x1C, 0xC7, 0x19, 0xC3, 0x70, 0x7C, 0x0F, 0x01,
  0xF0, 0x37, 0x06, 0x70, 0xC7, 0x18, 0x73, 0x07, 0x60, 0x60, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xCF, 0x0F, 0x37, 0xE7, 0xEE, 0x1E, 0x1F, 0x03, 0x03,
  0xC0, 0xC0, 0xF0, 0x30, 0x3C, 0x0C, 0x0F, 0x03, 0x03, 0xC0, 0xC0, 0xF0,
  0x30, 0x3C, 0x0C, 0x0F, 0x03, 0x03, 0xC0, 0xC0, 0xF0, 0x30, 0x30, 0xCF,
  0x1B, 0xFB, 0xC3, 0x70, 0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0, 0x78,
  0x0F, 0x01, 0xE0, 0x3C, 0x07, 0x80, 0xC0, 0x1F, 0x83, 0xFC, 0x70, 0xE6,
  0x06, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xE0, 0x67,
  0x0E, 0x3F, 0xC1, 0xF8, 0xCF, 0x8D, 0xFC, 0xF0, 0xEC, 0x06, 0xC0, 0x3C,
  0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x6F, 0x0E, 0xDF, 0xCC,
  0xF8, 0xC0, 0x0C, 0x00, 0xC0, 0x0C, 0x00, 0xC0, 0x0C, 0x00, 0xC0, 0x00,
  0x1F, 0x33, 0xFB, 0x70, 0xF6, 0x03, 0xC0, 0x3C, 0x03, 0xC0, 0x3C, 0x03,
  0xC0, 0x3C, 0x03, 0x60, 0x37, 0x0F, 0x3F, 0xB1, 0xF3, 0x00, 0x30, 0x03,
  0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x01, 0xBF, 0xFF, 0x0C,
  0x18, 0x30, 0x60, 0xC1, 0x83, 0x06, 0x0C, 0x18, 0x30, 0x00, 0x3E, 0x3F,
  0xB8, 0x58, 0x0C, 0x07, 0x01, 0xF0, 0x3E, 0x03, 0x80, 0xC0, 0x78, 0x7F,
  0xF1, 0xF0, 0x30, 0x60, 0xC7, 0xFF, 0xE6, 0x0C, 0x18, 0x30, 0x60, 0xC1,
  0x83, 0x06, 0x0C, 0x18, 0x30, 0xC0, 0x78, 0x0F, 0x01, 0xE0, 0x3C, 0x07,
  0x80, 0xF0, 0x1E, 0x03, 0xC0, 0x78, 0x0F, 0x03, 0xB0, 0xF7, 0xF6, 0x3C,
  0xC0, 0xC0, 0x3E, 0x07, 0x60, 0x66, 0x06, 0x30, 0xE3, 0x0C, 0x30, 0xC1,
  0x9C, 0x19, 0x81, 0x98, 0x0F, 0x00, 0xF0, 0x0F, 0x00, 0x60, 0xC0, 0x60,
  0x3E, 0x0F, 0x07, 0x60, 0xF0, 0x66, 0x0F, 0x06, 0x61, 0x90, 0x63, 0x19,
  0x8C, 0x31, 0x98, 0xC3, 0x11, 0x8C, 0x33, 0x0D, 0x81, 0xB0, 0xD8, 0x1B,
  0x0D, 0x81, 0xE0, 0x50, 0x0E, 0x07, 0x00, 0xE0, 0x70, 0x60, 0x71, 0x83,
  0x0E, 0x30, 0x33, 0x80, 0xD8, 0x07, 0x80, 0x18, 0x01, 0xE0, 0x0D, 0x80,
  0xCE, 0x0E, 0x30, 0x60, 0xC6, 0x07, 0x70, 0x18, 0xC0, 0x78, 0x0F, 0x01,
  0xE0, 0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0, 0x78, 0x0F, 0x03, 0xB0,
  0xF7, 0xF6, 0x3C, 0xC0, 0x18, 0x03, 0x00, 0x60, 0x1B, 0x07, 0x7F, 0xC3,
  0xF0, 0xFF, 0xFF, 0xF0, 0x18, 0x0E, 0x07, 0x01, 0x80, 0xC0, 0x70, 0x18,
  0x0C, 0x06, 0x01, 0x80, 0xFF, 0xFF, 0xF0, 0x00, 0x70, 0x1F, 0x03, 0x80,
  0x30, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00,
  0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0xC0, 0x18, 0x0F, 0x00, 0xF0, 0x01,
  0x80, 0x0C, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00,
  0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x03, 0x00, 0x38, 0x01, 0xF0,
  0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0x0F, 0x80, 0x1C, 0x00,
  0xC0, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00,
  0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0x30, 0x01, 0x80, 0x0F, 0x00, 0xF0,
  0x18, 0x03, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00,
  0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x0C, 0x01, 0xC0, 0xF8, 0x0E,
  0x00, 0x71, 0x9F, 0x86, 0x18, 0x3C, 0x3C, 0x7E, 0x7E, 0xFF, 0xFF, 0x18,
  0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18,
  0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18,
  0x18, 0xFF, 0xFF, 0x7E, 0x7E, 0x3C, 0x3C, 0x18
};

const GFXglyph Pragma_Sans314pt7bGlyphs[] PROGMEM = {
  {     0,   1,   1,   7,    0,    0 },   // 0x20 ' '
  {     1,   2,  20,   6,    2,  -19 },   // 0x21 '!'
  {     6,   5,   5,   7,    1,  -19 },   // 0x22 '"'
  {    10,  12,  20,  14,    1,  -19 },   // 0x23 '#'
  {    40,  15,  31,  17,    1,  -25 },   // 0x24 '$'
  {    99,  19,  20,  21,    1,  -19 },   // 0x25 '%'
  {   147,  17,  21,  19,    1,  -19 },   // 0x26 '&'
  {   192,   2,   5,   4,    1,  -19 },   // 0x27 '''
  {   194,   8,  36,  10,    1,  -26 },   // 0x28 '('
  {   230,   8,  36,  10,    1,  -26 },   // 0x29 ')'
  {   266,  12,  12,  14,    1,  -19 },   // 0x2A '*'
  {   284,  12,  12,  14,    1,  -15 },   // 0x2B '+'
  {   302,   2,   5,   4,    1,   -1 },   // 0x2C ','
  {   304,  12,   2,  14,    1,  -10 },   // 0x2D '-'
  {   307,   2,   2,   4,    1,   -1 },   // 0x2E '.'
  {   308,  14,  34,  16,    1,  -26 },   // 0x2F '/'
  {   368,  16,  20,  18,    1,  -19 },   // 0x30 '0'
  {   408,   7,  20,   9,    1,  -19 },   // 0x31 '1'
  {   426,  13,  20,  15,    1,  -19 },   // 0x32 '2'
  {   459,  14,  20,  16,    1,  -19 },   // 0x33 '3'
  {   494,  14,  20,  16,    1,  -19 },   // 0x34 '4'
  {   529,  12,  20,  14,    1,  -19 },   // 0x35 '5'
  {   559,  14,  20,  16,    1,  -19 },   // 0x36 '6'
  {   594,  13,  20,  15,    1,  -19 },   // 0x37 '7'
  {   627,  14,  20,  16,    1,  -19 },   // 0x38 '8'
  {   662,  14,  20,  17,    1,  -19 },   // 0x39 '9'
  {   697,   2,  14,   4,    1,  -13 },   // 0x3A ':'
  {   701,   2,  17,   4,    1,  -13 },   // 0x3B ';'
  {   706,  12,  17,  14,    1,  -17 },   // 0x3C '<'
  {   732,  12,   8,  14,    1,  -13 },   // 0x3D '='
  {   744,  12,  17,  14,    1,  -17 },   // 0x3E '>'
  {   770,  10,  20,  12,    1,  -19 },   // 0x3F '?'
  {   795,  24,  26,  26,    1,  -19 },   // 0x40 '@'
  {   873,  16,  20,  18,    1,  -19 },   // 0x41 'A'
  {   913,  14,  20,  16,    1,  -19 },   // 0x42 'B'
  {   948,  15,  20,  17,    1,  -19 },   // 0x43 'C'
  {   986,  15,  20,  17,    1,  -19 },   // 0x44 'D'
  {  1024,  13,  20,  14,    1,  -19 },   // 0x45 'E'
  {  1057,  13,  20,  14,    1,  -19 },   // 0x46 'F'
  {  1090,  15,  20,  17,    1,  -19 },   // 0x47 'G'
  {  1128,  14,  20,  16,    1,  -19 },   // 0x48 'H'
  {  1163,   2,  20,   4,    1,  -19 },   // 0x49 'I'
  {  1168,  12,  20,  14,    1,  -19 },   // 0x4A 'J'
  {  1198,  14,  20,  16,    1,  -19 },   // 0x4B 'K'
  {  1233,  13,  20,  15,    1,  -19 },   // 0x4C 'L'
  {  1266,  19,  20,  21,    1,  -19 },   // 0x4D 'M'
  {  1314,  15,  20,  17,    1,  -19 },   // 0x4E 'N'
  {  1352,  16,  20,  18,    1,  -19 },   // 0x4F 'O'
  {  1392,  14,  20,  16,    1,  -19 },   // 0x50 'P'
  {  1427,  18,  27,  19,    1,  -19 },   // 0x51 'Q'
  {  1488,  13,  20,  15,    1,  -19 },   // 0x52 'R'
  {  1521,  14,  20,  16,    1,  -19 },   // 0x53 'S'
  {  1556,  14,  20,  16,    1,  -19 },   // 0x54 'T'
  {  1591,  14,  20,  16,    1,  -19 },   // 0x55 'U'
  {  1626,  16,  20,  18,    1,  -19 },   // 0x56 'V'
  {  1666,  27,  20,  29,    1,  -19 },   // 0x57 'W'
  {  1734,  17,  20,  19,    1,  -19 },   // 0x58 'X'
  {  1777,  16,  20,  18,    1,  -19 },   // 0x59 'Y'
  {  1817,  14,  20,  16,    1,  -19 },   // 0x5A 'Z'
  {  1852,   8,  37,  10,    1,  -27 },   // 0x5B '['
  {  1889,  14,  34,  16,    1,  -26 },   // 0x5C '\'
  {  1949,   8,  37,  10,    1,  -27 },   // 0x5D ']'
  {  1986,  11,   8,  13,    1,  -19 },   // 0x5E '^'
  {  1997,  24,   2,  26,    1,    1 },   // 0x5F '_'
  {  2003,   3,   5,   5,    1,  -19 },   // 0x60 '`'
  {  2005,  11,  14,  13,    1,  -13 },   // 0x61 'a'
  {  2025,  12,  20,  14,    1,  -19 },   // 0x62 'b'
  {  2055,  10,  14,  12,    1,  -13 },   // 0x63 'c'
  {  2073,  12,  20,  14,    1,  -19 },   // 0x64 'd'
  {  2103,  12,  14,  13,    1,  -13 },   // 0x65 'e'
  {  2124,   7,  20,   9,    1,  -19 },   // 0x66 'f'
  {  2142,  12,  21,  14,    1,  -13 },   // 0x67 'g'
  {  2174,  11,  20,  13,    1,  -19 },   // 0x68 'h'
  {  2202,   2,  18,   4,    1,  -17 },   // 0x69 'i'
  {  2207,   6,  25,   7,    0,  -17 },   // 0x6A 'j'
  {  2226,  11,  20,  13,    1,  -19 },   // 0x6B 'k'
  {  2254,   2,  20,   4,    1,  -19 },   // 0x6C 'l'
  {  2259,  18,  14,  20,    1,  -13 },   // 0x6D 'm'
  {  2291,  11,  14,  13,    1,  -13 },   // 0x6E 'n'
  {  2311,  12,  14,  14,    1,  -13 },   // 0x6F 'o'
  {  2332,  12,  21,  14,    1,  -13 },   // 0x70 'p'
  {  2364,  12,  21,  14,    1,  -13 },   // 0x71 'q'
  {  2396,   7,  15,   9,    1,  -14 },   // 0x72 'r'
  {  2410,   9,  14,  11,    1,  -13 },   // 0x73 's'
  {  2426,   7,  17,   9,    1,  -16 },   // 0x74 't'
  {  2441,  11,  14,  13,    1,  -13 },   // 0x75 'u'
  {  2461,  12,  14,  14,    1,  -13 },   // 0x76 'v'
  {  2482,  20,  14,  22,    1,  -13 },   // 0x77 'w'
  {  2517,  13,  14,  15,    1,  -13 },   // 0x78 'x'
  {  2540,  11,  21,  13,    1,  -13 },   // 0x79 'y'
  {  2569,  10,  14,  12,    1,  -13 },   // 0x7A 'z'
  {  2587,  12,  36,  12,    0,  -27 },   // 0x7B '{'
  {  2641,   2,  24,   4,    1,  -21 },   // 0x7C '|'
  {  2647,  12,  36,  12,    0,  -27 },   // 0x7D '}'
  {  2701,   8,   3,  10,    1,  -19 },   // 0x7E '~'
  {     0,   0,   0,   0,    0,    0 },   // 0x7F 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x80 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x81 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x82 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x83 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x84 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x85 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x86 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x87 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x88 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x89 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8A 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8B 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8C 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8D 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8E 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8F 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x90 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x91 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x92 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x93 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x94 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x95 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x96 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x97 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x98 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x99 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9A 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9B 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9C 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9D 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9E 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9F 'non-printable'
  {  2704,   8,  20,   9,    1,  -19 },   // 0xA0 ' '
  {  2724,   8,  20,   9,    1,  -19 }    // 0xA1 '¡'
};

const GFXfont Pragma_Sans314pt7b PROGMEM = {
  (uint8_t  *)Pragma_Sans314pt7bBitmaps,
  (GFXglyph *)Pragma_Sans314pt7bGlyphs, 0x20, 0xA1,  30 };

// Approx. 3376 bytes
