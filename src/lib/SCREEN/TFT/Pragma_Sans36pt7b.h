const uint8_t Pragma_Sans36pt7bBitmaps[] PROGMEM = {
  0x00, 0xFD, 0xF0, 0x29, 0x7E, 0xAF, 0xA9, 0x4A, 0x10, 0x20, 0x43, 0xC5,
  0x52, 0x1C, 0x0E, 0x17, 0x29, 0xF0, 0x81, 0x00, 0x60, 0x94, 0x68, 0x08,
  0x16, 0x29, 0x29, 0x06, 0x30, 0x91, 0x21, 0x85, 0x11, 0xA3, 0x7E, 0xC0,
  0x12, 0x44, 0x48, 0x88, 0x88, 0x88, 0x44, 0x22, 0x84, 0x22, 0x21, 0x11,
  0x11, 0x11, 0x22, 0x44, 0x25, 0x5D, 0xF2, 0x00, 0x21, 0x3E, 0x42, 0x00,
  0xC0, 0xF8, 0x80, 0x00, 0x10, 0x42, 0x08, 0x21, 0x04, 0x20, 0x82, 0x10,
  0x41, 0x08, 0x00, 0x38, 0x8A, 0x0C, 0x18, 0x30, 0x51, 0x1E, 0x3E, 0x92,
  0x49, 0x74, 0x42, 0x11, 0x11, 0x1F, 0x39, 0x10, 0x46, 0x04, 0x14, 0x5E,
  0x08, 0x62, 0x92, 0x4B, 0xF0, 0x82, 0xF4, 0x3D, 0x30, 0x86, 0x7E, 0x79,
  0x0B, 0xB3, 0x86, 0x1C, 0xDE, 0xF8, 0x44, 0x22, 0x11, 0x08, 0xFA, 0x1C,
  0xDE, 0xCE, 0x18, 0x5E, 0x7A, 0x38, 0x61, 0x7C, 0x10, 0x9E, 0x84, 0x86,
  0x00, 0xC9, 0x84, 0x10, 0x60, 0xF8, 0x01, 0xF0, 0x06, 0x08, 0x31, 0x13,
  0x00, 0xF0, 0xC2, 0x34, 0x20, 0x08, 0x1E, 0x18, 0x64, 0xEA, 0x05, 0x8D,
  0x64, 0xD9, 0x15, 0x3E, 0x60, 0x07, 0x80, 0x18, 0x30, 0xA1, 0x22, 0x4F,
  0x90, 0xA1, 0xFA, 0x18, 0x7E, 0x86, 0x18, 0x7E, 0x3C, 0x82, 0x04, 0x08,
  0x10, 0x10, 0x1E, 0xF9, 0x0A, 0x0C, 0x18, 0x30, 0x61, 0x7C, 0xFC, 0x21,
  0xF8, 0x42, 0x1F, 0xFC, 0x21, 0xF8, 0x42, 0x10, 0x3C, 0x82, 0x04, 0x78,
  0x30, 0x50, 0x9E, 0x86, 0x18, 0x7F, 0x86, 0x18, 0x61, 0xFF, 0x08, 0x42,
  0x10, 0x86, 0x6E, 0x8A, 0x4A, 0x30, 0xC2, 0x89, 0x23, 0x82, 0x08, 0x20,
  0x82, 0x08, 0x3F, 0x81, 0xC3, 0xC3, 0xA5, 0xA5, 0xA9, 0x99, 0x99, 0x87,
  0x1A, 0x69, 0x96, 0x58, 0xE1, 0x38, 0x8A, 0x0C, 0x18, 0x30, 0x51, 0x1C,
  0xFA, 0x18, 0x61, 0xFA, 0x08, 0x20, 0x38, 0x44, 0x82, 0x82, 0x82, 0x82,
  0x82, 0x44, 0x38, 0x10, 0x0F, 0xFA, 0x18, 0x61, 0xFA, 0x18, 0x61, 0x7A,
  0x08, 0x18, 0x18, 0x18, 0x5E, 0xFE, 0x20, 0x40, 0x81, 0x02, 0x04, 0x08,
  0x86, 0x18, 0x61, 0x86, 0x1C, 0xDE, 0x42, 0x85, 0x11, 0x22, 0x45, 0x06,
  0x0C, 0x82, 0x14, 0x62, 0x46, 0x24, 0x92, 0x29, 0x22, 0x94, 0x28, 0xC1,
  0x0C, 0x42, 0x24, 0x34, 0x18, 0x18, 0x24, 0x26, 0x42, 0xC6, 0x88, 0xA1,
  0x41, 0x02, 0x04, 0x08, 0xFC, 0x20, 0x84, 0x21, 0x04, 0x3F, 0xF8, 0x88,
  0x88, 0x88, 0x88, 0x88, 0x88, 0x8F, 0x82, 0x04, 0x10, 0x20, 0x82, 0x04,
  0x10, 0x40, 0x82, 0x08, 0x10, 0x40, 0xF1, 0x11, 0x11, 0x11, 0x11, 0x11,
  0x11, 0x1F, 0x01, 0x94, 0x90, 0xFF, 0xE0, 0x90, 0x70, 0x5B, 0x38, 0xBC,
  0x84, 0x2D, 0x98, 0xC6, 0x3E, 0x74, 0x21, 0x08, 0x38, 0x08, 0x5B, 0x38,
  0xC6, 0x2F, 0x74, 0x7F, 0x08, 0x3C, 0x2B, 0xA4, 0x92, 0x6C, 0xE3, 0x18,
  0xBC, 0x21, 0xF0, 0x84, 0x2D, 0x98, 0xC6, 0x31, 0xBF, 0x45, 0x55, 0x58,
  0x84, 0x25, 0x4C, 0x72, 0x92, 0xFF, 0xED, 0x26, 0x4C, 0x99, 0x32, 0x40,
  0xB6, 0x63, 0x18, 0xC4, 0x74, 0x63, 0x18, 0xB8, 0xB6, 0x63, 0x18, 0xFA,
  0x10, 0x80, 0x6C, 0xE3, 0x18, 0xBC, 0x21, 0x08, 0xF2, 0x49, 0x00, 0xE8,
  0xC3, 0x1F, 0x5D, 0x24, 0x90, 0x8C, 0x63, 0x18, 0xBC, 0x89, 0x24, 0x8C,
  0x30, 0xC0, 0x88, 0xA6, 0x55, 0x4A, 0xA3, 0x51, 0x10, 0x48, 0xA3, 0x0C,
  0x49, 0x10, 0x8C, 0x63, 0x18, 0xBC, 0x21, 0xF0, 0xF8, 0x88, 0x84, 0x7C,
  0x19, 0x88, 0x42, 0x10, 0x88, 0x83, 0x08, 0x42, 0x10, 0xC3, 0xFF, 0xE0,
  0xC3, 0x08, 0x42, 0x10, 0x82, 0x09, 0x88, 0x42, 0x11, 0x98, 0x69, 0x60,
  0x69, 0x60
};

const GFXglyph Pragma_Sans36pt7bGlyphs[] PROGMEM = {
  {     0,   1,   1,   3,    0,    0 },   // 0x20 ' '
  {     1,   1,   8,   3,    1,   -7 },   // 0x21 '!'
  {     2,   2,   2,   4,    1,   -7 },   // 0x22 '"'
  {     3,   5,   8,   6,    0,   -7 },   // 0x23 '#'
  {     8,   7,  13,   7,    0,  -10 },   // 0x24 '$'
  {    20,   8,   8,  10,    1,   -7 },   // 0x25 '%'
  {    28,   7,   8,   8,    1,   -7 },   // 0x26 '&'
  {    35,   1,   2,   3,    1,   -7 },   // 0x27 '''
  {    36,   4,  16,   5,    1,  -11 },   // 0x28 '('
  {    44,   4,  16,   5,    0,  -11 },   // 0x29 ')'
  {    52,   5,   5,   5,    0,   -7 },   // 0x2A '*'
  {    56,   5,   5,   7,    1,   -6 },   // 0x2B '+'
  {    60,   1,   2,   3,    1,    0 },   // 0x2C ','
  {    61,   5,   1,   6,    0,   -4 },   // 0x2D '-'
  {    62,   1,   1,   3,    1,    0 },   // 0x2E '.'
  {    63,   6,  15,   7,    0,  -11 },   // 0x2F '/'
  {    75,   7,   8,   9,    1,   -7 },   // 0x30 '0'
  {    82,   3,   8,   4,    0,   -7 },   // 0x31 '1'
  {    85,   5,   8,   6,    0,   -7 },   // 0x32 '2'
  {    90,   6,   8,   7,    0,   -7 },   // 0x33 '3'
  {    96,   6,   8,   8,    1,   -7 },   // 0x34 '4'
  {   102,   5,   8,   7,    1,   -7 },   // 0x35 '5'
  {   107,   6,   8,   7,    0,   -7 },   // 0x36 '6'
  {   113,   5,   8,   6,    1,   -7 },   // 0x37 '7'
  {   118,   6,   8,   8,    1,   -7 },   // 0x38 '8'
  {   124,   6,   8,   8,    1,   -7 },   // 0x39 '9'
  {   130,   1,   6,   3,    1,   -5 },   // 0x3A ':'
  {   131,   1,   7,   3,    1,   -5 },   // 0x3B ';'
  {   132,   5,   7,   6,    1,   -7 },   // 0x3C '<'
  {   137,   5,   4,   6,    0,   -5 },   // 0x3D '='
  {   140,   5,   7,   6,    0,   -7 },   // 0x3E '>'
  {   145,   5,   8,   6,    0,   -7 },   // 0x3F '?'
  {   150,  10,  10,  12,    1,   -7 },   // 0x40 '@'
  {   163,   7,   8,   8,    0,   -7 },   // 0x41 'A'
  {   170,   6,   8,   8,    1,   -7 },   // 0x42 'B'
  {   176,   7,   8,   8,    1,   -7 },   // 0x43 'C'
  {   183,   7,   8,   9,    1,   -7 },   // 0x44 'D'
  {   190,   5,   8,   7,    1,   -7 },   // 0x45 'E'
  {   195,   5,   8,   7,    1,   -7 },   // 0x46 'F'
  {   200,   7,   8,   9,    1,   -7 },   // 0x47 'G'
  {   207,   6,   8,   8,    1,   -7 },   // 0x48 'H'
  {   213,   1,   8,   3,    1,   -7 },   // 0x49 'I'
  {   214,   5,   8,   7,    1,   -7 },   // 0x4A 'J'
  {   219,   6,   8,   7,    1,   -7 },   // 0x4B 'K'
  {   225,   6,   8,   7,    1,   -7 },   // 0x4C 'L'
  {   231,   8,   8,  10,    1,   -7 },   // 0x4D 'M'
  {   239,   6,   8,   8,    1,   -7 },   // 0x4E 'N'
  {   245,   7,   8,   9,    1,   -7 },   // 0x4F 'O'
  {   252,   6,   8,   8,    1,   -7 },   // 0x50 'P'
  {   258,   8,  11,   9,    1,   -7 },   // 0x51 'Q'
  {   269,   6,   8,   8,    1,   -7 },   // 0x52 'R'
  {   275,   6,   8,   7,    1,   -7 },   // 0x53 'S'
  {   281,   7,   8,   7,    0,   -7 },   // 0x54 'T'
  {   288,   6,   8,   8,    1,   -7 },   // 0x55 'U'
  {   294,   7,   8,   8,    0,   -7 },   // 0x56 'V'
  {   301,  12,   8,  12,    0,   -7 },   // 0x57 'W'
  {   313,   8,   8,   8,    0,   -7 },   // 0x58 'X'
  {   321,   7,   8,   7,    0,   -7 },   // 0x59 'Y'
  {   328,   6,   8,   8,    1,   -7 },   // 0x5A 'Z'
  {   334,   4,  16,   5,    1,  -11 },   // 0x5B '['
  {   342,   6,  15,   7,    0,  -11 },   // 0x5C '\'
  {   354,   4,  16,   5,    0,  -11 },   // 0x5D ']'
  {   362,   5,   4,   6,    0,   -8 },   // 0x5E '^'
  {   365,  11,   1,  11,    0,    1 },   // 0x5F '_'
  {   367,   2,   2,   2,    0,   -7 },   // 0x60 '`'
  {   368,   5,   6,   7,    1,   -5 },   // 0x61 'a'
  {   372,   5,   8,   7,    1,   -7 },   // 0x62 'b'
  {   377,   5,   6,   6,    1,   -5 },   // 0x63 'c'
  {   381,   5,   8,   7,    1,   -7 },   // 0x64 'd'
  {   386,   5,   6,   7,    1,   -5 },   // 0x65 'e'
  {   390,   3,   8,   4,    0,   -7 },   // 0x66 'f'
  {   393,   5,   9,   7,    1,   -5 },   // 0x67 'g'
  {   399,   5,   8,   7,    1,   -7 },   // 0x68 'h'
  {   404,   1,   8,   3,    1,   -7 },   // 0x69 'i'
  {   405,   2,  11,   4,    1,   -7 },   // 0x6A 'j'
  {   408,   5,   8,   6,    1,   -7 },   // 0x6B 'k'
  {   413,   1,   8,   3,    1,   -7 },   // 0x6C 'l'
  {   414,   7,   6,   9,    1,   -5 },   // 0x6D 'm'
  {   420,   5,   6,   7,    1,   -5 },   // 0x6E 'n'
  {   424,   5,   6,   7,    1,   -5 },   // 0x6F 'o'
  {   428,   5,   9,   7,    1,   -5 },   // 0x70 'p'
  {   434,   5,   9,   7,    1,   -5 },   // 0x71 'q'
  {   440,   3,   6,   5,    1,   -5 },   // 0x72 'r'
  {   443,   4,   6,   4,    0,   -5 },   // 0x73 's'
  {   446,   3,   7,   4,    0,   -6 },   // 0x74 't'
  {   449,   5,   6,   7,    1,   -5 },   // 0x75 'u'
  {   453,   6,   6,   6,    0,   -5 },   // 0x76 'v'
  {   458,   9,   6,   9,    0,   -5 },   // 0x77 'w'
  {   465,   6,   6,   6,    0,   -5 },   // 0x78 'x'
  {   470,   5,   9,   7,    1,   -5 },   // 0x79 'y'
  {   476,   5,   6,   6,    1,   -5 },   // 0x7A 'z'
  {   480,   5,  16,   5,    0,  -11 },   // 0x7B '{'
  {   490,   1,  11,   3,    1,   -9 },   // 0x7C '|'
  {   492,   5,  16,   5,    0,  -11 },   // 0x7D '}'
  {   502,   4,   3,   4,    0,   -9 },   // 0x7E '~'
  {     0,   0,   0,   0,    0,    0 },   // 0x7F 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x80 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x81 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x82 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x83 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x84 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x85 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x86 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x87 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x88 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x89 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8A 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8B 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8C 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8D 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8E 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x8F 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x90 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x91 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x92 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x93 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x94 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x95 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x96 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x97 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x98 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x99 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9A 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9B 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9C 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9D 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9E 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0x9F 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0xA0 ' '
  {     0,   0,   0,   0,    0,    0 },   // 0xA1 '¡'
  {     0,   0,   0,   0,    0,    0 },   // 0xA2 '¢'
  {     0,   0,   0,   0,    0,    0 },   // 0xA3 '£'
  {     0,   0,   0,   0,    0,    0 },   // 0xA4 '¤'
  {     0,   0,   0,   0,    0,    0 },   // 0xA5 '¥'
  {     0,   0,   0,   0,    0,    0 },   // 0xA6 '¦'
  {     0,   0,   0,   0,    0,    0 },   // 0xA7 '§'
  {     0,   0,   0,   0,    0,    0 },   // 0xA8 '¨'
  {     0,   0,   0,   0,    0,    0 },   // 0xA9 '©'
  {     0,   0,   0,   0,    0,    0 },   // 0xAA 'ª'
  {     0,   0,   0,   0,    0,    0 },   // 0xAB '«'
  {     0,   0,   0,   0,    0,    0 },   // 0xAC '¬'
  {     0,   0,   0,   0,    0,    0 },   // 0xAD 'non-printable'
  {     0,   0,   0,   0,    0,    0 },   // 0xAE '®'
  {     0,   0,   0,   0,    0,    0 },   // 0xAF '¯'
  {     0,   0,   0,   0,    0,    0 },   // 0xB0 '°'
  {     0,   0,   0,   0,    0,    0 },   // 0xB1 '±'
  {     0,   0,   0,   0,    0,    0 },   // 0xB2 '²'
  {     0,   0,   0,   0,    0,    0 },   // 0xB3 '³'
  {     0,   0,   0,   0,    0,    0 },   // 0xB4 '´'
  {     0,   0,   0,   0,    0,    0 },   // 0xB5 'µ'
  {     0,   0,   0,   0,    0,    0 },   // 0xB6 '¶'
  {     0,   0,   0,   0,    0,    0 },   // 0xB7 '·'
  {     0,   0,   0,   0,    0,    0 },   // 0xB8 '¸'
  {     0,   0,   0,   0,    0,    0 },   // 0xB9 '¹'
  {     0,   0,   0,   0,    0,    0 },   // 0xBA 'º'
  {     0,   0,   0,   0,    0,    0 },   // 0xBB '»'
  {     0,   0,   0,   0,    0,    0 },   // 0xBC '¼'
  {     0,   0,   0,   0,    0,    0 },   // 0xBD '½'
  {     0,   0,   0,   0,    0,    0 },   // 0xBE '¾'
  {     0,   0,   0,   0,    0,    0 },   // 0xBF '¿'
  {     0,   0,   0,   0,    0,    0 },   // 0xC0 'À'
  {     0,   0,   0,   0,    0,    0 },   // 0xC1 'Á'
  {     0,   0,   0,   0,    0,    0 },   // 0xC2 'Â'
  {     0,   0,   0,   0,    0,    0 },   // 0xC3 'Ã'
  {     0,   0,   0,   0,    0,    0 },   // 0xC4 'Ä'
  {     0,   0,   0,   0,    0,    0 },   // 0xC5 'Å'
  {     0,   0,   0,   0,    0,    0 },   // 0xC6 'Æ'
  {     0,   0,   0,   0,    0,    0 },   // 0xC7 'Ç'
  {     0,   0,   0,   0,    0,    0 },   // 0xC8 'È'
  {     0,   0,   0,   0,    0,    0 },   // 0xC9 'É'
  {     0,   0,   0,   0,    0,    0 },   // 0xCA 'Ê'
  {     0,   0,   0,   0,    0,    0 },   // 0xCB 'Ë'
  {     0,   0,   0,   0,    0,    0 },   // 0xCC 'Ì'
  {     0,   0,   0,   0,    0,    0 },   // 0xCD 'Í'
  {     0,   0,   0,   0,    0,    0 },   // 0xCE 'Î'
  {     0,   0,   0,   0,    0,    0 },   // 0xCF 'Ï'
  {     0,   0,   0,   0,    0,    0 },   // 0xD0 'Ð'
  {     0,   0,   0,   0,    0,    0 },   // 0xD1 'Ñ'
  {     0,   0,   0,   0,    0,    0 },   // 0xD2 'Ò'
  {     0,   0,   0,   0,    0,    0 },   // 0xD3 'Ó'
  {     0,   0,   0,   0,    0,    0 },   // 0xD4 'Ô'
  {     0,   0,   0,   0,    0,    0 },   // 0xD5 'Õ'
  {     0,   0,   0,   0,    0,    0 },   // 0xD6 'Ö'
  {     0,   0,   0,   0,    0,    0 },   // 0xD7 '×'
  {     0,   0,   0,   0,    0,    0 },   // 0xD8 'Ø'
  {     0,   0,   0,   0,    0,    0 },   // 0xD9 'Ù'
  {     0,   0,   0,   0,    0,    0 },   // 0xDA 'Ú'
  {     0,   0,   0,   0,    0,    0 },   // 0xDB 'Û'
  {     0,   0,   0,   0,    0,    0 },   // 0xDC 'Ü'
  {     0,   0,   0,   0,    0,    0 },   // 0xDD 'Ý'
  {     0,   0,   0,   0,    0,    0 },   // 0xDE 'Þ'
  {     0,   0,   0,   0,    0,    0 },   // 0xDF 'ß'
  {     0,   0,   0,   0,    0,    0 },   // 0xE0 'à'
  {     0,   0,   0,   0,    0,    0 },   // 0xE1 'á'
  {     0,   0,   0,   0,    0,    0 },   // 0xE2 'â'
  {     0,   0,   0,   0,    0,    0 },   // 0xE3 'ã'
  {     0,   0,   0,   0,    0,    0 },   // 0xE4 'ä'
  {     0,   0,   0,   0,    0,    0 },   // 0xE5 'å'
  {     0,   0,   0,   0,    0,    0 },   // 0xE6 'æ'
  {     0,   0,   0,   0,    0,    0 },   // 0xE7 'ç'
  {     0,   0,   0,   0,    0,    0 },   // 0xE8 'è'
  {     0,   0,   0,   0,    0,    0 },   // 0xE9 'é'
  {     0,   0,   0,   0,    0,    0 },   // 0xEA 'ê'
  {     0,   0,   0,   0,    0,    0 },   // 0xEB 'ë'
  {     0,   0,   0,   0,    0,    0 },   // 0xEC 'ì'
  {     0,   0,   0,   0,    0,    0 },   // 0xED 'í'
  {     0,   0,   0,   0,    0,    0 },   // 0xEE 'î'
  {     0,   0,   0,   0,    0,    0 },   // 0xEF 'ï'
  {     0,   0,   0,   0,    0,    0 },   // 0xF0 'ð'
  {     0,   0,   0,   0,    0,    0 },   // 0xF1 'ñ'
  {     0,   0,   0,   0,    0,    0 },   // 0xF2 'ò'
  {     0,   0,   0,   0,    0,    0 },   // 0xF3 'ó'
  {     0,   0,   0,   0,    0,    0 },   // 0xF4 'ô'
  {     0,   0,   0,   0,    0,    0 },   // 0xF5 'õ'
  {     0,   0,   0,   0,    0,    0 },   // 0xF6 'ö'
  {   504,   4,   3,   4,    0,   -8 }    // 0xF7 '÷'
};

const GFXfont Pragma_Sans36pt7b PROGMEM = {
  (uint8_t  *)Pragma_Sans36pt7bBitmaps,
  (GFXglyph *)Pragma_Sans36pt7bGlyphs, 0x20, 0xF7,   13 };

// Approx. 1175 bytes
