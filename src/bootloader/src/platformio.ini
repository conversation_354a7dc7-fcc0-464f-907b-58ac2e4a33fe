[platformio]
src_dir = Src
lib_dir = Drivers

[env]
#platform = ststm32 @8.1.0
platform = ststm32 @9.0.0
framework = stm32cube
build_unflags = -nostartfiles -nostdlib
board_build.ldscript = linker/stm32.ld
upload_protocol = custom
extra_scripts =
	python/upload_stlink.py
debug_build_flags = -O0 -ggdb3 -g3
debug_tool = stlink
monitor_speed = 420000

[generic]
VERSION = -D BOOTLOADER_VERSION=0.5.4
flags_hal =
    ${generic.VERSION}
    -Wl,-Map,"'${BUILD_DIR}/firmware.map'"
flags =
    ${generic.flags_hal}
    -D USE_FULL_LL_DRIVER=1

# ========================

[r9m_generic]
flags =
    -D MCU_TYPE=R9M
    -D PIN_LED_RED="A,11"
    -D PIN_LED_GREEN="A,12"
    -D DUPLEX_PIN="A,5"
    # S.Port = UART3
    -D UART_RX_PIN="B,11"
    -D UART_TX_PIN="B,10"
    -D UART_BAUD=57600
    # ESPbackpack = UART1
    -D UART_RX_PIN_2ND="A,10"
    -D UART_TX_PIN_2ND="A,9"
    -D UART_BAUD_2ND=115200
    -D STK500=1
    -Wl,--defsym=RAM_SIZE=20K
    -Wl,--defsym=FLASH_SIZE=8K
    -D FLASH_APP_OFFSET=0x4000u
    ${generic.flags}

[env:R9M]
board = bluepill_f103c8_128k
board_build.mcu = stm32f103c8t6
board_build.f_cpu = 72000000L
board_upload.maximum_size = 8192
build_flags =
    ${r9m_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x0

[env:R9M_stock]
extends = env:R9M
build_flags =
    ${r9m_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x2000

# ========================

[r9m_lite_generic]
flags =
    -D MCU_TYPE=R9M_LITE
    -D PIN_LED_RED="A,1"
    -D PIN_LED_GREEN="A,4"
    -D DUPLEX_PIN="A,5"
    # S.Port = UART3
    -D UART_RX_PIN="B,11"
    -D UART_TX_PIN="B,10"
    -D UART_BAUD=57600
    -D STK500=1
    -Wl,--defsym=RAM_SIZE=20K
    -Wl,--defsym=FLASH_SIZE=8K
    -D FLASH_APP_OFFSET=0x4000u
    ${generic.flags}

[IGNORE_env:R9M_LITE]
extends = env:R9M
build_flags =
    ${r9m_lite_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x0

[IGNORE_env:R9M_LITE_stock]
extends = env:R9M_LITE
build_flags =
    ${r9m_lite_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x2000

# ========================

[r9m_lite_pro_generic]
flags =
    -D MCU_TYPE=R9M_LITE_PRO
    # S.Port = UART3
    -D UART_RX_PIN="B,11"
    -D UART_TX_PIN="B,10"
    -D UART_BAUD=57600
    -D DUPLEX_PIN="B,2"
    -D DUPLEX_INVERTED=1
    -D PIN_LED_RED="B,3"
    -D PIN_LED_GREEN="A,15"
    -D STK500=1
    -Wl,--defsym=RAM_SIZE=40K
    -D FLASH_APP_OFFSET=0x8000u
    -Wl,--defsym=FLASH_SIZE=16K
    ${generic.flags}

[env:R9M_LITE_PRO]
board = stm32f303
board_upload.maximum_size = 16384
build_flags =
    ${r9m_lite_pro_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x0

[IGNORE_env:R9M_LITE_PRO_stock]
extends = env:R9M_LITE_PRO
build_flags =
    ${r9m_lite_pro_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x2000

# ========================

[r9mm_generic]
flags =
    -D MCU_TYPE=R9MM
    -D PIN_LED_RED="C,1"
    -D PIN_LED_GREEN="B,3"
    # USART1
    -D UART_RX_PIN="A,10"
    -D UART_TX_PIN="A,9"
    -D HSI_VALUE=8000000
    -Wl,--defsym=RAM_SIZE=20K
    ${generic.flags}

[env:R9MM]
board = genericSTM32F103RB
board_build.mcu = stm32f103rbt6
board_build.f_cpu = 72000000L
board_upload.maximum_size = 32768
build_flags =
    ${r9mm_generic.flags}
    -D PIN_BUTTON="C,13"
    -Wl,--defsym=FLASH_OFFSET=0x0
    -Wl,--defsym=FLASH_SIZE=32K
    -D FLASH_APP_OFFSET=0x8000u

[env:R9MM_stock]
board = ${env:R9MM.board}
board_build.mcu = ${env:R9MM.board_build.mcu}
board_build.f_cpu = ${env:R9MM.board_build.f_cpu}
board_build.ldscript = ${env:R9MM.board_build.ldscript}
board_upload.maximum_size = 8192
build_flags =
    ${r9mm_generic.flags}
    -D PIN_BUTTON="C,13"
    -Wl,--defsym=FLASH_OFFSET=0x2000
    -Wl,--defsym=FLASH_SIZE=8K
    -D FLASH_APP_OFFSET=0x8000u
    # S.Port = UART3
    #-D UART_RX_PIN_2ND="B,8"
    #-D UART_TX_PIN_2ND="B,9"
    #-D UART_BAUD_2ND=57600
    #-D DUPLEX_PIN="A,5"

[env:R9MM_no_btn]
board = ${env:R9MM.board}
board_build.mcu = ${env:R9MM.board_build.mcu}
board_build.f_cpu = ${env:R9MM.board_build.f_cpu}
board_build.ldscript = ${env:R9MM.board_build.ldscript}
board_upload.maximum_size = 16384
build_flags =
    ${r9mm_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x2000
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x8000u

[env:R9MM_no_btn_stock]
board = ${env:R9MM.board}
board_build.mcu = ${env:R9MM.board_build.mcu}
board_build.f_cpu = ${env:R9MM.board_build.f_cpu}
board_build.ldscript = ${env:R9MM.board_build.ldscript}
board_upload.maximum_size = 16384
build_flags =
    ${r9mm_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x2000
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x8000u

# ========================

[r9mx_generic]
flags =
    -D MCU_TYPE=R9MX
    -D STM32L4xx=1
    -D PIN_LED_RED="B,2"
    -D PIN_LED_GREEN="B,3"
    # USART1
    -D UART_RX_PIN="A,10"
    -D UART_TX_PIN="A,9"
    -D HSI_VALUE=16000000
    -Wl,--defsym=RAM_SIZE=64K
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x8000u
    ${generic.flags}
    #${generic.flags_hal}

[env:R9MX]
board = ${env:R9MX_no_btn.board}
board_build.mcu = ${env:R9MX_no_btn.board_build.mcu}
board_upload.maximum_size = ${env:R9MX_no_btn.board_upload.maximum_size}
build_flags =
    ${env:R9MX_no_btn.build_flags}
    -D PIN_BUTTON="B,0"

[env:R9MX_stock]
board = ${env:R9MX_no_btn_stock.board}
board_build.mcu = ${env:R9MX_no_btn_stock.board_build.mcu}
board_upload.maximum_size = ${env:R9MX_no_btn_stock.board_upload.maximum_size}
build_flags =
    ${env:R9MX_no_btn_stock.build_flags}
    -D PIN_BUTTON="B,0"

[env:R9MX_no_btn]
board = nucleo_l433rc_p
board_build.mcu = stm32l433cby6
board_upload.maximum_size = 16384
build_flags =
    ${r9mx_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x0

[env:R9MX_no_btn_stock]
board = ${env:R9MX_no_btn.board}
board_build.mcu = ${env:R9MX_no_btn.board_build.mcu}
board_upload.maximum_size = ${env:R9MX_no_btn.board_upload.maximum_size}
build_flags =
    ${r9mx_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x2000

# ========================

[r9slim_generic]
flags =
    -D MCU_TYPE=R9SLIM
    -D PIN_LED_RED="A,11"
    -D PIN_LED_GREEN="A,12"
    -D UART_RX_PIN="A,3"
    -D UART_TX_PIN="A,2"
    -D HSI_VALUE=8000000
    -Wl,--defsym=RAM_SIZE=20K
    -Wl,--defsym=FLASH_SIZE=16K
    ${generic.flags}
[env:R9SLIM]
board = stm32f103c8
build_flags =
    ${r9slim_generic.flags}
    -D PIN_BUTTON="C,13"
    -Wl,--defsym=FLASH_OFFSET=0x0
    -D FLASH_APP_OFFSET=0x8000u

[env:R9SLIM_stock]
board = ${env:R9SLIM.board}
board_build.ldscript = ${env:R9SLIM.board_build.ldscript}
board_upload.maximum_size = 8192
build_flags =
    ${r9slim_generic.flags}
    -D PIN_BUTTON="C,13"
    -Wl,--defsym=FLASH_OFFSET=0x2000
    -D FLASH_APP_OFFSET=0x8000u

[env:R9SLIM_no_btn]
board = ${env:R9SLIM.board}
board_build.ldscript = ${env:R9SLIM.board_build.ldscript}
board_upload.maximum_size = 16384
build_flags =
    ${r9slim_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x0
    -D FLASH_APP_OFFSET=0x8000u

[env:R9SLIM_no_btn_stock]
board = ${env:R9SLIM.board}
board_build.ldscript = ${env:R9SLIM.board_build.ldscript}
board_upload.maximum_size = 16384
build_flags =
    ${r9slim_generic.flags}
    -Wl,--defsym=FLASH_OFFSET=0x2000
    -D FLASH_APP_OFFSET=0x8000u

# ========================

[slim_plus_generic]
button =
    -D PIN_BUTTON="C,13"
flags =
    -D MCU_TYPE=R9SLIM_PLUS
    # USART3 RX
    -D UART_RX_PIN="B,11"
    # USART1 TX
    -D UART_TX_PIN="A,9"
slim_plus =
    -D PIN_LED_RED="A,11"
    -D PIN_LED_GREEN="A,12"
    -D HSI_VALUE=8000000
    -D FLASH_APP_OFFSET=0x8000u
    -Wl,--defsym=FLASH_SIZE=16K
    ${generic.flags}

[env:R9SLIM_PLUS]
extends = env:R9SLIM_PLUS_no_btn
build_flags =
    ${env:R9SLIM_PLUS_no_btn.build_flags}
    ${slim_plus_generic.button}

[env:R9SLIM_PLUS_stock]
extends = env:R9SLIM_PLUS_no_btn
build_flags =
    ${env:R9SLIM_PLUS_no_btn_stock.build_flags}
    ${slim_plus_generic.button}

[env:R9SLIM_PLUS_no_btn]
board = stm32f103c8
build_flags =
    ${slim_plus_generic.flags}
    ${slim_plus_generic.slim_plus}
    -Wl,--defsym=FLASH_OFFSET=0x0
    -Wl,--defsym=RAM_SIZE=20K

[env:R9SLIM_PLUS_no_btn_stock]
extends = env:R9SLIM_PLUS_no_btn
build_flags =
    ${slim_plus_generic.flags}
    ${slim_plus_generic.slim_plus}
    -Wl,--defsym=FLASH_OFFSET=0x2000
    -Wl,--defsym=RAM_SIZE=20K

# ========================

[env:R9SLIM_PLUS_OTA]
extends = env:R9SLIM_PLUS_OTA_no_btn
build_flags =
    ${env:R9SLIM_PLUS_OTA_no_btn.build_flags}
    ${slim_plus_generic.button}

[env:R9SLIM_PLUS_OTA_stock]
extends = env:R9SLIM_PLUS_OTA_no_btn
build_flags =
    ${env:R9SLIM_PLUS_OTA_no_btn_stock.build_flags}
    ${slim_plus_generic.button}

[env:R9SLIM_PLUS_OTA_no_btn]
board = stm32f303
board_upload.maximum_size = 16384
build_flags =
    ${slim_plus_generic.flags}
    ${slim_plus_generic.slim_plus}
    -Wl,--defsym=FLASH_OFFSET=0x0
    -Wl,--defsym=RAM_SIZE=40K

[env:R9SLIM_PLUS_OTA_no_btn_stock]
extends = env:R9SLIM_PLUS_OTA_no_btn
build_flags =
    ${slim_plus_generic.flags}
    ${slim_plus_generic.slim_plus}
    -Wl,--defsym=FLASH_OFFSET=0x2000
    -Wl,--defsym=RAM_SIZE=40K

# ========================

[jumper_generic]
flags =
    ${slim_plus_generic.slim_plus}
    -D MCU_TYPE=JUMPER_R900
    -Wl,--defsym=FLASH_OFFSET=0
    -Wl,--defsym=RAM_SIZE=20K
button =
    -D PIN_BUTTON="C,13"
    #-D BUTTON_INVERTED=0
    #-D BUTTON_FLOATING=1

[env:Jumper_R900_no_btn]
extends = env:R9SLIM_PLUS
build_flags =
    ${jumper_generic.flags}
    # USART2
    -D UART_RX_PIN="A,3"
    -D UART_TX_PIN="A,2"

[env:Jumper_R900]
extends = env:Jumper_R900_no_btn
build_flags =
    ${env:Jumper_R900_no_btn.build_flags}
    ${jumper_generic.button}

[env:Jumper_R900_SPORT]
extends = env:Jumper_R900_no_btn
build_flags =
    ${jumper_generic.flags}
    # USART1 TX, half duplex
    -D UART_TX_PIN="A,9"

[env:Jumper_R900_SPORT_no_btn]
extends = env:Jumper_R900_SPORT
build_flags =
    ${env:Jumper_R900_SPORT.build_flags}
    ${jumper_generic.button}

# ========================

[env:GHOST_TX]
board = stm32f303
board_upload.maximum_size = 16384
build_flags =
    -D MCU_TYPE=GHOST_TX
    -D WS2812_LED_PIN="B,6"
    -D STK500=1
    -D UART_TX_PIN="A,10"
    -D UART_BAUD=57600
    -D UART_INV=1
    -D HSI_VALUE=8000000
    -Wl,--defsym=RAM_SIZE=40K
    -Wl,--defsym=FLASH_OFFSET=0x0
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x4000u
    ${generic.flags}

[env:GHOST_ATTO]
# Version 1.2
board = stm32f301
board_upload.maximum_size = 16384
build_flags =
    -D MCU_TYPE=GHOST_ATTO_v1_2
    -D WS2812_LED_PIN="A,7"
    -D PIN_BUTTON="A,12"
    # USART1 TX, AFIO=1
    -D UART_RX_PIN="B,6"
    # USART2 TX
    -D UART_TX_PIN="A,2"
    -D HSI_VALUE=8000000
    -Wl,--defsym=RAM_SIZE=16K
    -Wl,--defsym=FLASH_OFFSET=0x0
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x4000u
    ${generic.flags}

# ========================

[IGNORE_env:RHF76_052]
board = rhf76_052
board_build.mcu = stm32l051c8t6
board_build.f_cpu = 32000000L
board_upload.maximum_size = 16384
build_flags =
    -D MCU_TYPE=RHF76_052
    -D PIN_LED_RED="B,4"
    # USART1, AFIO=1
    -D UART_RX_PIN="B,7"
    -D UART_TX_PIN="B,6"
    -Wl,--defsym=RAM_SIZE=8K
    -Wl,--defsym=FLASH_OFFSET=0x0
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x4000u
    -D HSE_VALUE=12000000U
    ${generic.flags}

# ========================

[IGNORE_env:RAK4200]
board = rak4200
board_upload.maximum_size = 16384
build_flags =
    -D MCU_TYPE=RAK4200
    -D PIN_LED_RED="A,12"
    # USART1
    -D UART_RX_PIN="A,10"
    -D UART_TX_PIN="A,9"
    -D HSI_VALUE=16000000
    -Wl,--defsym=RAM_SIZE=20K
    -Wl,--defsym=FLASH_OFFSET=0x0
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x4000u
    ${generic.flags}

# ========================

[IGNORE_env:RAK811]
board = rak811_tracker
board_build.mcu = stm32l151rbt6
board_build.f_cpu = 32000000L
board_upload.maximum_size = 16384
build_flags =
    -D MCU_TYPE=RAK811
    -D STM32L1xx
    #-D PIN_LED_RED=","
    # USART1, AFIO=1
    -D UART_RX_PIN="B,7"
    -D UART_TX_PIN="B,6"
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x4000u
    ${generic.flags}

# ========================

[env:SX1280_RX_2020_PCB_v0.2]
board = stm32f103c8
build_flags =
    -D MCU_TYPE=SX1280_RX_2020_v0_2
    -D PIN_LED_RED="B,15"
    # USART1
    -D UART_RX_PIN="A,10"
    -D UART_TX_PIN="A,9"
    -Wl,--defsym=RAM_SIZE=20K
    -Wl,--defsym=FLASH_OFFSET=0x0
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x4000u
    ${generic.flags}

# ========================

[env:SX1280_RX_Nano_PCB_v0.5]
board = nucleo_l432kc
board_build.mcu = stm32l432kbu6
board_build.f_cpu = 80000000L
board_upload.maximum_size = 16384
build_flags =
    -D MCU_TYPE=SX1280_RX_CCG_NANO_v0_5
    -D STM32L4xx=1
    -D PIN_BUTTON="A,3"
    -D PIN_LED_RED="B,5"
    # USART1, AFIO=1
    -D UART_RX_PIN="B,7"
    -D UART_TX_PIN="B,6"
    -D HSI_VALUE=16000000
    -Wl,--defsym=RAM_SIZE=48K
    -Wl,--defsym=FLASH_OFFSET=0x0
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x4000u
    ${generic.flags}

# ========================
# NamimnoRC HW bootloaders

[env:namimnorc_tx]
board = stm32f103c8
board_upload.maximum_size = 8192
build_flags =
    -D MCU_TYPE=VOYAGER_900_TX
    -D STK500=1
    -D WS2812_LED_PIN="B,0"
    # S.Port = UART3
    -D UART_RX_PIN="B,11"
    -D UART_TX_PIN="B,10"
    -D UART_BAUD=57600
    -D DUPLEX_PIN="A,1"
    -D DUPLEX_INVERTED=1
    # ESPbackpack = UART1
    -D UART_TX_PIN_2ND="A,9"
    -D UART_RX_PIN_2ND="A,10"
    -D UART_BAUD_2ND=115200
    -D HSI_VALUE=8000000
    -Wl,--defsym=RAM_SIZE=20K
    -Wl,--defsym=FLASH_OFFSET=0
    -Wl,--defsym=FLASH_SIZE=8K
    -D FLASH_APP_OFFSET=0x4000u
    ${generic.flags}

[env:voyager_900]
board = stm32f103c8
build_flags =
    -D MCU_TYPE=VOYAGER_900
    -D PIN_LED_RED="A,11"
    -D UART_RX_PIN="A,10"
    -D UART_TX_PIN="A,9"
    -D HSI_VALUE=8000000
    -Wl,--defsym=RAM_SIZE=20K
    -Wl,--defsym=FLASH_OFFSET=0
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x8000u
    ${generic.flags}

[env:flash_2400]
board = stm32f103c8
build_flags =
    -D MCU_TYPE=FLASH_2400
    -D PIN_LED_RED="A,1"
    -D UART_RX_PIN="A,10"
    -D UART_TX_PIN="A,9"
    -D HSI_VALUE=8000000
    -Wl,--defsym=RAM_SIZE=20K
    -Wl,--defsym=FLASH_OFFSET=0
    -Wl,--defsym=FLASH_SIZE=16K
    -D FLASH_APP_OFFSET=0x8000u
    ${generic.flags}

# ========================

# The bootloader used on the FM30 is a DFU bootloader (modded for 16MHz/button/strings)
# https://github.com/davidgfnet/stm32-dfu-bootloader
;[env:FM30]
;board = bluepill_f103c8
;board_upload.maximum_size = 8192
;build_flags =
;    -D MCU_TYPE=FM30
;    -D PIN_LED_RED="B,2"
;    -D LED_RED_INVERTED=1
;    -D PIN_LED_GREEN="A,7"
;    -D LED_GREEN_INVERTED=1
;    -D PIN_BUTTON="B,0"
;    # S.Port = UART1
;    -D STK500=1
;    -D UART_RX_PIN="A,10"
;    -D UART_TX_PIN="A,9"
;    -D DUPLEX_PIN="B,7"
;    #-D DUPLEX_INVERTED=1
;    #-D UART_INV=1
;    -D UART_BAUD=57600
;    -D FLASH_APP_OFFSET=0x2000u
;    -Wl,--defsym=RAM_SIZE=20K
;    -Wl,--defsym=FLASH_SIZE=8K
;    -Wl,--defsym=FLASH_OFFSET=0x0
;    ${generic.flags}

[fm30_mini_common]
board = eval_f373vc
board_upload.maximum_size = 16384
flags =
    -D STM32F3xx # standard board defines STM32F3 only
    -D MCU_TYPE=FM30_MINI
    -D PIN_LED_RED="B,6"
    -D LED_RED_INVERTED=1
    -D PIN_LED_GREEN="B,7"
    -D LED_GREEN_INVERTED=1
    -D FLASH_APP_OFFSET=0x4000u
    -Wl,--defsym=RAM_SIZE=32K
    -Wl,--defsym=FLASH_SIZE=16K
    -Wl,--defsym=FLASH_OFFSET=0x0
    ${generic.flags}

[env:FM30_MINI]
extends = fm30_mini_common
build_flags = ${fm30_mini_common.flags}
    # XMODEM (Betaflight Passthrough)= UART2
    -D XMODEM=1
    -D UART_RX_PIN="A,3"
    -D UART_TX_PIN="A,2"
    -D UART_BAUD=420000

[env:FM30_MINI_RXTX]
# RX as TX, only difference is the protocol is for OpenTX flashing, not BF passthrough
extends = fm30_mini_common
build_flags = ${fm30_mini_common.flags}
    # STK500 (OpenTX)= UART2
    -D STK500=1
    -D UART_TX_PIN="A,2"
    -D UART_BAUD=57600
    -D UART_INV=1

# ========================

[env:BLUEPILL]
board = bluepill_f103c8
board_build.mcu = stm32f103c8t6
board_build.f_cpu = 72000000L
board_upload.maximum_size = 16384
build_flags =
    ${generic.flags}
    -D MCU_TYPE=BLUEPILL_DEV
    -D PIN_LED_GREEN="C,13"
    -D LED_GREEN_INVERTED=1
    -D UART_RX_PIN="A,10"
    -D UART_TX_PIN="A,9"
    -D UART_BAUD=57600
    -D FLASH_APP_OFFSET=0x8000u
    -Wl,--defsym=RAM_SIZE=20K
    -Wl,--defsym=FLASH_SIZE=16K
    -Wl,--defsym=FLASH_OFFSET=0x0

[env:SX1272_RX_PCB]
board = bluepill_f103c8
board_build.mcu = stm32f103c8t6
board_build.f_cpu = 72000000L
board_upload.maximum_size = 16384
build_flags =
    ${generic.flags}
    -D MCU_TYPE=BLUEPILL_DEV
    -D PIN_LED_GREEN="A,10"
    -D UART_RX_PIN="B,7"
    -D UART_TX_PIN="B,6"
    -D UART_BAUD=420000
    -D FLASH_APP_OFFSET=0x4000u
    -Wl,--defsym=RAM_SIZE=20K
    -Wl,--defsym=FLASH_SIZE=16K
    -Wl,--defsym=FLASH_OFFSET=0x0

# ========================