{"build": {"core": "stm32", "cpu": "cortex-m4", "extra_flags": "-DSTM32F302xC=1 -DSTM32F3xx=1 -DSTM32F302=1", "f_cpu": "72000000L", "mcu": "stm32f302cct6", "product_line": "STM32F302xC", "variant": "STM32F302CC"}, "connectivity": ["can"], "debug": {"default_tools": ["stlink"], "jlink_device": "STM32F302CC", "openocd_target": "stm32f3x", "svd_path": "STM32F30x.svd"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "cmsis", "mbed", "stm32cube"], "name": "STM32F302CC", "upload": {"maximum_ram_size": 40960, "maximum_size": 262144, "protocol": "stlink", "protocols": ["jlink", "stlink", "blackmagic", "mbed"]}, "url": "https://www.st.com/en/microcontrollers-microprocessors/stm32f302cc.html", "vendor": "ST"}