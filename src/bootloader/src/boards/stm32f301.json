{"build": {"cpu": "cortex-m4", "extra_flags": "-DSTM32F301x8=1 -DSTM32F3xx=1 -DSTM32F301=1", "f_cpu": "72000000L", "mcu": "stm32f301k8", "product_line": "STM32F301x8", "variant": "STM32F301K8"}, "connectivity": ["can"], "debug": {"default_tools": ["stlink"], "jlink_device": "STM32F301K8", "onboard_tools": ["stlink"], "openocd_board": "st_nucleo_f3", "svd_path": "STM32F30x.svd"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "cmsis", "mbed", "stm32cube"], "name": "STM32F301K8", "upload": {"maximum_ram_size": 16384, "maximum_size": 65536, "protocol": "stlink", "protocols": ["jlink", "stlink", "blackmagic", "mbed"]}, "url": "https://www.st.com/content/st_com/en/products/microcontrollers-microprocessors/stm32-32-bit-arm-cortex-mcus/stm32-mainstream-mcus/stm32f3-series/stm32f301/stm32f301k8.html", "vendor": "ST"}